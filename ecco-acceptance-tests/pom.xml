<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ecco-acceptance-tests</artifactId>
    <packaging>jar</packaging>
    <name>ecco-acceptance-tests</name>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <!-- define some properties by default - could be overridden by command line -D<name>=<value> or by specifying the <name>=<value> in src/test/resource property files -->
    <!-- we could move these to a <filter> file if necessary -->
    <!-- see http://maven.apache.org/plugins/maven-resources-plugin/examples/filter.html -->
    <properties>
        <!-- <env>dev</env> -->
        <selenium.version>3.141.59</selenium.version>
        <httpPort>8082</httpPort>
        <target.host.url>http://localhost:${httpPort}/ecco-war</target.host.url>
    </properties>

    <build>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.codehaus.cargo</groupId>
                <artifactId>cargo-maven2-plugin</artifactId>
                <configuration>
                    <!-- The rest is set in pluginManagement in parent/pom.xml -->
                    <configuration>
                        <properties>
                            <cargo.servlet.port>${httpPort}</cargo.servlet.port>
                            <cargo.logging>low</cargo.logging>
                            <cargo.start.jvmargs>
                                -Djava.locale.providers=JRE,SPI
                                -Duser.timezone=UTC
                                -Dcookie.insecure=true
                                -Dazure.activedirectory.client-id=asdf
                                -Dazure.activedirectory.client-secret=asdf
                            </cargo.start.jvmargs>
                        </properties>
                    </configuration>
                    <deployables>
                        <deployable>
                            <groupId>${project.groupId}</groupId>
                            <artifactId>ecco-war</artifactId>
                            <type>war</type>
                            <properties>
                                <context>/ecco-war</context>
                            </properties>
                        </deployable>
                    </deployables>
                </configuration>
            </plugin>
            <plugin>
                <!-- disable by default because the configuration is more involved -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- running install causes ecco-acceptance-tests to run regardless of 'fast' -->
    <!-- so we would like to activate the profiles of ui tests when 'not fast' -->
    <!-- however, we also need to specify other arguments along with !fast - and maven unforunately doesn't do AND logic - it does OR - http://jira.codehaus.org/browse/MNG-4516 -->
    <!-- since profile activators are determined at a high level (before pom analysis or filtering), we can't set a property in a profile to be picked up here -->
    <!-- nor can we activate a profile by profile name - which would have allowed us to exlude the whole project from the build until fast is absent -->
    <!-- the alternative is to wait for the issue to be fixed, and for each fast install - break when the test run -->
    <!-- for now we prefer to drop the ecco-acceptance-tests profile activation unless specifically requested -->
    <!-- in particular we change self-host activation from !deploy.server to deploy.server=local and !tests to tests=all -->
    <!-- which forces us to remember to provide -Ddeploy.server=local and -Dtestui=all to perform ui tests -->
    <profiles>
        <profile>
              <id>self-host</id>
              <activation>
                  <!-- default behaviour is to self host - override by specifying deploy.server=hostname/url -->
                  <!-- does this property get picked up by firefox/selenium? otherwise we would be best to replace deploy.server here with target.host.url? -->
                  <!-- <property><name>!deploy.server</name></property> -->
                  <!-- we change this based on the above logic -->
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          <property><name>deploy.server</name><value>local</value></property>
              </activation>
            <properties>
                <!-- if we are localhost - then override any filtered var -->
                <target.host.url>http://localhost:${httpPort}/ecco-war</target.host.url>
            </properties>
            <build>
                <plugins>
                <plugin>
                    <groupId>org.codehaus.cargo</groupId>
                    <artifactId>cargo-maven2-plugin</artifactId>
                    <configuration>
                    </configuration>
                    <executions>
                        <execution>
                            <id>start-tomcat</id>
                            <phase>pre-integration-test</phase>
                            <goals>
                                <goal>start</goal>
                            </goals>
                            <configuration>
                                <configuration>
                                    <type>standalone</type>
                                    <home>
                                        ${project.build.directory}/apache-tomcat-${cargo.tomcat.version}
                                    </home>
                                    <properties>
                                        <cargo.servlet.port>${httpPort}</cargo.servlet.port>
                                        <!--<cargo.logging>high</cargo.logging>-->
                                        <env>dev</env>
                                        <db.extraContexts>acceptanceTests</db.extraContexts>
                                        <!-- Consider -Decco.authn.acl=true-->
                                        <cargo.start.jvmargs>
                                            -Djava.locale.providers=JRE,SPI
                                            -Duser.timezone=UTC
                                            -Dcookie.insecure=true
                                            -Dazure.activedirectory.client-id=asdf
                                            -Dazure.activedirectory.client-secret=asdf
                                        </cargo.start.jvmargs>
                                    </properties>
                                </configuration>
                            </configuration>
                        </execution>
                        <execution>
                            <id>stop-tomcat</id>
                            <phase>post-integration-test</phase>
                            <goals>
                                <goal>stop</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>enable-api-acceptance-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>api-acceptance-tests</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <skipTests>false</skipTests>
                                    <includes>
                                        <include>**/api/**/*Tests.java</include>
                                    </includes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <!-- run yarn e2e via maven-frontend-plugin-->
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>1.7.5</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>install-node-and-yarn</goal>
                                </goals>
                                <!-- optional: default phase is "generate-resources"-->
                                <phase>generate-resources</phase>
                            </execution>
                            <execution>
                                <id>yarn e2e</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>yarn</goal>
                                </goals>
                                <configuration>
                                    <arguments>e2e</arguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <nodeVersion>v18.20.8</nodeVersion>
                            <yarnVersion>v1.22.22</yarnVersion>
                            <installDirectory>../target/</installDirectory>
                            <workingDirectory>${project.basedir}</workingDirectory>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>enable-smoke-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>smoke-tests</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <skipTests>false</skipTests>
                                    <includes>
                                        <include>**/smoke/**/*Tests.java</include>
                                    </includes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>enable-performance-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>performance-tests</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <skipTests>false</skipTests>
                                    <includes>
                                        <include>**/performance/**/*Tests.java</include>
                                    </includes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>enable-other-acceptance-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>other-acceptance-tests</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <skipTests>false</skipTests>
                                    <includes>
                                        <include>**/acceptancetests/*Tests.java</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/api/**/*Tests.java</exclude>
                                        <exclude>**/smoke/**/*Tests.java</exclude>
                                        <exclude>**/performance/**/*Tests.java</exclude>
                                    </excludes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-data-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-rota</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>test-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-war</artifactId>
            <version>1.0.0.CI-SNAPSHOT</version>
            <type>war</type>
        </dependency>

        <dependency>
            <groupId>args4j</groupId>
            <artifactId>args4j</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>${selenium.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>selenium-android-driver</artifactId>
                    <groupId>org.seleniumhq.selenium</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>selenium-iphone-driver</artifactId>
                    <groupId>org.seleniumhq.selenium</groupId>
                </exclusion>
                <exclusion>
                    <!-- we exclude this one too as we use an older artifact that has this when running on Bamboo -->
                    <artifactId>selenium-htmlunit-driver</artifactId>
                    <groupId>org.seleniumhq.selenium</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>htmlunit-driver</artifactId>
                    <groupId>org.seleniumhq.selenium</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-leg-rc</artifactId>
            <version>${selenium.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-remote-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-server</artifactId>
                <version>${selenium.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
