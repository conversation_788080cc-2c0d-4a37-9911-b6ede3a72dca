package com.ecco.calendar.core;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.ProxyDtoBuilderProxy;

/**
 * The details required for a calendar user.
 */
public interface CalendarOwnerDefinition extends BuildableDto<CalendarOwnerDefinition> {
    String getFirstName();
    String getLastName();
    String getEmail();
    String getUsername();
    String getPassword();

    interface Builder extends DtoBuilder<CalendarOwnerDefinition> {
        Builder firstName(String firstName);
        Builder lastName(String lastName);
        Builder email(String email);
        Builder username(String username);
        Builder password(String password);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, CalendarOwnerDefinition.class);
        }
    }
}
