package com.ecco.calendar.core.webapi;

import com.ecco.calendar.core.RecurringEntry;
import com.ecco.calendar.dom.EventType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDateTime;
import org.springframework.hateoas.RepresentationModel;

import java.net.URI;
import java.util.List;
import java.util.UUID;

/**
 * View model, designed for JSON serialization. Based on the (now redundant) CustomCalEvent class.
 * Uses hateos RepresentationModel.
 * Used as dto's from EventController and ReferralController.getReferralAndEventsToViewModel.
 */
@SuppressWarnings({"unused", "WeakerAccess"})
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class EventResource extends RepresentationModel<EventResource> { // TODO implements CalendarableEventDto {

    @JsonProperty("uid")
    String entryId;

    /**
     * Whether this event is a recurrence, so is part of a recurring series.
     * NB This is not true for the parent recurring entry itself (this checks for ':' - see CosmoCalendarService.isRecurring).
     * A parent recurring entry can be determined through recurringEntry below.
     */
    boolean recurrence;

    /**
     * Indication that this event is itself setting the schedule for recurrences.
     * @see RecurringEntry
     * Determined using BaseEventStamp#isRecurring which looks for RRULE's - and we exclude recurrences
     */
    boolean recurringEntry;

    /**
     * Added for debugging purposes to see which rota schedules need extending.
     * Having this information via events (not 'addDemandWithStatuses') is because we only want confirmed
     * events - which are therefore on the calendar.
     * e.g. X-ECCO-UPDATED-BY:entity://AppointmentRecurringActionCommand/{uuid}
     */
    URI updatedByUri;

    // String eventId; intended as a reference to ecco's eventId?

    /**
     * The service recipient this event is about - if any.
     * This is simply a convenience to create a change entry command - which keeps audits unified.
     * NB This is not the same as the author above - a user should be able to create an event on another's calendar.
     */
    Integer serviceRecipientId;
    String serviceRecipientPrefix;
    Integer serviceAllocationId;

    /**
     * The contact this event is about - if any.
     * This is simply a convenience to create a change entry command - which keeps audits unified.
     * NB This is not the same as the author - a user should be able to create an event on another's calendar.
     */
    Long contactId;

    /**
     * Unique reference to the native object of the user (calendarId) who created the event.
     * Typically this is of the format 'entity://HibUser/2'.
     * Note that this is probably meant to be the
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    String calendarIdUserReferenceUri;

    /**
     * The owner's calendarId - complements AttendeeAdapter#attendee.
     * This is the calendarId of calendarIdUserReferenceUri.
     */
    String ownerCalendarId;

    String title;
    String location;
    /**
     * Who is associated with this event.
     * This will be the user who created the appointment, and any others.
     * See as-was ItemIntegrationAdvice which calls postEntry, which uses the default rootCalendarId as the 'collection' to attach to.
     */
    List<EventAttendee> attendees;
    boolean allDay;
    // can be ISO8601
    LocalDateTime start;
    LocalDateTime end;
    //String url; // action to visit on click, removed to avoid property existing client-side and fullcalendar clicking it
    List<String> classNames; // eg colour!
    EventType eventType; // Interview, Meeting etc
    Integer eventCategoryId; // a 'type' when its a user event (list definition entry)

    Integer eventStatusId; // could also be on attendees

    /**
     * Also known as the 'outcome' or 'rate' of the event for the purposes of rostering.
     * An outcome that can be recorded from the rota pages or the staff app.
     * Examples include DNA / Rescheduled / Attended.
     * We keep this separate from eventStatus for simplicity as a new concept.
     */
    Integer eventStatusRateId;

    /**
     * Optional evidence item related to this event.
     */
    UUID evidenceWorkUuid;
}
