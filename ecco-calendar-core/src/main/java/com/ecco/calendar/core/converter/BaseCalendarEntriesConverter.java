package com.ecco.calendar.core.converter;

import com.ecco.calendar.core.CalendarEntries;

import java.util.LinkedHashSet;
import java.util.Set;

public abstract class BaseCalendarEntriesConverter<T> implements CalendarEntriesConverter<T> {

    @Override
    public Set<T> convert(Set<CalendarEntries> calendarEntriesSet) {
        Set<T> targetTypeSet = new LinkedHashSet<T>();
        for (CalendarEntries calEntries : calendarEntriesSet) {
            targetTypeSet.add(convert(calEntries));
        }
        return targetTypeSet;
    }

}
