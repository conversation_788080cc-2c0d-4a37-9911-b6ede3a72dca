package com.ecco.calendar.core;

import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import org.jspecify.annotations.Nullable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Provides operations around a recurring item and its associated attendee recurring items.
 * Unique by entry (since a new entry is created when it changes).
 *
 * @see CalendarRecurringSeriesDecorator
 */
public interface CalendarRecurringSeriesRepository extends CrudRepository<CalendarSeriesRelatedEntry, Integer> {

    /**
     * Returns the list of all handles relating to the provided handle.
     * NB Since the endDate flows as a date without time, we make the startDate date only to be clearer.
     * This indicates there could be more results than the actual range if time we're pushed through the code.
     */
    @Query("select entryHandle from CalendarSeriesRelatedEntry where seriesHandle=:handle and (" +
                "(startDate < :endDate and endDate >= :startDate and :endDate is not null) or " + // we are somewhere in the range at some point
                "(startDate <= :startDate and endDate >= :startDate and :endDate is null) or " + // we are active at the start date - because we're not testing a range
                "(startDate < :endDate and endDate is null) or " + // we start before, and don't end - so must be in the range
                "(endDate is null and :endDate is null))" // we may have started after, but neither has an end date so we must be in the range
            )
    List<RecurringEntryHandle> findAllBySeriesHandleInRange(@Param("handle") RecurringEntryHandle entryHandle,
                                                            @Param("startDate") LocalDate minDateInclusive,
                                                            @Param("endDate") LocalDate maxDateExclusive);

    @Query("select entryHandle from CalendarSeriesRelatedEntry where seriesHandle=:handle and " +
            "startDate <= :startDate"
    )
    List<RecurringEntryHandle> findAllBySeriesHandleStartOnOrBefore(@Param("handle") RecurringEntryHandle entryHandle,
                                                                    @Param("startDate") LocalDate start);

    @Query("select entryHandle from CalendarSeriesRelatedEntry where seriesHandle=:handle and " +
            "((endDate >= :endDate) or (endDate is null)) or " +
            "(endDate is null and :endDate is null)"
    )
    List<RecurringEntryHandle> findAllBySeriesHandleEndOnOrAfter(@Param("handle") RecurringEntryHandle entryHandle,
                                                                 @Param("endDate") LocalDate end);

    @Query("select seriesHandle from CalendarSeriesRelatedEntry where entryHandle=:handle")
    Optional<RecurringEntryHandle> findSeriesHandleByHandle(@Param("handle") RecurringEntryHandle handle);

    CalendarSeriesRelatedEntry findOneByEntryHandle(RecurringEntryHandle entryHandle);

    List<CalendarSeriesRelatedEntry> findAllBySeriesHandle(RecurringEntryHandle seriesHandle);

    List<CalendarSeriesRelatedEntry> findAllByParentHandle(RecurringEntryHandle parentHandle);

    boolean existsByEntryHandle(RecurringEntryHandle entryHandle);

    @Modifying
    void deleteByEntryHandle(RecurringEntryHandle entryHandle);

    @Modifying
    @Query("update CalendarSeriesRelatedEntry set startDate=:startDate, endDate=:endDate where entryHandle=:handle")
    void updateEntry(@Param("handle") RecurringEntryHandle entryHandle, @Param("startDate") LocalDate startDate, @Param("endDate") @Nullable LocalDate endDate);

}
