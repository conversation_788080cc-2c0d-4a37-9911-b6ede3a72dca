package com.ecco.calendar.core;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.DtoBuilderProxy;
import com.ecco.dto.ProxyDtoBuilderProxy;
import org.joda.time.DateTime;
import org.joda.time.Duration;

import java.io.Serializable;
import java.net.URI;

/**
 * Provides enough information to define a new unavailable interval for creation.
 *
 * @since 18/06/15
 */
public interface UnavailableIntervalDefinition extends Serializable, BuildableDto<UnavailableIntervalDefinition> {
    /** The title of the entry used for display purposes. */
    String getTitle();

    /** The start of the period of unavailability. */
    DateTime getStart();

    /** The length of time the unavailability lasts for. */
    Duration getDuration();

    /** URI of entity which manages this unavailability. */
    URI getManagedByUri();

    interface Builder extends DtoBuilder<UnavailableIntervalDefinition> {
        Builder title(String title);
        Builder start(DateTime start);
        Builder duration(Duration duration);
        Builder managedByUri(URI managedByUri);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, UnavailableIntervalDefinition.class);
        }

        public static Builder create(UnavailableIntervalDefinition template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }
    }

}
