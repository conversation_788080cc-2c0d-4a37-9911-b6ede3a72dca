package com.ecco.data.client.dataimport.csv

import au.com.bytecode.opencsv.CSVReader
import com.ecco.data.client.dataimport.csv.conversion.DataImportConversionService
import com.ecco.data.client.dataimport.csv.mappers.CsvBeanMapper
import com.ecco.data.client.dataimport.csv.mappers.CsvExpressionMapper
import com.ecco.data.client.dataimport.csv.mappers.CsvNullMapper
import com.ecco.data.client.dataimport.support.ImportOperation
import com.ecco.utils.extensions.excludeIndex
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.util.Assert
import org.springframework.util.StringUtils
import java.io.IOException
import java.io.Reader

/**
 * Process a CSV file (quote character = ", escape character = \), where the first two lines contains settings to be
 * used for the rest of the process.
 * <AUTHOR>
 *
 * @param <RESULT> */
class MappingCSVParser<RESULT>(
    private val dataMapper: (keyValues: List<Pair<String, Any?>>) -> RESULT,
    private val aliasMapper: (row: List<String>) -> List<String> = { row -> row },
    private val synonymMapper: (row: List<String>) -> List<String> = { row -> row },
) {
    private val log: Logger = LoggerFactory.getLogger(javaClass)

    fun parse(reader: Reader): List<ImportOperation<RESULT>> {
        val csv = RowCountingCsvReader(reader)
        val config = readConfig(csv)

        Assert.state(
            config.firstRow > config.mappingRow,
            "Invalid CSV import configuration: Require firstRow > mappingRow",
        )
        Assert.state(
            config.lastRow >= config.firstRow,
            "Invalid CSV import configuration: Require lastRow >= firstRow",
        )

        try {
            csv.skipToRow(config.mappingRow)

            val header = csv.readNext() ?: throw IllegalStateException("Cannot import CSV: Missing header")

            val skipColumnIndex =
                if (config.isSkipRowsEnabled) {
                    header.indexOf(config.skipRowsColumnName)
                } else {
                    -1
                }

            val expressionMapper =
                if (config.isSpELOperationsEnabled) {
                    val sources =
                        csv.readNext()
                            ?: throw IllegalStateException("Cannot import CSV: Missing SpEL operations")
                    val parser = SpelExpressionParser()
                    CsvExpressionMapper(sources.map { if (StringUtils.hasText(it)) parser.parseExpression(it) else null })
                } else {
                    CsvNullMapper()
                }

            csv.skipToRow(config.firstRow)
            return csv
                .readAsSequenceWithRowCount()
                .takeWhile { (row, _) -> row < config.lastRow }
                .map { (row, values) -> Pair(row, synonymMapper(values)) }
                .map { (row, values) -> Pair(row, expressionMapper(values)) }
                .filter { (row, values) ->
                    if (skipColumnIndex > -1 && values[skipColumnIndex].toString().toBoolean()) {
                        log.info("row #{} SKIPPING", row)
                        false
                    } else {
                        true
                    }
                }.map { (row, values) -> Pair(row, aliasMapper(header.toList()).zip(values)) }
                .map { (row, keyValues) -> Pair(row, keyValues.excludeIndex(skipColumnIndex)) }
                .map { (row, keyValues) -> Pair(row, dataMapper(keyValues)) }
                .map { (row, result) -> ImportOperation(config.baseUrl, result, row) }
                .toList()
        } catch (e: IOException) {
            throw RuntimeException(e)
        }
    }

    private fun readConfig(csv: CSVReader): ParserConfig {
        val header = csv.readNext()
        val values = csv.readNext()
        val mapper =
            CsvBeanMapper(
                ParserConfig::class.java,
                DataImportConversionService.getInstance(),
            )
        return mapper(header.zip(values))
    }
}