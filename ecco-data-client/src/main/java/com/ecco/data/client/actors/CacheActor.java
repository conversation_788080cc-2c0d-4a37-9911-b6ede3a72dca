package com.ecco.data.client.actors;

import com.ecco.webApi.serviceConfig.CacheCommandViewModel;
import com.ecco.webApi.viewModels.Result;

import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class CacheActor extends BaseActor {

    public CacheActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<Result> clearCaches() {
        CacheCommandViewModel cmd = new CacheCommandViewModel(true, null, false);
        return executeCommand(cmd);
    }

    public ResponseEntity<Result> clearServiceTypeCaches() {
        // cacheName as per RepositoryBasedServiceTypeService from ServiceTypeController
        CacheCommandViewModel cmd = new CacheCommandViewModel(
                        false,
                        List.of(new String[] {"serviceTypeViewModel"}),
                        false);
        return executeCommand(cmd);
    }

}
