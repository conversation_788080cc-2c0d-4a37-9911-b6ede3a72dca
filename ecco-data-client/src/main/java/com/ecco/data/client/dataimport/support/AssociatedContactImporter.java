package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.dataimport.csv.CSVBeanReader;
import com.ecco.data.client.model.AssociatedContact;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.CharSource;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class AssociatedContactImporter {

    protected final RestTemplate restTemplate;

    public AssociatedContactImporter(RestTemplate template) {
        this.restTemplate = template;
    }

    public void read(CharSource source) {

      final Map<String, String> allSynonyms = ImmutableMap.<String,String>builder()
              .putAll(Synonyms.synonymsToNormalizedMap)
              .putAll(Synonyms.relationshipSynonyms)
              .build();
      try {
          new CSVBeanReader<>(source, AssociatedContact.class, new HashMap<>() {
          }, allSynonyms)
                .readUsing(new AssociatedContactHandler(restTemplate));
      } catch (IOException e) {
        throw Throwables.propagate(e);
      }
    }

}
