package com.ecco.data.client.dataimport.support;

import java.io.IOException;
import java.util.Map;

import org.springframework.web.client.RestTemplate;

import com.ecco.data.client.dataimport.csv.CSVBeanReader;
import com.ecco.webApi.acls.AclEntryViewModel;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.CharSource;

public class AclImporter {

    private final Map<String, String> aliases = ImmutableMap.<String,String>builder()
            .build();

    private final RestTemplate restTemplate;

    public AclImporter(RestTemplate template) {
        this.restTemplate = template;
    }

    public void read(CharSource source) {

        try {
            AclHandler dataHandler = new AclHandler(restTemplate);
            dataHandler.ensureAcls();
            new CSVBeanReader<>(source, AclEntryViewModel.class, aliases, Synonyms.synonymsToNormalizedMap)
                    .readBatchUsing(dataHandler);
        } catch (IOException e) {
            throw Throwables.propagate(e);
        }
    }

}
