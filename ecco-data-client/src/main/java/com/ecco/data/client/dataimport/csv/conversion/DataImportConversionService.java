package com.ecco.data.client.dataimport.csv.conversion;

import org.springframework.core.convert.ConversionService;
import org.springframework.format.support.DefaultFormattingConversionService;
import org.springframework.format.support.FormattingConversionService;

public class DataImportConversionService {

    private static final FormattingConversionService SERVICE = new DefaultFormattingConversionService();

    static {
        SERVICE.addConverter(new LocalDateFromStringConverter());
        SERVICE.addConverter(new LocalDateTimeFromStringConverter());
        //new DateTimeFormatterRegistrar().registerFormatters(SERVICE);
    }

    public static ConversionService getInstance() {
        return SERVICE;
    }

}
