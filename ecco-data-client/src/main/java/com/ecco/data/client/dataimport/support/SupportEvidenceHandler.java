package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.model.evidence.GoalImportViewModel;
import com.ecco.data.client.model.evidence.OutcomeBasedImportViewModel;
import com.ecco.dom.EvidenceGroup;
import com.ecco.serviceConfig.viewModel.ActionViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.CommentCommandViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.GoalUpdateCommandViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.UUID;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Handles the import of a spreadsheet row which includes evidence information.
 * (We tried mapping the csv header straight to command view models, but this required
 * a public constructor and getter/setters).
 * Although we have our own EvidenceImportViewModel, its possible we could use the
 * out-bound EvidenceViewModel.
 */
class SupportEvidenceHandler extends BaseEvidenceHandler<OutcomeBasedImportViewModel> {

    // create sessionData as a class level variable
    private SessionDataViewModel sessionData;
    private List<OutcomeViewModel> supportOutcomes;

    SupportEvidenceHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    @Override
    void processEvidence(ImportOperation<OutcomeBasedImportViewModel> operation, UUID workUuid, ReferralViewModel rvm, SessionDataViewModel sessionData) {

        OutcomeBasedImportViewModel input = operation.record;

        if (!input.commentConcat.isEmpty()) {
            applyCommentConcat(input, rvm, workUuid);
        }

        // NB createAndSync below is hardcoded on NEEDS_ASSESSMENT and NEEDS group, so we do so here
        CommentCommandViewModel cc = createCommentCommand(input, EvidenceGroup.NEEDS, EvidenceTask.NEEDS_ASSESSMENT, rvm, workUuid);
        cc.commentTypeId = ChangeViewModel.create(null, input.commentTypeId);
        cc.minsSpent = ChangeViewModel.create(null, input.minsSpent);
        cc.mileageTo = ChangeViewModel.create(null, input.mileageTo);
        cc.mileageDuring = ChangeViewModel.create(null, input.mileageDuring);
        //cc.flagIds

        syncCommentCommandToServer(operation.baseUri.concat(apiPath), cc);

        if (!input.goals.isEmpty()) {
            createAndSyncGoalCommandsToServer(operation.baseUri.concat(apiPath), input, rvm, workUuid);
        }

        /*
        public AreaUpdateCommandViewModel riskAreaCommand;
        public GoalRiskUpdateCommandViewModel riskGoalCommand;
        */
    }

    private void createAndSyncGoalCommandsToServer(String baseUri, OutcomeBasedImportViewModel input, ReferralViewModel rvm, UUID workUuid) {

        // set sessionData as a class level variable if not already set
        if (sessionData == null) {
            sessionData = sessionDataActor.getSessionData().getBody();
            supportOutcomes = StreamSupport.stream(sessionData.supportOutcomes.spliterator(), false).collect(Collectors.toList());
        }

        var st = getServiceType(rvm.serviceTypeId.intValue());

        for (Entry<String, GoalImportViewModel> entry : input.goals.entrySet()) {
            int actionDefId = getActionId(entry.getKey(), supportOutcomes);

            GoalImportViewModel goalIn = entry.getValue();
            GoalUpdateCommandViewModel goal = new GoalUpdateCommandViewModel(workUuid,
                    rvm.serviceRecipientId,
                    EvidenceGroup.NEEDS,
                    // TODO this is perhaps dependent on the status, but doesn't really matter
                    EvidenceTask.NEEDS_ASSESSMENT,
                    actionDefId,
                    UUID.randomUUID(), // TODO: within data-import, this causes a different actionInstance for every entry even if you have multiple ones on the same work and action !!
                    null);

            goal.operation = BaseCommandViewModel.OPERATION_ADD;

            // TODO Create enum for server side use
            // (it seems we only have static refs - see GenericTypeAction)
            // refer to scripts/evidence/dto.ts for SmartStepStatus
            Integer status = goalIn.achieved ? 3 : null;
            if (status == null) {
               status = goalIn.wanted ? 1 : null;
            }
            goal.statusChange = ChangeViewModel.create(null, status);

            // sensible to only save a new goal if there is a status
            if (status != null) {
                syncGoalCommandToServer(baseUri, goal);
            }
        }
    }

    private long syncGoalCommandToServer(String uri, GoalUpdateCommandViewModel input) {
        com.ecco.webApi.viewModels.Result result = executeCommand(uri, input);
        return Long.parseLong(result.getId());
    }

    private int getActionId(String name, List<OutcomeViewModel> supportOutcomes) {
        ActionViewModel[] foundActions = supportOutcomes.stream()
                .flatMap(o -> o.actionGroups.stream())
                .flatMap(ag -> ag.actions.stream())
                .filter(a -> a.name.equalsIgnoreCase(name))
                .toArray(ActionViewModel[]::new);

        Assert.state(foundActions.length == 1, "Action [" + name + "] found " + foundActions.length + " times for outcomes");

        return foundActions[0].id;
    }
}
