package com.ecco.data.client.actors;

import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.ecco.webApi.featureConfig.SessionDataViewModel;
import com.ecco.webApi.listsConfig.FundingSourceViewModel;
import com.ecco.webApi.viewModels.Result;

public class SessionDataActor extends BaseActor {

    public SessionDataActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<SessionDataViewModel> getSessionData() {
        return restTemplate.getForEntity(
                apiBaseUrl + "config/global",
                SessionDataViewModel.class);
    }

    public ResponseEntity<Result> createFundingSource(FundingSourceViewModel fvm) {

        ResponseEntity<Result> responseEntity = restTemplate.postForEntity(
                apiBaseUrl + "fundingSource/", fvm, Result.class);
        Assert.state(responseEntity.getStatusCode().is2xxSuccessful(), responseEntity.getBody().getMessage());
        return responseEntity;
    }

}
