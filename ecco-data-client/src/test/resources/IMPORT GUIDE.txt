***************************
    QUICK GUIDE

GOTCHAS
======
 - check encoding of csv when opening
 - check url - wrong url gets Jackson error Unrecognized 'code' field, not marked as ignorable
 - check version of code - new property locally may need removing else it causes: "not marked as ignorable; nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException:"
 - manipulate spreadsheet where possible (or push back to customer) to avoid time on SPEL
    - but see sampleClientImport for the mapping ideas there
 - update sql at the end to set start dates etc
 - CLEAR CACHE also, as the EtaggedResponseCacheManager cache still exists (even persists) beyond repository cache evictions

USAGE
=====
copy a csv file into /src/test/resources
modify ImportProcessorTest (main method) to load the code and process
    also supply URL as an argument on running the import:
    -Dtarget.host.url=https://test.eccosolutions.co.uk/test11
modify ImportProcessorBase to change the login for the username and password
    create/enable ecco_import user on the target
clean up the imported data, at the end - eg:

     -- SEE all statuses (+0 due to https://stackoverflow.com/questions/11609474/mysql-always-returning-bit-values-as-blank)
        select r.id as rid, r.servicerecipientId as srid, r.clientId as cid, r.decision+0,r.acceptedOnService+0,r.acceptedReferral+0,r.decisionReferralMadeOn,r.decisionMadeOn,r.receivingServiceDate,r.signpostedCommentId, r.exitCommentId
        from referrals r
        inner join servicerecipients sr on r.servicerecipientId=sr.id
        where sr.serviceallocationid in (?)
        -- limit 5;

     -- IDENTIFY those imported
        -- NB this is based on 'sr.created > date(now()) and r.decisionmadeon is null' BUT could capture new referrals during the time
        select r.id,sr.created,r.id,r.code,r.clientId,r.decision+0,r.acceptedreferral+0,r.acceptedonservice+0,r.decisionmadeon,r.receivingService+0,r.receivingServiceDate
        from referrals r
        inner join servicerecipients sr on r.servicerecipientId=sr.id
        inner join clientdetails cd on r.clientId=cd.id
        inner join contacts c on cd.contactsId=c.id
        inner join services_projects sp on sp.id=sr.serviceAllocationId
        inner join services s on s.id=sp.serviceId
        where sr.serviceallocationid in (?)
        -- where sr.created >= curdate();

     -- ACCEPT [if call 'accept on service' then this is done]
        update referrals set decision=1,acceptedreferral=1,acceptedonservice=1
        where id in (select r.id from (select * from referrals) r inner join servicerecipients sr on sr.id=r.servicerecipientId inner join services_projects sp on sp.id=sr.serviceAllocationId inner join services s on s.id=sp.serviceId
            where r.decisionmadeon is null and sr.created > date(now())
            and s.id=?);

    -- START (receivingService and date)
    update referrals set receivingService=1,receivingServiceDate=decisionmadeon
    where id in (select r.id from (select * from referrals) r inner join servicerecipients sr on sr.id=r.servicerecipientId inner join services_projects sp on sp.id=sr.serviceAllocationId inner join services s on s.id=sp.serviceId
        where r.receivingService=0 and r.decision=1 and r.acceptedreferral=1 and r.acceptedonservice=1 and r.decisionmadeon is not null
        and sr.created > date(now())
        and s.id=?);

    - INDEX [no longer needed?]
        update servicerecipients sr
        inner join referral r on r.serviceRecipientId=sr.id
        set sr.currentTaskIndex=99
        where r.decisionmadeon is null and sr.created > date(now())
        and serviceallocationId in (select id from services_projects sp where sp.serviceid=?);

    -- MOVE to the proper service/project
        update servicerecipients sr
        inner join referrals r on r.serviceRecipientId=sr.id
        set sr.serviceallocationId=?
        where sr.created > curdate()
        and sr.serviceallocationId=?
        -- and serviceallocationId in (select id from services_projects sp where sp.serviceid=?);


check ReferralAggregateImporter.strictReferenceMode
debug the ImportProcessorTest as a java application
The field names to use can be seen in the sample files, and refer to ecco-data-client/*Importer files.
    (also note *Importer synonyms that translate values)
These are extra fields, so fields not shown here get directly mapped onto the webApi ViewModel.
The ecco-data-client/*Handler files also interpret the object into webApi ViewModels which get posted.
On the server side, the controllers receive the ViewModel and translate with *FromViewModel.
eg ReferralAggregateImporter / ReferralViewModel -> ReferralAggregateHandler -> ReferralFromViewModel


strictReferenceMode: create reference data on the fly
    If you are sure your data is clean and you've got an empty database, then
    you may choose to do this, but don't commit the change to enable it.
    Temporarily change this in ReferralAggregateImporter.strictReferenceMode


CSV FORMAT
==========
currently quote character = " escape character = backslash

If your CSV file ends up with +ACI- instead of ", then open it has been opened and saved as UTF-7.
- Open again in OpenOffice
- File -> Save as...
- Tick "Filter settings..."
- Select UTF-8
Note: you may see UTF-7 in the CSV import dialog, so change that to UTF-8 and you'll probably avoid corrupting it.

first 2 rows are always config
    the mappingRow is the equivalent ecco mapped name (no text skips the column)
    firstRow is the first to import
    lastRow is the last and is inclusive
    spEL allows use of spEL on a line underneath the mapping line, eg #this.replaceAll(' ', '');
    skipRowsColumnName defines the column name to which skipRowsEnabled looks for
    skipRowsEnabled looks up a column defined by skipRowsColumnName to determine whether a row is skipped before processing
    base url - the place to submit api calls
      baseUri is put into ImportOperation which is passed to *Handler.processEntity and used to do api calls
    therefore:
      login url: ClientConfig
      credentials: ImportProcessorBase
      api url: on the individual files
        (the url is tied to the file for safety)

the ecco mapped names are explained by looking in the ecco-data-client *Importer class for the import(s) being run and its corresponding read() class
    (eg we take examples from ReferralAggregateImporter, which uses ReferralAggregate)
    aliases: first the ecco mapped name is checked for an alias defined in ReferralAggregateImporter, so firstName maps to client.firstName - where client is a property of ReferralAggregateImporter - which is typically the ClientViewModel for the web-api
    synonyms: a collection of mappings which take a value and translate it to another value (eg Straight to Hetrosexual)
    mappings such as 'referralIndividuals['GP'].type therefore map to the ReferralAggregateImporter referralIndividual map of <String, IndividualViewModel> and are processed according to the ReferralAggregateHandler
    the individual is added, or returned if already exists (from some error saving, or duplicate code) and then added to the referral 'individualContacts'

