"mappingRow","firstRow","lastRow","spELOperationsEnabled","baseUrl"
7,9,9,TRUE,"http://localhost:8888/ecco-war"
,,,,"^^ need to also change in ClientConfig.java"
"First create A101 on the acommodation service to see the entry, although you wont anyway since the evidencegroup is '100'",,,"Base data service 'accommodation' is configured with an iaptFeedback questionniare which has 'SDQ' where question 1 is 'I try to be nice to other people. I care about their feelings'",
,,,"evidenceGroup is derived","The answers are 'not true'; 'somewhat true'; 'always true'; 'missing'"
,,,,
"referralCode","workDate","commentConcat['imported comment']","evidenceTask","answers[""I try to be nice to other people. I care about their feelings""]"
,"#this.equals('NULL') ? null : #this",,"'iaptFeedback'",
"A101",07/03/03,"This and this for this note",,"not true"
