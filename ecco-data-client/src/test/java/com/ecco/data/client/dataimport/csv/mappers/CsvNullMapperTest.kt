package com.ecco.data.client.dataimport.csv.mappers

import kotlin.test.*

class CsvNullMapperTest {
    private val mapper = CsvNullMapper()

    @Test
    fun emptyList() {
        assertEquals(listOf(), mapper(listOf()))
    }

    @Test
    fun listOfText() {
        assertEquals(listOf("a", "b", "c"), mapper(listOf("a", "b", "c")))
    }

    @Test
    fun listWithSomeEmptyStrings() {
        assertEquals(listOf("a", null, "b", "c", null), mapper(listOf("a", "", "b", "c", "")))
    }
}