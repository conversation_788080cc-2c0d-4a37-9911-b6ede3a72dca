package com.ecco.webApi.boot;

import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.offline.OfflineConfig;
import com.ecco.offline.ResourcesWebMvcConfig;
import com.ecco.service.config.ServiceConfig;
import com.ecco.serviceConfig.config.MessageSourceConfig;
import com.ecco.web.WebMvcCommonConfig;
import com.ecco.webApi.WebApiConfig;
import com.ecco.security.config.SecurityMethodConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.cache.CacheMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.hateoas.HypermediaAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@Configuration
@ComponentScan
@EnableAutoConfiguration(exclude = {
        HypermediaAutoConfiguration.class,
        CacheMetricsAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class // We do this in our persistence config
})
@EnableConfigurationProperties()
@EnableTransactionManagement
@EnableWebMvc
@EnableAspectJAutoProxy(proxyTargetClass = true)
@Import({
        SecurityMethodConfig.class,
        InfrastructureConfig.class,
        ServiceConfig.class,
        WebApiConfig.class,
        WebMvcCommonConfig.class,
        ResourcesWebMvcConfig.class,
        MessageSourceConfig.class,
        OfflineConfig.class
})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
