package com.ecco.calendar.event;

import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.bus.Subscriber;
import com.ecco.security.event.LocationUpdated;
import net.fortuna.ical4j.model.Parameter;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.parameter.AltRep;
import net.fortuna.ical4j.model.property.Location;
import org.osaf.cosmo.service.ContentService;

import java.net.URISyntaxException;

/**
 * Keeps LOCATION values up to date by reacting to {@link LocationUpdated} events.
 */
public class LocationSyncAgent extends BulkPropertyUpdater<Location, AltRep, LocationUpdated> {

    public LocationSyncAgent(int monthsToSync, MessageBus messageBus, ContentService contentService) {
        // Property.Location has Parameter.ALTREP that can specify a uri - see https://tools.ietf.org/html/rfc5545
        super(monthsToSync, contentService, Property.LOCATION, Parameter.ALTREP, AltRep.class);

        messageBus.subscribe(LocationUpdated.class, new Subscriber<LocationUpdated>() {
            @Override
            public void receive(LocationUpdated message) {
                updateProperties(message.getLocationReference(), message);
            }
        });
    }

    @Override
    protected void updateProperty(Location property, LocationUpdated message) throws URISyntaxException {
        property.setValue(message.getValue());
    }
}
