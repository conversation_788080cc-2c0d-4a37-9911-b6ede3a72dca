package com.ecco.calendar.cosmo;

import java.net.URI;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.fortuna.ical4j.model.Parameter;
import net.fortuna.ical4j.model.parameter.Dir;
import net.fortuna.ical4j.model.parameter.PartStat;
import net.fortuna.ical4j.model.parameter.Role;
import net.fortuna.ical4j.model.property.Attendee;

public class AttendeeAdapter implements com.ecco.calendar.core.Attendee {

    static final Logger log = LoggerFactory.getLogger(AttendeeAdapter.class);

    private final Attendee attendee;
    private final String attendeeCalendarId;

    public AttendeeAdapter(Attendee attendee, String attendeeCalendarId) {
        this.attendee = attendee;
        this.attendeeCalendarId = attendeeCalendarId;
    }

    @Override
    public String getName() {
        final Parameter cn = attendee.getParameter(Parameter.CN);
        if (cn != null) {
            return cn.getValue();
        } else {
            // Note: in EntryConverter, this was to return
            String calAddress =  attendee.getCalAddress().getSchemeSpecificPart();
            log.warn("No CN on attendee. Returning email, but some code used to return calAddress: {}", calAddress);
            return getEmail();
        }
    }


    /**
     * Unique reference to the native object of the user (calendarId) of the attendee.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    @Override
    public String getCalendarIdUserReferenceUri() {
        return getDirEntryRef() != null ? getDirEntryRef().toString() : null;
    }

    private URI getDirEntryRef() {
        Dir directoryEntryRef = attendee.getParameter(Parameter.DIR);
        return directoryEntryRef != null ? directoryEntryRef.getUri() : null;
    }

    /*
     * Get the calendarId, which is the CollectionItem from the attendee owner's SystemCollection calendar.
     * @see NoteItemToRecurringEntryAdapter#getOwnerCalendarId().
     * We can't use exactly the same code as there, but we can getRootItem from the getDirEntryRef()
     * by using EntityUriMapper - since its set this way in CosmoHelper#syncAttendeesWithCalendars.
     */
    public String getCalendarId() {
        return attendeeCalendarId;
    }

    /**
     * Cosmo's email cannot be used as a unique identifier.
     * In the calendaring system, HibUser has the email as unique (also see cosmo_users constraint). The email is set from
     * CalendarableEntity's which fire an CalendarableCreated event picked up by CalendarUserSyncAgent. Individual creates
     * an email of the form: getUsername() + "@eccosolutions.co.uk". ReferralServiceImpl.setNewClient still creates a user,
     * but if it didn't the calendar would still require a unique email.
     * HOWEVER updates to emails need to be passed on to the calendaring system - within each event attendee - which seems
     * cumbersome, but this is already currently done for names - see BulkPropertyUpdater triggered from
     * CosmoCalendarService.updateCalendarOwner (where emails are skipped, not sure why - see Individual.buildCalendarNamesUpdate).
     * HOWEVER, bulk updates are designed to change future events (set for 10 years) to retain the data from the past
     * so we don't want to use email as a unique identifier since we can't go in the past and find the same identifier.
     * Non-Individual's such as Container do create a calendar email which is unique and not designed to change.
     */
    @Override
    public String getEmail() {
        return attendee.getValue().startsWith("mailto:")? attendee.getValue().substring(7) : attendee.getValue();
    }

    @Override
    public boolean isRequired() {
        final Parameter role = attendee.getParameter(Parameter.ROLE);
        return role != null && (role.equals(Role.CHAIR) || role.equals(Role.REQ_PARTICIPANT));
    }

    @Override
    public Status getStatus() {
        final Parameter partStat = attendee.getParameter(Parameter.PARTSTAT);
        if (partStat != null) {
            if (partStat.equals(PartStat.ACCEPTED)) {
                return Status.ACCEPTED;
            }
            if (partStat.equals(PartStat.DECLINED)) {
                return Status.DECLINED;
            }
            if (partStat.equals(PartStat.TENTATIVE)) {
                return Status.TENTATIVE;
            }
        }
        return null;
    }
}
