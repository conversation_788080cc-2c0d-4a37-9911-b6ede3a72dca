package com.ecco.calendar.cosmo;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.google.common.collect.Range;
import com.ecco.calendar.core.DaysOfWeek;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import net.fortuna.ical4j.model.Calendar;
import net.fortuna.ical4j.model.Recur;
import net.fortuna.ical4j.model.WeekDay;
import org.joda.time.LocalDate;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.service.ContentService;
import org.osaf.cosmo.service.UserService;
import org.springframework.context.ApplicationEvent;

import org.jspecify.annotations.Nullable;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Date;

/**
 * An investigation into using <PERSON>cur as a modification.
 * This would have allowed allocating to the masters representing portions of a schedule.
 * See CosmoCalendarServiceTest#shouldFindExtendedRecurrenceFromModifiedEvent.
 *
 * @deprecated This was for investigation purposes only, and is now archived
 */
@WriteableTransaction
public class CosmoCalendarRecurringOverrideService extends CosmoCalendarRecurringService {

    /** For CGLIB only */
    public CosmoCalendarRecurringOverrideService() {
        this(null, null, null, null, null, null);
    }

    public CosmoCalendarRecurringOverrideService(ContentService contentService, UserService userService, EntityFactory entityFactory,
                                                 EntityUriMapper entityUriMapper, MessageBus<ApplicationEvent> messageBus,
                                                 final CosmoConverter entryConverter) {
        super(contentService, userService, entityFactory, entityUriMapper, messageBus, entryConverter);
    }

    @Override
    public int confirmRecurrencesInRange(RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                         @Nullable DaysOfWeek matchDays, @Nullable URI updatedBy,
                                         @Nullable Integer allocateRescheduledMins, Range<LocalDate> rescheduleBounds,
                                         String allocateToResourceCalendarId) {

        //var recurringEntry = getRecurringEntry(recurringEntryHandle);
        var recurringEntry = contentService.findItemByUid(recurringEntryHandle.toString());
        var recurringEntryStamp = StampUtils.getEventStamp(recurringEntry);
        var modification = recurringEntry.copy();
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp((NoteItem) modification);
        // createCalendar failed, so try setting the event calendar as per other frequent calendar operations
        //modStamp.createCalendar();
        try {
            modStamp.setEventCalendar(new Calendar(recurringEntryStamp.getEventCalendar()));
        } catch (ParseException | IOException | URISyntaxException e) {
            e.printStackTrace();
        }
        final var recurrenceIdStartJdk = range.lowerEndpoint().atZone(ZoneId.of("UTC"));
        final var recurrenceIdStart = Date.from(range.lowerEndpoint());
        modStamp.setRecurrenceId(new net.fortuna.ical4j.model.DateTime(recurrenceIdStart));
        // overwrite the copied properties - or we can set duration which overwrites end date
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(Date.from(recurrenceIdStartJdk.plusHours(1).toInstant())));
        // duration or end date
        modStamp.setDuration(recurringEntryStamp.getDuration());
        //modStamp.setEndDate(recurrenceIdEnd);
        modStamp.setStatus(recurringEntryStamp.getStatus());

        Recur recur = recurringEntryStamp.getRecurrenceRules().stream().findFirst().orElseThrow();
        // override if weekly, otherwise just use the recur that is already there
        if (recur.getFrequency().equals(Recur.Frequency.WEEKLY)) {
            // as per CosmoConverter
            recur = new Recur(Recur.WEEKLY, null);
            for (Integer day : DaysOfWeek.toSet(matchDays)) {
                recur.getDayList().add(WeekDay.getDay(day));
            }
        }
//        if (entry.getIntervalFrequency() > 1) {
//            recur.setInterval(entry.getIntervalFrequency());
//        }
        var until = Date.from(range.upperEndpoint());
        if (until != null) {
            //Date dte = end.toDateTime(start.toLocalTime()).toDate();
            recur.setUntil(new net.fortuna.ical4j.model.DateTime(until));
        }
        modStamp.setRecurrenceRule(recur);

        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(recurringEntry, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        ((NoteItem) modification).setModifies((NoteItem) recurringEntry);
        ((NoteItem) recurringEntry).addModification((NoteItem) modification);

        contentService.createContent(recurringEntry.getParent(), (ContentItem) modification);
        return 1;
        /*
        AtomicInteger count = new AtomicInteger();
        Stream<Recurrence> recurrences = findRecurrences(recurringEntryHandle, range, Recurrence.Status.TENTATIVE);
        recurrences.forEach(r -> {
            var matchRecurrence = matchDays == null || matchDays.isCalendarDayISO(r.getStart().dayOfWeek().get());
            if (matchRecurrence) {
                if (allocateRescheduledMins != null) {
                    var adjustedStartJoda = r.getStart().withFieldAdded(DurationFieldType.minutes(), allocateRescheduledMins);
                    java.time.LocalDateTime adjustedStart = JodaToJDKAdapters.dateTimeToJdk(adjustedStartJoda).toLocalDateTime();
                    rescheduleActivity(r.getRecurrenceHandle(), null, localDateTimeToJoda(adjustedStart), null, updatedBy, rescheduleBounds);
                    entityManager.flush(); // Otherwise we end up allocating at the old start time
                }

                confirmRecurrence(r.getRecurrenceHandle(), updatedBy, allocateToResourceCalendarId);

                count.getAndIncrement();
            }
        });
        return count.get();
        */
    }
}
