package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.*;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import net.fortuna.ical4j.model.Component;
import net.fortuna.ical4j.model.DateList;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.parameter.Value;
import net.fortuna.ical4j.validate.ValidationException;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.*;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.osaf.cosmo.calendar.ICalendarUtils;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.hibernate.HibEntityFactory;
import org.osaf.cosmo.service.ContentService;
import org.osaf.cosmo.service.UserService;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ecco.calendar.cosmo.CosmoConverter.MANAGED_BY_PROPERTY_NAME;
import static com.ecco.calendar.cosmo.CosmoTestDataUtil.*;
import static java.util.stream.Collectors.toSet;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.*;
import static org.osaf.cosmo.icalendar.ICalendarConstants.COMPONENT_VAVAILABLITY;

/**
 * Test the CosmoConverter, which translates between generic and cosmo-specific entities.
 */
public class EntryConverterTest {

    private CosmoConverter converter;
    private final CosmoTestDataUtil dataUtil = new CosmoTestDataUtil();
    private final URI userRefUri = new URI("entity://HibUser/2");

    public EntryConverterTest() throws URISyntaxException {
    }

    @Before
    public void setup() {
        EntityUriMapper mockEntityUriMapper = mock(EntityUriMapper.class);
        when(mockEntityUriMapper.uriForEntity(any())).thenReturn(userRefUri);
        ContentService serv = mock(ContentService.class);
        HomeCollectionItem home = mock(HomeCollectionItem.class);
        when(home.getChildByName(any())).thenReturn(mock(Item.class));
        when(serv.getRootItem(any())).thenReturn(home);
        converter = new CosmoConverter(Mockito.mock(SoftwareFeatureService.class),
                new HibEntityFactory(), mockEntityUriMapper,
                serv, Mockito.mock(UserService.class));
    }

    @Test
    @Ignore("Need to establish whether ID should be converted as this behaviour changed in commit on 18Sep12")
    public void shouldConvertEntry() {
        final String entryId = "1";
        NoteItem item = converter.convert(dataUtil.createTestEntry(entryId, CAL_1_ID));
        dataUtil.assertItemValues(item, entryId);
    }

    @Test
    @Ignore("Need to establish whether ID should be converted as this behaviour changed in commit on 18Sep12")
    public void shouldConvertAllDayEntry() {
        final String entryId = "1";
        Entry entry = dataUtil.createTestEntry(entryId, CAL_1_ID);
        entry.setAllDay(true);
        NoteItem item = converter.convert(entry);
        dataUtil.assertAnyTimeItemValues(item, entryId);
    }

    @Test
    public void shouldConvertEntryAllDay() {
        final String entryId = "1";
        var testEntry = dataUtil.createTestEntry(entryId, CAL_1_ID);
        testEntry.setStart(testEntry.getStart().withTimeAtStartOfDay());
        testEntry.setAllDay(true);
        testEntry.setEnd(null);
        NoteItem item = converter.convert(testEntry);
        EventStamp stamp = (EventStamp) item.getStamp(EventStamp.class);
        // the 'convert' method doesn't really do a lot - it assumes we've provided the right details
        assertNull(stamp.getEndDate());
    }

    @Test
    public void shouldConvertItem() {
        final String itemId = "1";
        Entry entry = converter.convertItemToEntry(dataUtil.createTestItem(itemId, CAL_1_ID));
        dataUtil.assertEntryValues(entry, itemId, CAL_1_ID);
    }

    @Test
    public void shouldConvertAnyTime1DaysItem() {
        final String itemId = "1";
        NoteItem item = dataUtil.createTestItemNoEnd(itemId, CAL_1_ID);
        var stamp = ((EventStamp) item.getStamp(EventStamp.class));
        stamp.setAnyTime(true);
        // allday should be a date, not datetime
        var today = new net.fortuna.ical4j.model.Date(new DateTime().withTimeAtStartOfDay().toDate());
        stamp.setStartDate(today);

        // no duration, or end date

        Entry entry = converter.convertItemToEntry(item);
        dataUtil.assertAllDayEntryValues(entry, itemId, CAL_1_ID, new DateTime().withTimeAtStartOfDay(),1);
    }

    @Test
    public void shouldConvertAnyTime4DaysItem() {
        final String itemId = "1";
        NoteItem item = dataUtil.createTestItemNoEnd(itemId, CAL_1_ID);
        var stamp = ((EventStamp) item.getStamp(EventStamp.class));
        stamp.setAnyTime(true);
        // allday should a date, not datetime
        var jdkDate = JodaToJDKAdapters.dateTimeToJdk(START.withTimeAtStartOfDay()).toInstant();
        var icalDate = new net.fortuna.ical4j.model.Date(jdkDate.toEpochMilli());
        stamp.setStartDate(icalDate);

        // set a duration
        stamp.setDuration(java.time.Duration.ofDays(4));

        Entry entry = converter.convertItemToEntry(item);
        dataUtil.assertAllDayEntryValues(entry, itemId, CAL_1_ID, START.withTimeAtStartOfDay(),4);
    }

    @Test
    public void shouldConvertItemByDay() {
        final String itemId = "byDay";
        DateTime start = DateTime.parse("2022-01-06").withTime(8, 0, 0, 0);
        DateTime end = start.withTime(9, 30, 0, 0);

        var testItem = dataUtil.createTestItem(itemId, CAL_1_ID);

        converter.setRecurringItemEventStamp(testItem, new RecurringEntryDefinition() {
            @Override
            public String getTitle() {
                return null;
            }

            @Override
            public String getDescription() {
                return null;
            }

            @Override
            public DateTime getStart() {
                return start;
            }

            @Override
            public Duration getDuration() {
                return new Duration(start, end);
            }

            @Override
            public LocalDate getScheduleEndDate() {
                return end.plusDays(14).toLocalDate();
            }

            @Override
            public String getIntervalType() {
                return "WK";
            }

            @Override
            public Set<Integer> getCalendarDays() {
                return new HashSet<>(Arrays.asList(2,4,6)); // Mon Wed Fri
            }

            @Override
            public Integer getIntervalFrequency() {
                return 1;
            }

            @Override
            public URI getManagedByUri() {
                return null;
            }

            @Override
            public URI getUpdatedByUri() {
                return null;
            }
        }, true);

        EventStamp stamp = StampUtils.getEventStamp(testItem);
        var rrule = stamp.getRecurrenceRules().get(0);

        // mimic issue that occurs in RecurrenceExpander#calculateRecurrenceRange
        var icalDate = new net.fortuna.ical4j.model.DateTime(start.toDate());
        var icalEndDate = new net.fortuna.ical4j.model.DateTime(end.plusYears(1).toDate());
        DateList startDates = rrule.getDates(icalDate, icalDate, icalEndDate, Value.DATE_TIME);

        // if starts today (6th) and ends in 2 weeks (20th) then we have 6
        var expectedInstants = Arrays.asList(
                    "2022-01-07T08:00:00.00Z","2022-01-10T08:00:00.00Z","2022-01-12T08:00:00.00Z","2022-01-14T08:00:00.00Z","2022-01-17T08:00:00.00Z","2022-01-19T08:00:00.00Z"
                ).stream().map(java.time.Instant::parse).collect(Collectors.toList());
        assertArrayEquals(expectedInstants.toArray(), startDates.stream().map(Date::toInstant).toArray());
    }

    @Test
    public void shouldConvertItemAllDays() {
        final String itemId = "byDay";

        // *******************
        // SINGLE event
        NoteItem testItem = dataUtil.createTestItemNoEnd(itemId, CAL_1_ID);
        EventStamp stampSingle = StampUtils.getEventStamp(testItem);
        stampSingle.setAnyTime(true);
        // allday should be a date, not datetime
        var today = new net.fortuna.ical4j.model.Date(new DateTime().withTimeAtStartOfDay().toDate());
        stampSingle.setStartDate(today);
        // no duration, or end date
        Entry entry = converter.convertItemToEntry(testItem);
        dataUtil.assertAllDayEntryValues(entry, itemId, CAL_1_ID, new DateTime().withTimeAtStartOfDay(),1);

        // *******************
        // RECURRING the event
        DateTime start = DateTime.parse("2023-03-13");
        DateTime end = start.plusDays(2); // recurring Mon Wed Fri means we also cover Tues Thurs Sat
        converter.setRecurringItemEventStamp(testItem, new RecurringEntryDefinition() {
            @Override
            public String getTitle() {
                return null;
            }

            @Override
            public String getDescription() {
                return null;
            }

            @Override
            public DateTime getStart() {
                return start;
            }

            @Override
            public Duration getDuration() {
                return new Duration(start, end);
            }

            @Override
            public LocalDate getScheduleEndDate() {
                return end.plusDays(14).toLocalDate();
            }

            @Override
            public String getIntervalType() {
                return "WK";
            }

            @Override
            public Set<Integer> getCalendarDays() {
                return new HashSet<>(Arrays.asList(2,4,6)); // Mon Wed Fri
            }

            @Override
            public Integer getIntervalFrequency() {
                return 1;
            }

            @Override
            public URI getManagedByUri() {
                return null;
            }

            @Override
            public URI getUpdatedByUri() {
                return null;
            }
        }, true);

        EventStamp stamp = StampUtils.getEventStamp(testItem);
        var rrule = stamp.getRecurrenceRules().get(0);

        // mimic issue that occurs in RecurrenceExpander#calculateRecurrenceRange
        var icalDate = new net.fortuna.ical4j.model.DateTime(start.toDate());
        var icalEndDate = new net.fortuna.ical4j.model.DateTime(end.plusYears(1).toDate());
        DateList startDates = rrule.getDates(icalDate, icalDate, icalEndDate, Value.DATE_TIME);

        // if starts today (6th) and ends in 2 weeks (20th) then we have 6
        var expectedInstants = Arrays.asList(
                "2023-03-13T00:00:00.00Z","2023-03-15T00:00:00.00Z","2023-03-17T00:00:00.00Z","2023-03-20T00:00:00.00Z","2023-03-22T00:00:00.00Z","2023-03-24T00:00:00.00Z","2023-03-27T00:00:00.00Z",
                "2023-03-29T00:00:00.00Z" // recurring finishes at the end date + 14 days, which is start + 2 + 14
        ).stream().map(java.time.Instant::parse).collect(Collectors.toList());
        assertArrayEquals(expectedInstants.toArray(), startDates.stream().map(Date::toInstant).toArray());
    }

    @Test
    public void shouldConvertItems() {
        final String itemId1 = "1";
        final String itemId2 = "2";
        Set<Item> items = new HashSet<>();
        items.add(dataUtil.createTestItem(itemId1, CAL_1_ID));
        items.add(dataUtil.createTestItem(itemId2, CAL_1_ID));
        var entries = converter.convert(items.stream()).collect(toSet());
        assertEquals(2, entries.size());
        for (Entry entry : entries) {
            if (entry.getItemUid().equals(itemId1)) {
                dataUtil.assertEntryValues(entry, itemId1, CAL_1_ID);
            } else {
                dataUtil.assertEntryValues(entry, itemId2, CAL_1_ID);
            }
        }
    }

    @Test
    public void shouldConvertEmptyAvailability() {
        final String attributeKey = getClass().getSimpleName();
        final String attributeValue = String.valueOf(getClass().hashCode());
        final String availabilityId = "1";
        Availability availability = dataUtil.createEmptyAvailability(availabilityId, attributeKey, attributeValue);

        final AvailabilityItem availabilityItem = converter.convert(availability);

        dataUtil.assertItemAttribute(availabilityItem, attributeKey, attributeValue);
        dataUtil.assertAvailabilityCalendarValues(availabilityItem, availability.getInterval());
    }

    @Test
    public void shouldRoundTripEmptyAvailability() {
        final String availabilityId = "1";
        Availability availability = dataUtil.createEmptyAvailability(availabilityId, getClass().getSimpleName(), String.valueOf(getClass().hashCode()));

        final AvailabilityItem availabilityItem = converter.convert(availability);
        final Availability roundTripped = converter.convert(availabilityItem);

        dataUtil.assertRoundTrip(availability, roundTripped);
    }

    @Test
    public void shouldConvertAvailabilityWithSingleInterval() throws ValidationException {
        final String availabilityId = "1";
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Interval singleInterval = new Interval(startOfDay.withTime(9, 0, 0, 0), startOfDay.withTime(17, 0, 0, 0));
        Availability availability = dataUtil.createTestAvailability(availabilityId, startOfDay, 1, singleInterval);

        final AvailabilityItem availabilityItem = converter.convert(availability);
        dataUtil.assertAvailabilityCalendarValues(availabilityItem, availability.getInterval());
        dataUtil.assertAvailableIntervals(availabilityItem, singleInterval);
    }

    @Test
    public void shouldRoundTripSingleIntervalAvailability() throws ValidationException {
        final String availabilityId = "1";
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Interval singleInterval = new Interval(startOfDay.withTime(9, 0, 0, 0), startOfDay.withTime(17, 0, 0, 0));
        Availability availability = dataUtil.createTestAvailability(availabilityId, startOfDay, 1, singleInterval);

        final AvailabilityItem availabilityItem = converter.convert(availability);
        final Availability roundTripped = converter.convert(availabilityItem);

        dataUtil.assertRoundTrip(availability, roundTripped);
    }

    @Test
    public void shouldConvertAvailabilityWithMultipleIntervals() throws ValidationException {
        final String availabilityId = "1";
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Interval firstInterval = new Interval(startOfDay.withTime(9, 0, 0, 0), startOfDay.withTime(12, 30, 0, 0));
        final Interval secondInterval = new Interval(startOfDay.withTime(13, 30, 0, 0), startOfDay.withTime(17, 0, 0, 0));
        Availability availability = dataUtil.createTestAvailability(availabilityId, startOfDay, 1, firstInterval, secondInterval);

        final AvailabilityItem availabilityItem = converter.convert(availability);
        dataUtil.assertAvailabilityCalendarValues(availabilityItem, availability.getInterval());
        dataUtil.assertAvailableIntervals(availabilityItem, firstInterval, secondInterval);
    }

    @Test
    public void shouldRoundTripMultipleIntervalAvailability() throws ValidationException {
        final String availabilityId = "1";
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Interval firstInterval = new Interval(startOfDay.withTime(9, 0, 0, 0), startOfDay.withTime(12, 30, 0, 0));
        final Interval secondInterval = new Interval(startOfDay.withTime(13, 30, 0, 0), startOfDay.withTime(17, 0, 0, 0));
        Availability availability = dataUtil.createTestAvailability(availabilityId, startOfDay, 1, firstInterval, secondInterval);

        final AvailabilityItem availabilityItem = converter.convert(availability);
        final Availability roundTripped = converter.convert(availabilityItem);

        dataUtil.assertRoundTrip(availability, roundTripped);
    }

    @Test
    public void shouldRoundTripUnavailability() {
        UnavailableIntervalDefinition unavailabilityDefinition = UnavailableIntervalDefinition.BuilderFactory.create()
                .title("Personalitate artistica")
                .start(new DateTime().withTimeAtStartOfDay())
                .duration(Duration.standardDays(8))
                .managedByUri(URI.create("urn:intelectuala"))
                .build();
        final AvailabilityItem availabilityItem = converter.convert(unavailabilityDefinition);
        final UnavailableInterval roundTripped = converter.convertUnavailability(availabilityItem);

        dataUtil.assertUnavailableInterval(unavailabilityDefinition, roundTripped);
        final Component vAvailability = availabilityItem.getAvailabilityCalendar().getComponent(COMPONENT_VAVAILABLITY);
        assertEquals("Expect managed by URI to be set on Cosmo item", unavailabilityDefinition.getManagedByUri().toString(), ICalendarUtils.getXProperty(MANAGED_BY_PROPERTY_NAME, vAvailability));
        final Property priority = vAvailability.getProperty(Property.PRIORITY);
        assertNotNull("Expect priority to be set on Cosmo item", priority);
        assertThat("Expect priority to be higher than default on Cosmo item", Integer.parseInt(priority.getValue()), allOf(greaterThan(0), lessThan(9)));
        final Property uid = vAvailability.getProperty(Property.UID);
        assertNotNull("Expect UID to be set on Cosmo item", uid);
        assertTrue("Expect UID to be non-empty", StringUtils.isNotEmpty(uid.getValue()));
    }

    @Test
    public void shouldOverlayUnavailability() {
        UnavailableIntervalDefinition unavailabilityDefinition1 = UnavailableIntervalDefinition.BuilderFactory.create()
                .title("Personalitate artistica")
                .start(new DateTime().withTimeAtStartOfDay())
                .duration(Duration.standardDays(8))
                .managedByUri(URI.create("urn:intelectuala"))
                .build();
        UnavailableIntervalDefinition unavailabilityDefinition2 = UnavailableIntervalDefinition.BuilderFactory.create()
                .title("Canalia liberala")
                .start(new DateTime().plusDays(1).withTimeAtStartOfDay())
                .duration(Duration.standardDays(6))
                .build();
        final AvailabilityItem availabilityItem = converter.convert(unavailabilityDefinition1);
        converter.overlay(availabilityItem, unavailabilityDefinition2);
        final UnavailableInterval roundTripped = converter.convertUnavailability(availabilityItem);
        dataUtil.assertUnavailableInterval(unavailabilityDefinition2, roundTripped);
    }

}