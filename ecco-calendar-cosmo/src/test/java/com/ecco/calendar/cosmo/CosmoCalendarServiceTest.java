package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.*;
import com.ecco.calendar.core.CalendarException;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import net.fortuna.ical4j.model.Calendar;
import net.fortuna.ical4j.model.*;
import net.fortuna.ical4j.model.component.VEvent;
import net.fortuna.ical4j.model.parameter.Cn;
import net.fortuna.ical4j.model.parameter.PartStat;
import net.fortuna.ical4j.model.property.Attendee;
import net.fortuna.ical4j.model.property.Priority;
import net.fortuna.ical4j.model.property.Status;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.api.Assertions;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.joda.time.DateTime;
import org.joda.time.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.osaf.cosmo.calendar.query.CalendarFilter;
import org.osaf.cosmo.calendar.query.CalendarQueryProcessor;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.filter.ItemFilter;
import org.osaf.cosmo.model.filter.NoteItemFilter;
import org.osaf.cosmo.model.hibernate.*;
import org.osaf.cosmo.service.ContentService;
import org.osaf.cosmo.service.UserService;
import org.springframework.context.ApplicationEvent;
import org.springframework.dao.DataAccessResourceFailureException;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static java.util.stream.Collectors.toSet;
import static net.fortuna.ical4j.model.Component.VAVAILABILITY;
import static net.fortuna.ical4j.model.Recur.Frequency.DAILY;
import static net.fortuna.ical4j.model.Recur.Frequency.WEEKLY;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.*;
import static org.mockito.hamcrest.MockitoHamcrest.argThat;

@SuppressWarnings({"deprecation", "ConstantConditions", "unused"})
public class CosmoCalendarServiceTest {

    private CosmoConverter entryConverter;
    private CosmoCalendarService service;
    private ContentService mockCosmoContentService;
    private UserService mockCosmoUserService;
    private CalendarQueryProcessor mockCalendarQueryProcessor;
    private EntityFactory mockEntityFactory;
    private CosmoConverter mockEntryConverter;
    private EntityUriMapper mockEntityUriMapper;
    private MessageBus<ApplicationEvent> mockMessageBus;
    private final CosmoTestDataUtil dataUtil = new CosmoTestDataUtil();
    private final HibEntityFactory entityFactory = new HibEntityFactory();
    private final HibNoteItem entryItem = new HibNoteItem();
    private final MockHibCollectionItem calendarItem = new MockHibCollectionItem();
    private final URI userRefUri = new URI("entity://HibUser/2");
    private static final String ENTRY_1_ID = "ENTRY1";
    private static final String ENTRY_2_ID = "ENTRY2";
    private static final int CAL_ITEMS_SIZE = 2;

    public CosmoCalendarServiceTest() throws URISyntaxException {
    }

    static class MockHibCollectionItem extends HibCollectionItem {
        private Set<Item> children = new HashSet<>();

        @Override
        public Set<Item> getChildren() {
            return children;
        }

        void setChildren(Set<Item> children) {
            this.children = children;
        }
    }

    @Before
    public void setup() {
        mockEntityUriMapper = mock(EntityUriMapper.class);

        mockCosmoContentService = mock(ContentService.class);
        when(mockCosmoContentService.findItemByUid(eq(ENTRY_1_ID))).thenReturn(entryItem);
        when(mockCosmoContentService.findItemByUid(eq(CosmoTestDataUtil.CAL_1_ID))).thenReturn(calendarItem);
        when(mockCosmoContentService.findItemByUid(eq(CosmoTestDataUtil.CAL_2_ID))).thenReturn(calendarItem);

        HomeCollectionItem home = mock(HomeCollectionItem.class);
        when(home.getChildByName(any())).thenReturn(mock(Item.class));
        when(mockCosmoContentService.getRootItem(any())).thenReturn(home);

        mockCosmoUserService = mock(UserService.class);
        mockCalendarQueryProcessor = mock(CalendarQueryProcessor.class);
        //noinspection unchecked
        mockMessageBus = mock(MessageBus.class);
        mockEntityFactory = mock(EntityFactory.class);
        mockEntryConverter = mock(CosmoConverter.class);

        entryConverter = new CosmoConverter(mock(SoftwareFeatureService.class), new HibEntityFactory(),
                mockEntityUriMapper, mockCosmoContentService, mockCosmoUserService);

        service = createCalendarService();

        calendarItem.setOwner(new HibUser());
    }

    @Test
    public void shouldFindEntriesFromSingleCalendar() {
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(dataUtil.createTestItems(CAL_ITEMS_SIZE, CosmoTestDataUtil.CAL_1_ID));

        DateTime dt = new DateTime();
        Interval interval = new Interval(dt.withTime(0, 0, 0, 0), dt.withTime(23, 59, 59, 999));
        when(mockEntityUriMapper.uriForEntity(any())).thenReturn(userRefUri);
        CalendarEntries calEntries = service.findEntries(CosmoTestDataUtil.CAL_1_ID, interval);
        verify(mockCosmoContentService).findItems(isA(NoteItemFilter.class));
        assertCalEntriesValues(calEntries, CosmoTestDataUtil.CAL_1_ID, interval, CAL_ITEMS_SIZE);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldFindEntriesFromMultipleCalendars() {
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(
                dataUtil.createTestItems(CAL_ITEMS_SIZE, CosmoTestDataUtil.CAL_1_ID),
                dataUtil.createTestItems(CAL_ITEMS_SIZE, CosmoTestDataUtil.CAL_2_ID)
        );

        DateTime dt = new DateTime();
        Interval interval = new Interval(dt.withTime(0, 0, 0, 0), dt.withTime(23, 59, 59, 999));
        Set<String> calendarIds = new LinkedHashSet<>(Arrays.asList(CosmoTestDataUtil.CAL_1_ID, CosmoTestDataUtil.CAL_2_ID));

        when(mockEntityUriMapper.uriForEntity(any())).thenReturn(userRefUri);
        Set<CalendarEntries> calEntriesSet = service.findEntries(calendarIds, interval);
        verify(mockCosmoContentService, times(2)).findItems(isA(NoteItemFilter.class));

        assertEquals(calendarIds.size(), calEntriesSet.size());
        Iterator<CalendarEntries> it = calEntriesSet.iterator();
        assertCalEntriesValues(it.next(), CosmoTestDataUtil.CAL_1_ID, interval, CAL_ITEMS_SIZE);
        assertCalEntriesValues(it.next(), CosmoTestDataUtil.CAL_2_ID, interval, CAL_ITEMS_SIZE);
    }

    @Test
    public void shouldReturnCalEntriesWithEmptyEntriesWhenNoCosmoItemsFound() {
        final int itemsSize = 0;
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(dataUtil.createTestItems(itemsSize, CosmoTestDataUtil.CAL_1_ID));

        final DateTime dt = new DateTime();
        Interval interval = new Interval(dt.withTime(0, 0, 0, 0), dt.withTime(23, 59, 59, 999));
        Set<String> calendarIds = new LinkedHashSet<>(Arrays.asList(CosmoTestDataUtil.CAL_1_ID, CosmoTestDataUtil.CAL_2_ID));


        Set<CalendarEntries> calEntriesSet = service.findEntries(calendarIds, interval);
        verify(mockCosmoContentService, times(2)).findItems(isA(NoteItemFilter.class));
        assertEquals(2, calEntriesSet.size());
        Iterator<CalendarEntries> it = calEntriesSet.iterator();
        assertCalEntriesValues(it.next(), CosmoTestDataUtil.CAL_1_ID, interval, itemsSize);
        assertCalEntriesValues(it.next(), CosmoTestDataUtil.CAL_2_ID, interval, itemsSize);
    }

    @Test(expected = CalendarException.class)
    public void shouldThrowCalendarExceptionForNullFind() {
        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(null);

        service.findEntry(ENTRY_2_ID);
    }

    @Test(expected = CalendarException.class)
    public void shouldThrowCalendarExceptionForFindDataAccessError() {
        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenThrow(new DataAccessResourceFailureException("mock"));

        service.findEntry(ENTRY_2_ID);
    }

    @Test
    public void shouldFindEntry() {
        when(mockEntityUriMapper.uriForEntity(any())).thenReturn(userRefUri);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID));

        Entry entry = service.findEntry(ENTRY_2_ID);
        assertEquals(ENTRY_2_ID, entry.getItemUid());
        Assert.assertEquals(CosmoTestDataUtil.OWNER_1_REF_ID, entry.getCalendarIdUserReferenceUri());
    }

    @Test
    public void shouldGetRecurringEntry() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime().plusDays(10).toDate())));
        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        // RecurringEntry entry = service.getRecurringEntry(handle);
        NoteItem item = (NoteItem) mockCosmoContentService.findItemByUid(handle.toString());
        RecurringEntry entry = entryConverter.itemToRecurringEntry(item);
        assertEquals("handle", handle, entry.getHandle());
        assertEquals("title", testItem.getDisplayName(), entry.getTitle());
        assertEquals("description", testItem.getBody(), entry.getDescription());
        assertEquals("start", new DateTime(eventStamp.getStartDate()), entry.getStart());
    }

    @Test
    public void shouldGetRecurringEntryHandleForRecurrence() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);
        final RecurrenceHandle recurrenceHandle = RecurrenceHandle.fromString(noteOccurrence.getModificationUid().toString());

        final RecurringEntryHandle entryHandle = service.getEntryHandleFromRecurrenceHandle(recurrenceHandle);
        assertEquals("handle", RecurringEntryHandle.fromString(ENTRY_2_ID), entryHandle);
    }

    @Test
    public void shouldFindRecurrenceFromExpandedEvent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(noteOccurrence));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        final Recurrence recurrence = service.findFirstRecurrence(handle, dt.plusDays(3).toLocalDate().toInterval());

        // Check that what came back was what we asked for
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
    }

    // TODO
    //  ?? why copy() it - does cosmo-core do something clean
    //  ?? how does cosmo it handle overlapping changes?
    //  check: ical uid appears to be uid transient
    //  check: look at cosmo's 'breakRecurringEvent'
    //  check: often recurrence is with setAnyTime(null)
    //  check: other properties:
    //      RDATE - Recurrence Date-Times (same level as exceptiondates)
    //      This property defines the list of DATE-TIME values for
    //      recurring events [The value type can be set to DATE or PERIOD.]
    //      setRecurrenceRules - multiple

    @Test
    @Ignore("override master entries doesn't actually work, and not likely to")
    public void shouldFindExtendedRecurrenceFromModifiedEvent() {
        // GIVEN an item
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        final NoteItem modification;
        {
            // recurring event: daily (at 8am - 9.30) for 10 days from today
            testItem.setBody("repeating entry");
            eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
            eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        }

        // WHEN modify from recurrenceIdStart (+3 days +1 hr) with a recurring event for 5 days
        final var recurrenceIdStart = dt.plusDays(3);
        {
            // 'modification' is identical, except for:
            //      - object identifiers are different, as expected with a deep clone
            //      - uid of ENTRY2 is now null, but the icalUid exists as CAL1
            //      - parentDetails has a hibcollectionitemdetail reference
            //      - stamp RRULE:FREQ=DAILY;UNTIL=20210210T080000Z has the 'Z' on the end (not floating)
            //
            modification = (NoteItem) testItem.copy();

            // 'modification' is identical, except for:
            //      - stamps array is empty
            //      - tombstones array has the stamp (object ref, but assumed)
            modification.removeStamp(StampUtils.getEventStamp(modification));
            final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
            try {
                // Need to copy Calendar, and indexes
                //  - sets stamp.eventCalendar with the snippet starting 'BEGIN:VCALENDAR'
                modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
                // However, we start clean - 'Initializes the master event using the underlying item's'
                //modStamp.createCalendar();
            } catch (Exception e) {
                throw new RuntimeException("Cannot copy calendar", e);
            }
            // recurrence event: from 3 days time for 6 days - one very long event
            modStamp.setRecurrenceId(new net.fortuna.ical4j.model.DateTime(recurrenceIdStart.toDate()));
            // overwrite the copied properties - or we can set duration which overwrites end date
            modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(recurrenceIdStart.plusHours(1).toDate()));
            // duration or end date
            modStamp.setDuration(java.time.Duration.parse("PT1H"));
            //modStamp.setEndDate(recurrenceIdEnd);
            modStamp.setStatus(StampUtils.getEventStamp(testItem).getStatus());

            // Change the event to be for 5 days from recurrence start
            modStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime(recurrenceIdStart).plusDays(5).toDate())));
            // MODIFY STAMP

            modification.addStamp(modStamp);
            modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
            modification.setName(modification.getUid());
            modification.setModifies(testItem);
            testItem.addModification(modification);
        }

        ArgumentCaptor<NoteItemFilter> filterArgumentCaptor = ArgumentCaptor.forClass(NoteItemFilter.class);
        when(mockCosmoContentService.findItems(filterArgumentCaptor.capture())).thenReturn(Collections.singleton(modification));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        final Recurrence recurrence = service.findFirstRecurrence(handle, dt.plusDays(3).toLocalDate().toInterval());

        // Check the filter matched the right UID field (icalUid - can't filter on UID since it includes the recurrence ID which is unknown)
        assertNotNull("filter icaluid", filterArgumentCaptor.getValue().getIcalUid());
        assertNull("filter uid", filterArgumentCaptor.getValue().getUid());
        // Check that what came back was what we asked for
        assertEquals("handle", new ModificationUid(testItem, new net.fortuna.ical4j.model.DateTime(recurrenceIdStart.toDate())).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3).plusHours(1), recurrence.getStart());
        // Modified start time is 9am from 8am, the duration is 1hr from 1.5hr, the end time is 10am from 9.30am - meaning an extra 30 mins from the original end date
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3).plusMinutes(30), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
    }

    @Test
    public void shouldFindRecurrenceFromModifiedEvent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());

        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 1 hour later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).plusHours(1).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        testItem.addModification(modification);

        ArgumentCaptor<NoteItemFilter> filterArgumentCaptor = ArgumentCaptor.forClass(NoteItemFilter.class);
        when(mockCosmoContentService.findItems(filterArgumentCaptor.capture())).thenReturn(Collections.singleton(modification));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        final Recurrence recurrence = service.findFirstRecurrence(handle, dt.plusDays(3).toLocalDate().toInterval());

        // Check the filter matched the right UID field (icalUid - can't filter on UID since it includes the recurrence ID which is unknown)
        assertNotNull("filter icaluid", filterArgumentCaptor.getValue().getIcalUid());
        assertNull("filter uid", filterArgumentCaptor.getValue().getUid());
        // Check that what came back was what we asked for
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3).plusHours(1), recurrence.getStart());
        // Modified start time is 9am from 8am, the duration is 1hr from 1.5hr, the end time is 10am from 9.30am - meaning an extra 30 mins from the original end date
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3).plusMinutes(30), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
    }

    @Test
    public void shouldFindRecurrencesWithoutStatusFilter() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(noteOccurrence));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);

        final Collection<Recurrence> recurrences = service.findRecurrences(handle, dt.plusDays(3).toLocalDate().toInterval(), null).collect(toSet());

        // Check that what came back was what we asked for
        assertEquals("one recurrence", 1, recurrences.size());
        final Recurrence recurrence = recurrences.iterator().next();
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
    }

    @Test
    public void shouldFindFilteredRecurrencesWithStatusFilter() throws Exception {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId1 = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final net.fortuna.ical4j.model.DateTime recurrenceId2 = new net.fortuna.ical4j.model.DateTime(dt.plusDays(5).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId1, testItem);
        final NoteItem modification = addModification(testItem, recurrenceId2, Status.VEVENT_CONFIRMED);

        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Sets.newHashSet(noteOccurrence, modification));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);

        final Collection<Recurrence> recurrences = service.findRecurrences(handle, dt.plusDays(3).toLocalDate().toInterval(), Recurrence.Status.CONFIRMED).collect(toSet());

        // Check that what came back was what we asked for
        assertEquals("one recurrence", 1, recurrences.size());
        final Recurrence recurrence = recurrences.iterator().next();
        assertEquals("handle", new ModificationUid(testItem, recurrenceId2).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(5).plusHours(1), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(5).plusMinutes(30), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(5), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.CONFIRMED, recurrence.getStatus());
    }

    @Test
    public void shouldFindRecurrencesFromExpandedEvent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(noteOccurrence));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        final Collection<Recurrence> recurrences = service.findRecurrencesFromCalendar(CosmoTestDataUtil.CAL_1_ID, dt.plusDays(3).toLocalDate().toInterval()).collect(toSet());

        // Check that what came back was what we asked for
        assertEquals("one recurrence", 1, recurrences.size());
        final Recurrence recurrence = recurrences.iterator().next();
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
    }


    @Test
    public void shouldFindRecurrencesFromModifiedEvent() throws Exception {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());

        final NoteItem modification = addModification(testItem, recurrenceId, Status.VEVENT_TENTATIVE);

        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(modification));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        final Collection<Recurrence> recurrences = service.findRecurrencesFromCalendar(CosmoTestDataUtil.CAL_1_ID, dt.plusDays(3).toLocalDate().toInterval()).collect(toSet());

        // Check that what came back was what we asked for
        assertEquals("one recurrence", 1, recurrences.size());
        final Recurrence recurrence = recurrences.iterator().next();
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3).plusHours(1), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3).plusMinutes(30), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
        assertEquals("one attendee", 1, recurrence.getAttendees().size());
        final com.ecco.calendar.core.Attendee attendee = recurrence.getAttendees().iterator().next();
        assertEquals("attendee email", dataUtil.emailForCalendarOwner(CosmoTestDataUtil.CAL_1_ID), attendee.getEmail());
        assertEquals("attendee name", dataUtil.displayNameForCalendarOwner(CosmoTestDataUtil.CAL_1_ID), attendee.getName());
        assertEquals("attendee status", com.ecco.calendar.core.Attendee.Status.ACCEPTED, attendee.getStatus());
    }

    @Test
    public void shouldFindRecurrenceUntouchedFromModifiedEvent() throws Exception {
        // GIVEN a test item recurring
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));

        // GIVEN a modification on recurrenceId
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        // Modified start time is 9am from 8am, the duration is 1hr from 1.5hr, the end time is 10am from 9.30am
        final NoteItem modification = addModification(testItem, recurrenceId, null);

        // WHEN modify the parent test item to be 3 hours
        eventStamp.setDuration(java.time.Duration.parse("PT3H"));
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        // THEN the modification has not changed
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(modification));
        final Collection<Recurrence> recurrences = service.findRecurrencesFromCalendar(CosmoTestDataUtil.CAL_1_ID, dt.plusDays(3).toLocalDate().toInterval()).collect(toSet());

        // Check that the recurrence came back was what we asked for - 10am
        assertEquals("one recurrence", 1, recurrences.size());
        final Recurrence recurrence = recurrences.iterator().next();
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3).plusHours(1), recurrence.getStart());
        // 8-9.30 original recurring entry
        // 9-10am for recurrence - moved 1 hr and 1hr duration
        // 8-11 modified recurring entry - extended from 1.5hr to 3
        // recurrence has been NOT been modified - it is still 10am
        assertEquals("end", new DateTime(eventStamp.getStartDate()).plusDays(3).plusHours(2), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("one attendee", 1, recurrence.getAttendees().size());
        final com.ecco.calendar.core.Attendee attendee = recurrence.getAttendees().iterator().next();
        assertEquals("attendee email", dataUtil.emailForCalendarOwner(CosmoTestDataUtil.CAL_1_ID), attendee.getEmail());
        assertEquals("attendee name", dataUtil.displayNameForCalendarOwner(CosmoTestDataUtil.CAL_1_ID), attendee.getName());
        assertEquals("attendee status", com.ecco.calendar.core.Attendee.Status.ACCEPTED, attendee.getStatus());
    }

    private NoteItem addModification(NoteItem testItem, net.fortuna.ical4j.model.DateTime recurrenceId, Status status) throws URISyntaxException {
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 1 hour later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(new DateTime(recurrenceId).plusHours(1).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modStamp.setStatus(status != null ? status.getValue() : null);
        ParameterList pList = new ParameterList();
        pList.add(PartStat.ACCEPTED);
        pList.add(new Cn(dataUtil.displayNameForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)));
        modStamp.getExceptionEvent().getProperties().add(new Attendee(pList, "mailto:" + dataUtil.emailForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        ((HibNoteItem) modification).addParent(testItem.getParents().iterator().next());
        testItem.addModification(modification);
        return modification;
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldFindRecurrencesFromEventWithMasterInOtherCalendar() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());

        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 1 hour later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).plusHours(1).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());

        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(modification), Collections.singleton(testItem));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        final Collection<Recurrence> recurrences = service.findRecurrencesFromCalendar(CosmoTestDataUtil.CAL_1_ID, dt.plusDays(3).toLocalDate().toInterval()).collect(toSet());

        // Check that what came back was what we asked for
        assertEquals("one recurrence", 1, recurrences.size());
        final Recurrence recurrence = recurrences.iterator().next();
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(3).plusHours(1), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(3).plusMinutes(30), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(3), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.TENTATIVE, recurrence.getStatus());
    }

    @Test
    public void shouldNotFindDroppedRecurrencesFromEventWithNoExceptionDates() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("date with exception");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        var droppedRecurrences = service.findRecurrenceExceptions(handle, dt.plusDays(13).toLocalDate().toInterval());
        assertEquals("no dropped recurrences", 0, droppedRecurrences.count());
    }

    @Test
    public void shouldFindDroppedRecurrencesFromEventWithSingleExceptionDate() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("date with exception");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(13).toDate());
        final DateList exDates = new DateList();
        exDates.add(recurrenceId); // set an exception to the daily recurring entry at 13 days
        eventStamp.setExceptionDates(exDates);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        // find an exception to the recurrence at day 13 - this represents a dropped resource
        var droppedRecurrences = service.findRecurrenceExceptions(handle, dt.plusDays(13).toLocalDate().toInterval());
        var iterator = droppedRecurrences.iterator();

        final Recurrence recurrence = iterator.next();
        assertFalse("one dropped recurrence", iterator.hasNext());
        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(13), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(13), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(13), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.DROPPED, recurrence.getStatus());
    }

    @Test
    public void shouldFindDroppedRecurrencesFromEventWithMultipleExceptionDates() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("date with exception");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(13).toDate());
        final DateList exDates = new DateList();
        exDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(11).toDate()));
        exDates.add(recurrenceId);
        exDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(19).toDate()));
        exDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(23).toDate()));
        exDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(29).toDate()));
        eventStamp.setExceptionDates(exDates);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        var droppedRecurrences = service.findRecurrenceExceptions(handle, dt.plusDays(13).toLocalDate().toInterval());
        var iterator = droppedRecurrences.iterator();

        final Recurrence recurrence = iterator.next();
        assertFalse("one dropped recurrence", iterator.hasNext());

        assertEquals("handle", new ModificationUid(testItem, recurrenceId).toString(), recurrence.getRecurrenceHandle().toString());
        assertEquals("entry handle", handle, recurrence.getRecurringEntryHandle());
        assertEquals("title", testItem.getDisplayName(), recurrence.getTitle());
        assertEquals("start", new DateTime(eventStamp.getStartDate()).plusDays(13), recurrence.getStart());
        assertEquals("end", new DateTime(eventStamp.getEndDate()).plusDays(13), recurrence.getEnd());
        assertEquals("scheduled start", new DateTime(eventStamp.getStartDate()).plusDays(13), recurrence.getScheduledStart());
        assertEquals("status", Recurrence.Status.DROPPED, recurrence.getStatus());
    }

    @Test
    public void shouldAddEntry() {
        when(mockCosmoContentService.createContent(any(HibCollectionItem.class), any(HibNoteItem.class))).thenReturn(dataUtil.createTestItem(ENTRY_1_ID, CosmoTestDataUtil.CAL_1_ID));

        when(mockEntityUriMapper.uriForEntity(any())).thenReturn(userRefUri);
        service.addEntry(CosmoTestDataUtil.CAL_1_ID, dataUtil.createTestEntry(ENTRY_1_ID, CosmoTestDataUtil.OWNER_1_REF_ID));
        verify(mockCosmoContentService).createContent(isA(HibCollectionItem.class), isA(HibNoteItem.class));
    }

    @Test(expected = CalendarException.class)
    public void shouldThrowCalendarExceptionForInvalidAdd() {
        when(mockCosmoContentService.createContent(any(HibCollectionItem.class), any(HibNoteItem.class))).thenThrow(CalendarException.class);

        service.addEntry(CosmoTestDataUtil.CAL_1_ID, dataUtil.createTestEntry(ENTRY_1_ID, CosmoTestDataUtil.OWNER_1_REF_ID));
    }

    @Test
    public void shouldCreateRecurringEntryWithVEventPopulatedCorrectly() {
        // Creating content returns its argument, with IDs populated
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.createContent(any(HibCollectionItem.class), cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        DateTime start = new DateTime().minusMinutes(30);
        int calendarDay = start.getDayOfWeek() == 7 ? 1 : start.getDayOfWeek() + 1;

        // Create a nice random entry definition
        RecurringEntryDefinition definition = RecurringEntryDefinition.BuilderFactory.create()
                .title(RandomStringUtils.randomAlphabetic(20))
                .description(RandomStringUtils.randomAlphabetic(50))
                .location(LocationDefinition.BuilderFactory.create()
                        .value(RandomStringUtils.randomAlphabetic(10))
                        .managedByUri(URI.create("don://2/"))
                        .build())
                .start(start)
                .scheduleEndDate(new LocalDate().plusDays(28))
                .duration(Duration.standardMinutes(RandomUtils.nextInt(10, 61)))
                .intervalType("WK")
                .calendarDays(ImmutableSet.of(calendarDay, RandomUtils.nextInt(1, 87), RandomUtils.nextInt(1, 8)))
                .intervalFrequency(1)
                .build();

        // we must now have the start date as part of the calendarDays for this test to work,
        // since createRecurringEntry now adjusts the start date - it would just add the start date to the recurring entry
        final RecurringEntry created = service.createRecurringEntry(CosmoTestDataUtil.CAL_1_ID, definition);
        assertNotNull("handle", created.getHandle());
        // Check that what came back was what we asked for
        assertEquals("title", definition.getTitle(), created.getTitle());
        assertEquals("description", definition.getDescription(), created.getDescription());
        assertEquals("start", definition.getStart().withMillisOfSecond(0), created.getStart());

        // Check that what we created in Cosmo was what we asked for
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertEquals("noteItem title", definition.getTitle(), noteItem.getDisplayName());
        assertEquals("noteItem description", definition.getDescription(), noteItem.getBody());
        assertEquals("noteItem id", created.getHandle().toString(), noteItem.getUid());
        final EventStamp eventStamp = StampUtils.getEventStamp(noteItem);
        final VEvent event = eventStamp.getMasterEvent();
        // TENTATIVE is used to create demand on a rota
        assertEquals("vEvent status", Status.VEVENT_TENTATIVE.getValue(), eventStamp.getStatus());
        assertEquals("vEvent title", definition.getTitle(), event.getSummary().getValue());
        assertEquals("vEvent description", definition.getDescription(), event.getDescription().getValue());
        assertNull("vEvent location", event.getProperty(Property.LOCATION));
        assertNotNull("vEvent start", eventStamp.getStartDate());
        assertEquals("vEvent start", definition.getStart().withMillisOfSecond(0), new DateTime(eventStamp.getStartDate()));
        assertNotNull("vEvent end", eventStamp.getEndDate());
        assertEquals("vEvent end", definition.getStart().plus(definition.getDuration()).withMillisOfSecond(0), new DateTime(eventStamp.getEndDate()));
        final List<Recur> recurrenceRules = eventStamp.getRecurrenceRules();
        assertEquals("vEvent recurrence rules", 1, recurrenceRules.size());
        assertEquals("vEvent weekly recurrence", WEEKLY, recurrenceRules.get(0).getFrequency());
        assertEquals("vEvent recurrence days", definition.getCalendarDays().size(), recurrenceRules.get(0).getDayList().size());
        for (Integer day : definition.getCalendarDays()) {
            assertTrue("vEvent recurs on day " + day, recurrenceRules.get(0).getDayList().contains(WeekDay.getDay(day)));
        }
        assertNotNull("vEvent recurrence until", recurrenceRules.get(0).getUntil());
        assertEquals("vEvent recurrence until", definition.getScheduleEndDate().toDateTime(definition.getStart().toLocalTime()).withMillisOfSecond(0), new DateTime(recurrenceRules.get(0).getUntil()));
    }

    @Test
    public void shouldCreateRecurringEntryWithVEventPopulatedCorrectlyWithStartAdjustedToDays() {
        // Creating content returns its argument, with IDs populated
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.createContent(any(HibCollectionItem.class), cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        // Set calendar days to 2 days after the start date
        DateTime start = new DateTime().minusMinutes(30);
        DateTime adjustedStart = start.plusDays(2);
        int isoDay = adjustedStart.getDayOfWeek();
        int calendarDay = isoDay == 7 ? 1 : isoDay + 1;

        // Create a nice random entry definition
        RecurringEntryDefinition definition = RecurringEntryDefinition.BuilderFactory.create()
                .title(RandomStringUtils.randomAlphabetic(20))
                .description(RandomStringUtils.randomAlphabetic(50))
                .location(LocationDefinition.BuilderFactory.create()
                        .value(RandomStringUtils.randomAlphabetic(10))
                        .managedByUri(URI.create("don://2/"))
                        .build())
                .start(start)
                .scheduleEndDate(new LocalDate().plusDays(28))
                .duration(Duration.standardMinutes(RandomUtils.nextInt(10, 61)))
                .intervalType("WK")
                .calendarDays(ImmutableSet.of(calendarDay))
                .intervalFrequency(1)
                .build();

        final RecurringEntry created = service.createRecurringEntry(CosmoTestDataUtil.CAL_1_ID, definition);
        assertNotNull("handle", created.getHandle());
        // Check that what came back was what we asked for
        // The start we passed in is not the start we get back - its adjusted to the recurrence of the day of week
        assertEquals("start in definition is user-defined", start, definition.getStart());
        assertEquals("start out is ical safe", adjustedStart.withMillisOfSecond(0), created.getStart());

        // Check that what we created in Cosmo was what we asked for
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp eventStamp = StampUtils.getEventStamp(noteItem);
        final VEvent event = eventStamp.getMasterEvent();
        assertNotNull("vEvent start", eventStamp.getStartDate());
        assertEquals("vEvent start", adjustedStart.withMillisOfSecond(0), new DateTime(eventStamp.getStartDate()));
        assertNotNull("vEvent end", eventStamp.getEndDate());
        assertEquals("vEvent end", adjustedStart.plus(definition.getDuration()).withMillisOfSecond(0), new DateTime(eventStamp.getEndDate()));
        final List<Recur> recurrenceRules = eventStamp.getRecurrenceRules();
        assertEquals("vEvent recurrence days", definition.getCalendarDays().size(), recurrenceRules.get(0).getDayList().size());
        for (Integer day : definition.getCalendarDays()) {
            assertTrue("vEvent recurs on day " + day, recurrenceRules.get(0).getDayList().contains(WeekDay.getDay(day)));
        }
    }

    @Test
    public void shouldUpdateNewlyCreatedRecurringEntryWithNewBounds() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("recurring with exceptions");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        final Duration duration = new Duration(new DateTime(eventStamp.getStartDate()), new DateTime(eventStamp.getEndDate()));
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.emptySet());
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        // There are no exceptions or concrete events to deal with, so narrowing recurrence bounds is no problem
        service.updateRecurringEntryBounds(handle, dt.plusDays(12).toLocalDate(), dt.plusDays(23).toLocalDate());

        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        assertNotNull("vEvent start", stamp.getStartDate());
        assertEquals("vEvent start", dt.plusDays(12).withMillisOfSecond(0), new DateTime(stamp.getStartDate()));
        assertNotNull("vEvent end", stamp.getEndDate());
        assertEquals("vEvent end", dt.plusDays(12).plus(duration), new DateTime(stamp.getEndDate()));
        final List<Recur> recurrenceRules = stamp.getRecurrenceRules();
        assertEquals("vEvent recurrence rules", 1, recurrenceRules.size());
        assertNotNull("vEvent recurrence until", recurrenceRules.get(0).getUntil());
        assertEquals("vEvent recurrence until", dt.plusDays(23).withMillisOfSecond(0), new DateTime(recurrenceRules.get(0).getUntil()));
    }

    @Test
    public void shouldUpdateRecurringEntryBoundsWithNoConflict() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("recurring with exceptions");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        final Duration duration = new Duration(new DateTime(eventStamp.getStartDate()), new DateTime(eventStamp.getEndDate()));
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());

        // Create a modification where the recurrence ID is inside the bounds but the actual start date is outside.
        // It should update happily.
        // The bounds of the updateRecurringEntryBounds below are from 12 days to 23 days, so this recurrenceId is inside at 23 days.
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(23).toDate());
        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 36 hours later and be 1 hour long
        // NB the recurrenceId is after the start date
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(24).plusHours(12).toDate()));
        // NB the original testItem is 1.5hr (see START and END) and the test below checks for 'vEvent end' at 1.5hr
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        testItem.addModification(modification);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(modification));
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        // There are concrete recurrences (but no exception dates) to deal with, but narrowing recurrence bounds is no problem
        // even though the modified start date is after this end date
        service.updateRecurringEntryBounds(handle, dt.plusDays(12).toLocalDate(), dt.plusDays(23).toLocalDate());

        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        assertNotNull("vEvent start", stamp.getStartDate());
        assertEquals("vEvent start", dt.plusDays(12).withMillisOfSecond(0), new DateTime(stamp.getStartDate()));
        assertNotNull("vEvent end", stamp.getEndDate());
        assertEquals("vEvent end", dt.plusDays(12).plus(duration), new DateTime(stamp.getEndDate()));
        final List<Recur> recurrenceRules = stamp.getRecurrenceRules();
        assertEquals("vEvent recurrence rules", 1, recurrenceRules.size());
        assertNotNull("vEvent recurrence until", recurrenceRules.get(0).getUntil());
        assertEquals("vEvent recurrence until", dt.plusDays(23).withMillisOfSecond(0), new DateTime(recurrenceRules.get(0).getUntil()));
    }

    @Test
    public void shouldUpdateRecurringEntryIfConcreteRecurrenceOutsideBoundsStart() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("recurring with exceptions");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());

        // Create a modification where the recurrence ID is outside the bounds but the actual start date is inside.
        // The bounds of the updateRecurringEntryBounds are from 12 days to 30 days, so this recurrenceId is outside at 11 days.
        // It should still refuse to update - see CosmoCalendarService.updateRecurringEntryBounds
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(11).toDate());
        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 36 hours later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(12).plusHours(12).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        testItem.addModification(modification);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(modification));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        // There are concrete recurrences (but no exception dates) to deal with, and narrowing recurrence bounds causes a problem
        // because its before the new start date.
        service.updateRecurringEntryBounds(handle, dt.plusDays(12).toLocalDate(), dt.plusDays(30).toLocalDate());

        // TODO: Verify that correct things happened
    }

    @Test
    public void shouldUpdateRecurringEntryIfConcreteRecurrenceOutsideBoundsEnd() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("recurring with exceptions");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());

        // Create a modification where the recurrence ID is outside the bounds but the actual start date is inside.
        // It should still refuse to update.
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(25).toDate());
        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 36 hours later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(22).plusHours(12).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        testItem.addModification(modification);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.singleton(modification));

        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);
        service.updateRecurringEntryBounds(handle, dt.toLocalDate(), dt.plusDays(23).toLocalDate());

        // TODO: Verify that correct things happened
    }

    @Test
    public void shouldRemoveRedundantExceptionDatesWhenUpdatingRecurringEntryBounds() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("recurring with exceptions");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(30).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final List<net.fortuna.ical4j.model.DateTime> redundantExDates = new ArrayList<>();
        final List<net.fortuna.ical4j.model.DateTime> usefulExDates = new ArrayList<>();
        redundantExDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(11).toDate()));
        usefulExDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(13).toDate()));
        usefulExDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(19).toDate()));
        usefulExDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(23).toDate()));
        redundantExDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(29).toDate()));
        final DateList exDates = new DateList();
        exDates.addAll(usefulExDates);
        exDates.addAll(redundantExDates);
        eventStamp.setExceptionDates(exDates);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);
        when(mockCosmoContentService.findItems(any(ItemFilter.class))).thenReturn(Collections.emptySet());
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());
        final RecurringEntryHandle handle = RecurringEntryHandle.fromString(ENTRY_2_ID);

        service.updateRecurringEntryBounds(handle, dt.plusDays(12).toLocalDate(), dt.plusDays(23).toLocalDate());
        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        final Matcher<Iterable<net.fortuna.ical4j.model.Date>> hasRedundantDates = hasItems(redundantExDates.toArray(new net.fortuna.ical4j.model.Date[0]));
        final Matcher<Iterable<net.fortuna.ical4j.model.Date>> hasUsefulDates = hasItems(usefulExDates.toArray(new net.fortuna.ical4j.model.Date[0]));

        assertThat("vEvent exception dates", stamp.getExceptionDates(),
                allOf(not(hasRedundantDates), hasUsefulDates));

    }

    @Test
    public void shouldConfirmRecurrenceFromExpandedEventWithVEventPopulatedCorrectlyAndMultipleParents() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime().plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);

        when(mockCosmoContentService.findItemByUid(noteOccurrence.getUid())).thenReturn(noteOccurrence);
        when(mockCosmoContentService.findItemByUid(CosmoTestDataUtil.CAL_1_ID)).thenReturn(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_1_ID));
        when(mockCosmoContentService.findItemByUid(CosmoTestDataUtil.CAL_2_ID)).thenReturn(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_2_ID));
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.createContent(any(HibCollectionItem.class), any(HibNoteItem.class))).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());
        when(mockEntityUriMapper.uriForEntity(eq(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_1_ID).getOwner()))).thenReturn(URI.create(dataUtil.entityUriForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)));
        // second attendee is set as an additional parent
        when(mockEntityUriMapper.uriForEntity(eq(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_2_ID).getOwner()))).thenReturn(URI.create(dataUtil.entityUriForCalendarOwner(CosmoTestDataUtil.CAL_2_ID)));

        // set CAL_2_ID as CONFIRMED for the event
        service.confirmRecurrence(RecurrenceHandle.fromString(noteOccurrence.getUid()), null, CosmoTestDataUtil.CAL_2_ID);

        // Check we created a new item as well as the update
        verify(mockCosmoContentService).createContent(any(HibCollectionItem.class), any(HibNoteItem.class));

        // Check that what we created in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        // parents tested as parents
        assertThat("noteItem parents", noteItem.getParents(), Matchers.containsInAnyOrder(CosmoTestFixtureUtil.collectionWithUid(CosmoTestDataUtil.CAL_1_ID), CosmoTestFixtureUtil.collectionWithUid(CosmoTestDataUtil.CAL_2_ID)));
        assertEquals("noteItem title", testItem.getDisplayName(), noteItem.getDisplayName());
        assertEquals("noteItem description", testItem.getBody(), noteItem.getBody());
        assertEquals("noteItem iCalId", testItem.getIcalUid(), noteItem.getIcalUid());
        assertEquals("noteItem uid", noteOccurrence.getModificationUid().toString(), noteItem.getUid());
        assertEquals("noteItem modifies", testItem, noteItem.getModifies());
        final EventExceptionStamp exStamp = StampUtils.getEventExceptionStamp(noteItem);
        final VEvent event = exStamp.getExceptionEvent();
        assertEquals("vEvent status", Status.VEVENT_CONFIRMED.getValue(), exStamp.getStatus());
        assertEquals("vEvent uid", eventStamp.getIcalUid(), exStamp.getIcalUid());
        assertEquals("vEvent title", eventStamp.getMasterEvent().getSummary().getValue(), event.getSummary().getValue());
        assertEquals("vEvent description", eventStamp.getMasterEvent().getDescription().getValue(), event.getDescription().getValue());
        assertEquals("vEvent location", eventStamp.getLocation(), exStamp.getLocation());
        assertNotNull("vEvent start", exStamp.getStartDate());
        assertEquals("vEvent start", new DateTime(recurrenceId), new DateTime(exStamp.getStartDate()));
        assertNotNull("vEvent end", exStamp.getEndDate());
        assertEquals("vEvent end", new DateTime(recurrenceId).plus(new Duration(new DateTime(eventStamp.getStartDate()), new DateTime(eventStamp.getEndDate()))), new DateTime(exStamp.getEndDate()));
        assertEquals("vEvent recurrence rules", 0, exStamp.getRecurrenceRules().size());
        assertNull("vEvent recurrence dates", exStamp.getRecurrenceDates());
        assertEquals("vEvent recurrence ID", recurrenceId, exStamp.getRecurrenceId());
        final Matcher<Property> attendee1 = Matchers.allOf(CosmoTestFixtureUtil.attendeeWithEmail(dataUtil.emailForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)), CosmoTestFixtureUtil.attendeeWithCommonName(dataUtil.displayNameForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)), CosmoTestFixtureUtil.attendeeWithDirectoryEntryReference(dataUtil.entityUriForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)));
        final Matcher<Property> attendee2 = Matchers.allOf(CosmoTestFixtureUtil.attendeeWithEmail(dataUtil.emailForCalendarOwner(CosmoTestDataUtil.CAL_2_ID)), CosmoTestFixtureUtil.attendeeWithCommonName(dataUtil.displayNameForCalendarOwner(CosmoTestDataUtil.CAL_2_ID)), CosmoTestFixtureUtil.attendeeWithDirectoryEntryReference(dataUtil.entityUriForCalendarOwner(CosmoTestDataUtil.CAL_2_ID)));
        // parents tested as attendees
        assertThat("vEvent attendees", (Iterable<? extends Property>) event.getProperties(Property.ATTENDEE), containsInAnyOrder(attendee1, attendee2));
    }

    @Test
    public void shouldNotUnconfirmRecurrenceFromExpandedEvent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime().plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);

        when(mockCosmoContentService.findItemByUid(noteOccurrence.getUid())).thenReturn(noteOccurrence);

        URI updatedBy = URI.create("test://something/");
        service.unconfirmRecurrence(RecurrenceHandle.fromString(noteOccurrence.getUid()), updatedBy, false, CosmoTestDataUtil.CAL_2_ID);

        // Check we didn't create or update anything.
        verify(mockCosmoContentService).findItemByUid(noteOccurrence.getUid());
        verifyNoMoreInteractions(mockCosmoContentService);
    }

    @Test
    public void shouldUnconfirmRecurrenceFromConcreteEventWithVEventPopulatedCorrectlyAndSingleParent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());

        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 1 hour later and be 1 hour long, add to an extra calendar, and add attendees
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).plusHours(1).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        ((HibNoteItem) modification).addParent(testItem.getParents().iterator().next());
        ((HibNoteItem) modification).addParent(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_2_ID));
        testItem.addModification(modification);

        when(mockCosmoContentService.findItemByUid(modification.getUid())).thenReturn(modification);
        when(mockCosmoContentService.findItemByUid(CosmoTestDataUtil.CAL_1_ID)).thenReturn(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_1_ID));
        when(mockCosmoContentService.findItemByUid(CosmoTestDataUtil.CAL_2_ID)).thenReturn(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_2_ID));
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());
        when(mockEntityUriMapper.uriForEntity(eq(dataUtil.createTestCollectionItem(CosmoTestDataUtil.CAL_1_ID).getOwner()))).thenReturn(URI.create(dataUtil.entityUriForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)));

        URI updatedBy = URI.create("test://something/");
        service.unconfirmRecurrence(RecurrenceHandle.fromString(modification.getUid()), updatedBy,false, CosmoTestDataUtil.CAL_2_ID);

        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertThat("noteItem parents", noteItem.getParents(), Matchers.contains(CosmoTestFixtureUtil.collectionWithUid(CosmoTestDataUtil.CAL_1_ID)));
        assertEquals("noteItem iCalId", testItem.getIcalUid(), noteItem.getIcalUid());
        assertEquals("noteItem uid", modification.getUid(), noteItem.getUid());
        assertEquals("noteItem modifies", testItem, noteItem.getModifies());
        final EventExceptionStamp exStamp = StampUtils.getEventExceptionStamp(noteItem);
        final VEvent event = exStamp.getExceptionEvent();
        assertEquals("vEvent status", Status.VEVENT_TENTATIVE.getValue(), exStamp.getStatus());
        assertEquals("vEvent uid", eventStamp.getIcalUid(), exStamp.getIcalUid());
        // each attendee Parameter.DIR is matched against the calendarId as 'entityUriFor://CalendarOwner/" + calendarId'
        final Matcher<Property> attendee1 = Matchers.allOf(CosmoTestFixtureUtil.attendeeWithEmail(dataUtil.emailForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)), CosmoTestFixtureUtil.attendeeWithCommonName(dataUtil.displayNameForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)), CosmoTestFixtureUtil.attendeeWithDirectoryEntryReference(dataUtil.entityUriForCalendarOwner(CosmoTestDataUtil.CAL_1_ID)));
        assertThat("vEvent attendees", (Iterable<? extends Property>)event.getProperties(Property.ATTENDEE), contains(attendee1));
    }

    @Test
    public void shouldRescheduleRecurrenceFromExpandedEventWithVEventPopulatedCorrectly() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime().plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final DateTime rescheduledDt = new DateTime(dt.plusDays(3).minusHours(1));
        final int rescheduledDuration = (int) (eventStamp.getDuration().get(ChronoUnit.SECONDS)/60 + 120);

        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);

        when(mockCosmoContentService.findItemByUid(noteOccurrence.getUid())).thenReturn(noteOccurrence);
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.createContent(any(HibCollectionItem.class), any(HibNoteItem.class))).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        service.editRecurrence(RecurrenceHandle.fromString(noteOccurrence.getUid()), null, rescheduledDt, rescheduledDuration, null);

        // Check we created a new item as well as the update
        verify(mockCosmoContentService).createContent(any(HibCollectionItem.class), any(HibNoteItem.class));

        // Check that what we created in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        assertThat("noteItem parents", noteItem.getParents(), Matchers.contains(CosmoTestFixtureUtil.collectionWithUid(CosmoTestDataUtil.CAL_1_ID)));
        assertEquals("noteItem title", testItem.getDisplayName(), noteItem.getDisplayName());
        assertEquals("noteItem description", testItem.getBody(), noteItem.getBody());
        assertEquals("noteItem iCalId", testItem.getIcalUid(), noteItem.getIcalUid());
        assertEquals("noteItem uid", noteOccurrence.getModificationUid().toString(), noteItem.getUid());
        assertEquals("noteItem modifies", testItem, noteItem.getModifies());
        final EventExceptionStamp exStamp = StampUtils.getEventExceptionStamp(noteItem);
        final VEvent event = exStamp.getExceptionEvent();
        assertNotEquals("vEvent status", Status.VEVENT_CONFIRMED.getValue(), exStamp.getStatus());
        assertEquals("vEvent uid", eventStamp.getIcalUid(), exStamp.getIcalUid());
        assertEquals("vEvent title", eventStamp.getMasterEvent().getSummary().getValue(), event.getSummary().getValue());
        assertEquals("vEvent description", eventStamp.getMasterEvent().getDescription().getValue(), event.getDescription().getValue());
        assertEquals("vEvent location", eventStamp.getLocation(), exStamp.getLocation());
        assertNotNull("vEvent start", exStamp.getStartDate());
        assertEquals("vEvent start", rescheduledDt, new DateTime(exStamp.getStartDate()));
        assertNotNull("vEvent end", exStamp.getEndDate());
        assertEquals("vEvent end", new Dur(0, 0, rescheduledDuration, 0).getTime(exStamp.getStartDate()), exStamp.getEndDate());
        assertEquals("vEvent recurrence rules", 0, exStamp.getRecurrenceRules().size());
        assertNull("vEvent recurrence dates", exStamp.getRecurrenceDates());
        assertEquals("vEvent recurrence ID", recurrenceId, exStamp.getRecurrenceId());
    }

    @Test
    public void shouldRemoveModifiedRecurrenceWhenResettingIt() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());

        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 1 hour later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).plusHours(1).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        testItem.addModification(modification);

        when(mockCosmoContentService.findItemByUid(modification.getUid())).thenReturn(modification);

        service.resetRecurrence(RecurrenceHandle.fromString(modification.getUid()));

        verify(mockCosmoContentService).removeContent(modification);
    }

    @Test
    public void shouldCreateExceptionDateWhenDroppingRecurrenceFromExpandedEvent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("jacob's creaked");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime().plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(4).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);

        when(mockCosmoContentService.findItemByUid(noteOccurrence.getUid())).thenReturn(noteOccurrence);
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        service.dropRecurrence(RecurrenceHandle.fromString(noteOccurrence.getUid()));

        verify(mockCosmoContentService).findItemByUid(noteOccurrence.getUid());
        verify(mockCosmoContentService).updateContent(testItem);
        verifyNoMoreInteractions(mockCosmoContentService);

        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        Assertions.assertThat(stamp.getExceptionDates()).contains(recurrenceId);
    }

    @Test
    public void shouldAddToExistingExceptionDatesWhenDroppingRecurrenceFromExpandedEvent() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("jacob's creaked");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(new DateTime().plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final DateList existingDates = new DateList();
        final net.fortuna.ical4j.model.DateTime existingExDate = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        existingDates.add(existingExDate);
        eventStamp.setExceptionDates(existingDates);
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(4).toDate());
        final NoteOccurrence noteOccurrence = NoteOccurrenceUtil.createNoteOccurrence(recurrenceId, testItem);

        when(mockCosmoContentService.findItemByUid(noteOccurrence.getUid())).thenReturn(noteOccurrence);
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        service.dropRecurrence(RecurrenceHandle.fromString(noteOccurrence.getUid()));

        verify(mockCosmoContentService).findItemByUid(noteOccurrence.getUid());
        verify(mockCosmoContentService).updateContent(testItem);
        verifyNoMoreInteractions(mockCosmoContentService);

        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        Assertions.assertThat(stamp.getExceptionDates()).describedAs("vEvent exception dates").contains(existingExDate, recurrenceId);
    }

    @Test
    public void shouldRemoveModifiedRecurrenceWhenDroppingIfItExists() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());

        final NoteItem modification = (NoteItem) testItem.copy();
        // Swap the event stamp for an event exception stamp
        modification.removeStamp(StampUtils.getEventStamp(modification));
        final EventExceptionStamp modStamp = entityFactory.createEventExceptionStamp(modification);
        // Need to copy Calendar, and indexes
        try {
            modStamp.setEventCalendar(new Calendar(eventStamp.getEventCalendar()));
        } catch (Exception e) {
            throw new RuntimeException("Cannot copy calendar", e);
        }
        modStamp.setRecurrenceId(recurrenceId);
        // Change the event to start 1 hour later and be 1 hour long
        modStamp.setStartDate(new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).plusHours(1).toDate()));
        modStamp.setDuration(java.time.Duration.parse("PT1H"));
        modification.addStamp(modStamp);
        modification.setUid(new ModificationUid(testItem, modStamp.getRecurrenceId()).toString());
        modification.setName(modification.getUid());
        modification.setModifies(testItem);
        testItem.addModification(modification);

        when(mockCosmoContentService.findItemByUid(modification.getUid())).thenReturn(modification);
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        service.dropRecurrence(RecurrenceHandle.fromString(modification.getUid()));

        verify(mockCosmoContentService).removeContent(modification);
        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        assertThat("vEvent exception dates", stamp.getExceptionDates(), containsInAnyOrder(recurrenceId));
    }

    @Test
    public void shouldRemoveExceptionDateWhenReinstatingRecurrence() {
        final NoteItem testItem = dataUtil.createTestItem(ENTRY_2_ID, CosmoTestDataUtil.CAL_1_ID);
        testItem.setBody("my lovely body");
        final EventStamp eventStamp = StampUtils.getEventStamp(testItem);
        final DateTime dt = new DateTime(eventStamp.getStartDate());
        eventStamp.setRecurrenceRule(new Recur(DAILY, new net.fortuna.ical4j.model.DateTime(dt.plusDays(10).toDate())));
        eventStamp.setStatus(Status.VEVENT_TENTATIVE.getValue());
        final net.fortuna.ical4j.model.DateTime recurrenceId = new net.fortuna.ical4j.model.DateTime(dt.plusDays(3).toDate());
        final DateList exDates = new DateList();
        exDates.add(recurrenceId);
        exDates.add(new net.fortuna.ical4j.model.DateTime(dt.plusDays(7).toDate()));
        eventStamp.setExceptionDates(exDates);

        when(mockCosmoContentService.findItemByUid(eq(ENTRY_2_ID))).thenReturn(testItem);
        ArgumentCaptor<HibNoteItem> cosmoItem = ArgumentCaptor.forClass(HibNoteItem.class);
        when(mockCosmoContentService.updateContent(cosmoItem.capture())).thenAnswer(CosmoTestFixtureUtil.persistedNoteItem());

        service.reinstateRecurrence(RecurrenceHandle.fromString(new ModificationUid(testItem, recurrenceId).toString()));
        // Check that what we updated in Cosmo was what we expected
        final HibNoteItem noteItem = cosmoItem.getValue();
        final EventStamp stamp = StampUtils.getEventStamp(noteItem);
        assertThat("vEvent exception dates", stamp.getExceptionDates(),
                not(contains(recurrenceId)));
    }


    @Test
    public void shouldEditEntry() {

        service.editEntry(dataUtil.createTestEntry(ENTRY_1_ID, CosmoTestDataUtil.CAL_1_ID));
        verify(mockCosmoContentService).updateContent(isA(HibNoteItem.class));
    }

    @Test(expected = CalendarException.class)
    public void shouldThrowCalendarExceptionForInvalidEdit() {
        when(mockCosmoContentService.updateContent(any(HibNoteItem.class))).thenThrow(CalendarException.class);

        service.editEntry(dataUtil.createTestEntry(ENTRY_1_ID, CosmoTestDataUtil.CAL_1_ID));
    }

    @Test
    public void shouldDeleteEntry() {

        service.deleteEntry(ENTRY_1_ID);
        verify(mockCosmoContentService).removeItem(isA(HibNoteItem.class));
    }

    @Test(expected = CalendarException.class)
    public void shouldThrowCalendarExceptionForInvalidDelete() {
        doThrow(CalendarException.class).when(mockCosmoContentService).removeItem(any(HibNoteItem.class));

        service.deleteEntry(ENTRY_1_ID);
    }

    @Test
    public void shouldCreateAvailabilityRecordWhenNoExistingAvailabilityRecordsFound() {
        when(mockCalendarQueryProcessor.filterQuery(same(calendarItem), any(CalendarFilter.class))).thenReturn(Collections.emptySet());
        when(mockCosmoContentService.createContent(same(calendarItem), any(HibAvailabilityItem.class))).thenReturn(new HibAvailabilityItem());

        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_1_ID, startOfDay, 1,
                new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18)));
        service.updateAvailability(testAvailability);
    }

    @Test
    public void shouldCreateAvailabilityRecordWhenBlocking() {
        service = createCalendarServiceUnit(); // True unit test.

        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final UnavailableIntervalDefinition definition = dataUtil.createUnavailableInterval("Gheorghe Eminovici", startOfDay, Duration.standardDays(1));
        final HibAvailabilityItem availabilityItem = new HibAvailabilityItem();
        final HibAvailabilityItem savedItem = new HibAvailabilityItem();
        final UnavailableInterval savedEntity = mock(UnavailableInterval.class);

        when(mockEntryConverter.convert(same(definition))).thenReturn(availabilityItem);
        when(mockCosmoContentService.createContent(same(calendarItem), same(availabilityItem))).thenReturn(savedItem);
        when(mockEntryConverter.convertUnavailability(same(savedItem))).thenReturn(savedEntity);

        assertSame("Expected converted saved entity", savedEntity, service.blockAvailability(CosmoTestDataUtil.CAL_1_ID, definition));
    }

    @Test
    public void shouldUpdateUnavailabilityRecordWhenAsked() {
        service = createCalendarServiceUnit();

        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final UnavailableIntervalDefinition definition = dataUtil.createUnavailableInterval("Eminescu trebuie", startOfDay, Duration.standardDays(1));
        final HibAvailabilityItem availabilityItem = new HibAvailabilityItem();
        final HibAvailabilityItem savedItem = new HibAvailabilityItem();
        final UnavailableInterval savedEntity = mock(UnavailableInterval.class);

        when(mockCosmoContentService.findItemByUid(ENTRY_1_ID)).thenReturn(availabilityItem);
        when(mockCosmoContentService.updateContent(same(availabilityItem))).thenReturn(savedItem);
        when(mockEntryConverter.convertUnavailability(same(savedItem))).thenReturn(savedEntity);

        final UnavailableInterval result = service.updateUnavailableInterval(UnavailableInterval.Handle.fromString(ENTRY_1_ID), definition);

        verify(mockEntryConverter).overlay(same(availabilityItem), same(definition));
        assertSame("Expected converted, saved, updated entity", savedEntity, result);
    }

    @Test
    public void shouldRemoveAvailabilityRecordWhenReinstating() {
        service = createCalendarServiceUnit(); // True unit test.

        final HibAvailabilityItem availabilityItem = new HibAvailabilityItem();
        when(mockCosmoContentService.findItemByUid(ENTRY_1_ID)).thenReturn(availabilityItem);

        service.reinstateAvailability(UnavailableInterval.Handle.fromString(ENTRY_1_ID));

        verify(mockCosmoContentService).removeItem(same(availabilityItem));
    }

    @Test
    public void shouldReplaceAvailabilityRecordWhenExactMatchingIntervalFound() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, startOfDay, 1, new Interval(startOfDay, startOfDay.plusDays(1))));

        calendarItem.setChildren(Collections.singleton(existingItem));
        when(mockCosmoContentService.createContent(same(calendarItem), any(HibAvailabilityItem.class))).thenReturn(new HibAvailabilityItem());

        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_2_ID, startOfDay, 1,
                new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18)));
        service.updateAvailability(testAvailability);

        verify(mockCosmoContentService).removeItem(same(existingItem));
        verify(mockCosmoContentService).createContent(same(calendarItem), any(HibAvailabilityItem.class));
    }

    @Test
    public void shouldNotReplaceAvailabilityRecordWhenItIsOfHigherPriority() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, startOfDay, 1, new Interval(startOfDay, startOfDay.plusDays(1))));
        existingItem.getAvailabilityCalendar().getComponent(VAVAILABILITY).getProperties().add(Priority.MEDIUM);

        calendarItem.setChildren(Collections.singleton(existingItem));
        when(mockCosmoContentService.createContent(same(calendarItem), any(HibAvailabilityItem.class))).thenReturn(new HibAvailabilityItem());

        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_2_ID, startOfDay, 1,
                new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18)));
        service.updateAvailability(testAvailability);

        verify(mockCosmoContentService, never()).removeItem(same(existingItem));
        verify(mockCosmoContentService).createContent(same(calendarItem), any(HibAvailabilityItem.class));
    }

    @Test
    public void shouldReplaceAvailabilityRecordsWhenCoveringMultipleExistingOnes() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime tomorrow = startOfDay.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, new Interval(tomorrow, tomorrow.plusDays(1))));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, new Interval(theDayAfter, theDayAfter.plusDays(1))));

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2)));
        when(mockCosmoContentService.createContent(same(calendarItem), any(HibAvailabilityItem.class))).thenReturn(new HibAvailabilityItem());

        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_2_ID, startOfDay, 3,
                new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18)),
                new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18)),
                new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(18)));
        service.updateAvailability(testAvailability);

        verify(mockCosmoContentService).removeItem(same(existingItem1));
        verify(mockCosmoContentService).removeItem(same(existingItem2));
        final Matcher<AvailabilityItem> meetsAllExpectations = Matchers.allOf(
                CosmoTestFixtureUtil.coversInterval(new Interval(startOfDay, theDayAfter.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(18)))
        );
        verify(mockCosmoContentService).createContent(same(calendarItem), argThat(meetsAllExpectations));
    }

    @Test
    public void shouldSplitExistingAvailabilityWhenFullyOverlappingWithUpdatedOne() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime yesterday = startOfDay.minusDays(1);
        final DateTime tomorrow = startOfDay.plusDays(1);

        // Replace the middle day of a 3-day availability calendar
        AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, yesterday, 3,
                new Interval(yesterday.plusHours(9), startOfDay.plusHours(18)), // Overnighter!
                new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18))));
        existingItem.setIcalUid(existingItem.getUid());
        calendarItem.setChildren(Collections.singleton(existingItem));

        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_2_ID, startOfDay, 1,
                new Interval(startOfDay.plusHours(10), startOfDay.plusHours(16)));
        service.updateAvailability(testAvailability);

        final Matcher<AvailabilityItem> expectedUpdates = Matchers.allOf(
                sameInstance(existingItem),
                CosmoTestFixtureUtil.coversInterval(new Interval(yesterday, yesterday.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(yesterday.plusHours(9), startOfDay)),
                Matchers.not(CosmoTestFixtureUtil.containsAvailability(new Interval(yesterday.plusHours(9), startOfDay.plusHours(18)))),
                Matchers.not(CosmoTestFixtureUtil.containsAvailability(new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18))))
        );
        verify(mockCosmoContentService).updateContent(argThat(expectedUpdates));
        Matcher<AvailabilityItem> meetsAllContentExpectations = Matchers.allOf(
                CosmoTestFixtureUtil.coversInterval(new Interval(startOfDay, startOfDay.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(startOfDay.plusHours(10), startOfDay.plusHours(16))),
                CosmoTestFixtureUtil.hasNoICalUid()
        );
        verify(mockCosmoContentService).createContent(same(calendarItem), argThat(meetsAllContentExpectations));
        meetsAllContentExpectations = Matchers.allOf(
                CosmoTestFixtureUtil.coversInterval(new Interval(tomorrow, tomorrow.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18))),
                CosmoTestFixtureUtil.hasNoICalUid()
        );
        verify(mockCosmoContentService).createContent(same(calendarItem), argThat(meetsAllContentExpectations));
    }

    @Test
    public void shouldTruncateExistingAvailabilityWhenOverlappingStart() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime yesterday = startOfDay.minusDays(1);

        // Replace the second day of a 2-day availability calendar
        AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, yesterday, 2,
                new Interval(yesterday.plusHours(9), yesterday.plusHours(18)),
                new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18))));

        calendarItem.setChildren(Collections.singleton(existingItem));

        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_2_ID, startOfDay, 1,
                new Interval(startOfDay.plusHours(10), startOfDay.plusHours(16)));
        service.updateAvailability(testAvailability);

        final Matcher<AvailabilityItem> expectedUpdates = Matchers.allOf(
                sameInstance(existingItem),
                CosmoTestFixtureUtil.coversInterval(new Interval(yesterday, yesterday.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(yesterday.plusHours(9), yesterday.plusHours(18))),
                Matchers.not(CosmoTestFixtureUtil.containsAvailability(new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18)))));
        verify(mockCosmoContentService).updateContent(argThat(expectedUpdates));
        final Matcher<AvailabilityItem> meetsAllContentExpectations = Matchers.allOf(
                CosmoTestFixtureUtil.coversInterval(new Interval(startOfDay, startOfDay.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(startOfDay.plusHours(10), startOfDay.plusHours(16)))
        );
        verify(mockCosmoContentService).createContent(same(calendarItem), argThat(meetsAllContentExpectations));
    }

    @Test
    public void shouldTruncateExistingAvailabilityWhenOverlappingEnd() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime tomorrow = startOfDay.plusDays(1);

        // Replace the first day of a 2-day availability calendar
        AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, startOfDay, 2,
                new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18)),
                new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18))));

        calendarItem.setChildren(Collections.singleton(existingItem));

        final Availability testAvailability = dataUtil.createTestAvailability(ENTRY_2_ID, startOfDay, 1,
                new Interval(startOfDay.plusHours(10), startOfDay.plusHours(16)));
        service.updateAvailability(testAvailability);

        final Matcher<AvailabilityItem> expectedUpdates = Matchers.allOf(
                sameInstance(existingItem),
                CosmoTestFixtureUtil.coversInterval(new Interval(tomorrow, tomorrow.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(tomorrow.plusHours(9), tomorrow.plusHours(18))),
                Matchers.not(CosmoTestFixtureUtil.containsAvailability(new Interval(startOfDay.plusHours(9), startOfDay.plusHours(18))))
        );
        verify(mockCosmoContentService).updateContent(argThat(expectedUpdates));
        final Matcher<AvailabilityItem> meetsAllContentExpectations = Matchers.allOf(
                CosmoTestFixtureUtil.coversInterval(new Interval(startOfDay, startOfDay.plusDays(1))),
                CosmoTestFixtureUtil.containsAvailability(new Interval(startOfDay.plusHours(10), startOfDay.plusHours(16)))
        );
        verify(mockCosmoContentService).createContent(same(calendarItem), argThat(meetsAllContentExpectations));
    }

    @Test
    public void shouldReturnEmptyAvailabilityWhenNoCosmoItemsFound() {
        when(mockCalendarQueryProcessor.filterQuery(same(calendarItem), any(CalendarFilter.class))).thenReturn(Collections.emptySet());

        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Interval interval = new Interval(startOfDay, startOfDay.plusDays(1));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, interval, true);
        Assert.assertEquals("Expect availability to be for correct calendar ID", CosmoTestDataUtil.CAL_1_ID, availability.getCalendarId());
        assertEquals("Expect availability to cover interval requested", interval, availability.getInterval());
        assertEquals("Expect no availability", 0, availability.getAvailableIntervals().size());
    }

    @Test
    public void shouldReturnAvailabilityWhenOneCosmoItemFound() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final Interval interval = new Interval(startOfDay, startOfDay.plusDays(1));
        final Interval availableInterval1 = new Interval(startOfDay.plusHours(9), startOfDay.plusHours(17));

        final AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, startOfDay, 1,
                availableInterval1));
        calendarItem.setChildren(Collections.singleton(existingItem));
//        when(mockCalendarQueryProcessor.filterQuery(same(calendarItem), any(CalendarFilter.class))).thenReturn(Collections.<ICalendarItem>singleton(existingItem));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, interval, true);
        assertEquals("Expect availability to cover interval requested", interval, availability.getInterval());
        assertEquals("Expect availability", 1, availability.getAvailableIntervals().size());
        assertEquals(availableInterval1, availability.getAvailableIntervals().iterator().next().getInterval());
    }

    @Test
    public void shouldReturnEmptyAvailabilityWhenWholeIntervalOverridden() {
        final DateTime today = new DateTime().withTimeAtStartOfDay();
        final DateTime tomorrow = today.plusDays(1);
        final Interval allDay = new Interval(tomorrow, tomorrow.plusDays(1));
        final Interval workingHours = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        AvailabilityItem override = entryConverter.convert(dataUtil.createUnavailableInterval("Holiday", today, Days.SEVEN.toStandardDuration()));
        override.getAvailabilityCalendar().getComponent(VAVAILABILITY).getProperties().add(Priority.MEDIUM);
        AvailabilityItem baseline = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, tomorrow, 1, workingHours));

        calendarItem.setChildren(new HashSet<>(Arrays.asList(override, baseline)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, allDay, false);
        assertEquals("Expect availability to cover interval requested", allDay, availability.getInterval());
        assertEquals("Expect no availability", 0, availability.getAvailableIntervals().size());
    }

    @Test
    public void shouldReturnAvailabilityWhenOverrideAvoidsInterval() {
        final DateTime today = new DateTime().withTimeAtStartOfDay();
        final Interval allDay = new Interval(today, today.plusDays(1));
        final Interval workingHours = new Interval(today.plusHours(9), today.plusHours(17));
        final AvailabilityItem override = entryConverter.convert(dataUtil.createUnavailableInterval("Holiday tomorrow", today.plusDays(1), Days.ONE.toStandardDuration()));
        override.getAvailabilityCalendar().getComponent(VAVAILABILITY).getProperties().add(Priority.MEDIUM);
        final AvailabilityItem existingItem = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, today, 1,
                workingHours));
        calendarItem.setChildren(new HashSet<>(Arrays.asList(override, existingItem)));
//        when(mockCalendarQueryProcessor.filterQuery(same(calendarItem), any(CalendarFilter.class))).thenReturn(Collections.<ICalendarItem>singleton(existingItem));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, allDay, false);
        assertEquals("Expect availability to cover interval requested", allDay, availability.getInterval());
        assertEquals("Expect availability", 1, availability.getAvailableIntervals().size());
        assertEquals(workingHours, availability.getAvailableIntervals().iterator().next().getInterval());
    }

    @Test
    public void shouldReturnMergedAvailabilityWhenTwoCosmoItemsFound() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime tomorrow = startOfDay.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        final Interval interval = new Interval(startOfDay, startOfDay.plusDays(3));
        final Interval availableInterval1 = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        final Interval availableInterval2 = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(17));
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, availableInterval1));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, availableInterval2));

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, interval, true);
        assertEquals("Expect availability to cover interval requested", interval, availability.getInterval());
        assertEquals("Expect availability", 2, availability.getAvailableIntervals().size());
        assertThat(availability.getAvailableIntervals(), Matchers.allOf(Matchers.hasItems(CosmoTestFixtureUtil.withInterval(availableInterval1), CosmoTestFixtureUtil.withInterval(availableInterval2))));
    }

    @Test
    public void shouldExcludeOutOfIntervalComponentsInCosmoItemsFound() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime tomorrow = startOfDay.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        final Interval interval = new Interval(startOfDay, startOfDay.plusDays(2).plusHours(12));
        final Interval availableInterval1 = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        final Interval availableInterval2 = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(12));
        final Interval availableInterval3 = new Interval(theDayAfter.plusHours(13), theDayAfter.plusHours(17));
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, availableInterval1));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, availableInterval2, availableInterval3));

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, interval, true);
        assertEquals("Expect availability to cover interval requested", interval, availability.getInterval());
        assertEquals("Expect availability", 2, availability.getAvailableIntervals().size());
        assertThat(availability.getAvailableIntervals(), Matchers.allOf(Matchers.hasItems(CosmoTestFixtureUtil.withInterval(availableInterval1), CosmoTestFixtureUtil.withInterval(availableInterval2))));
    }

    @Test
    public void shouldExcludeOverriddenComponentsInCosmoItemsFound() {
        final DateTime today = new DateTime().withTimeAtStartOfDay();
        final DateTime tomorrow = today.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        final Interval threeDays = new Interval(today, today.plusDays(3));
        final Interval tomorrowWorkHours = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        final Interval dayAfterMorning = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(12));
        final Interval dayAfterAfternoon = new Interval(theDayAfter.plusHours(13), theDayAfter.plusHours(17));
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, tomorrowWorkHours));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, dayAfterMorning, dayAfterAfternoon));
        AvailabilityItem override = entryConverter.convert(dataUtil.createUnavailableInterval("excludingInterval3", dayAfterMorning.getEnd(), Days.ONE.toStandardDuration()));
        override.getAvailabilityCalendar().getComponent(VAVAILABILITY).getProperties().add(Priority.MEDIUM);

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2, override)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, threeDays, false);
        assertEquals("Expect availability to cover interval requested", threeDays, availability.getInterval());
        assertEquals("Expect availability", 2, availability.getAvailableIntervals().size());
        assertThat(availability.getAvailableIntervals(), Matchers.allOf(Matchers.hasItems(CosmoTestFixtureUtil.withInterval(tomorrowWorkHours), CosmoTestFixtureUtil.withInterval(dayAfterMorning))));
    }

    @Test
    public void shouldTruncateOutOfIntervalComponentsInCosmoItemsFound() {
        final DateTime startOfDay = new DateTime().withTime(0, 0, 0, 0);
        final DateTime tomorrow = startOfDay.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        final Interval interval = new Interval(startOfDay, startOfDay.plusDays(2).plusHours(12));
        final Interval availableInterval1 = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        final Interval availableInterval2 = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(12));
        final Interval availableInterval3 = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(17));
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, availableInterval1));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, availableInterval3));

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, interval, true);
        assertEquals("Expect availability to cover interval requested", interval, availability.getInterval());
        assertEquals("Expect availability", 2, availability.getAvailableIntervals().size());
        assertThat(availability.getAvailableIntervals(), Matchers.allOf(Matchers.hasItems(CosmoTestFixtureUtil.withInterval(availableInterval1), CosmoTestFixtureUtil.withInterval(availableInterval2))));
    }

    @Test
    public void shouldTruncateOverriddenComponentsInCosmoItemsFound() {
        final DateTime today = new DateTime().withTimeAtStartOfDay();
        final DateTime tomorrow = today.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        final Interval threeDays = new Interval(today, today.plusDays(3));
        final Interval tomorrowWorkHours = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        final Interval dayAfterMorning = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(12));
        final Interval dayAfterWorkHours = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(17));
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, tomorrowWorkHours));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, dayAfterWorkHours));
        AvailabilityItem override = entryConverter.convert(dataUtil.createUnavailableInterval("excluding 12 onwards", dayAfterMorning.getEnd(), Days.ONE.toStandardDuration()));
        override.getAvailabilityCalendar().getComponent(VAVAILABILITY).getProperties().add(Priority.MEDIUM);

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2, override)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, threeDays, false);
        assertEquals("Expect availability to cover interval requested", threeDays, availability.getInterval());
        assertEquals("Expect availability", 2, availability.getAvailableIntervals().size());
        assertThat(availability.getAvailableIntervals(), Matchers.allOf(Matchers.hasItems(CosmoTestFixtureUtil.withInterval(tomorrowWorkHours), CosmoTestFixtureUtil.withInterval(dayAfterMorning))));
    }

    @Test
    public void shouldSplitOverriddenComponentsInCosmoItemsFound() {
        final DateTime today = new DateTime().withTimeAtStartOfDay();
        final DateTime tomorrow = today.plusDays(1);
        final DateTime theDayAfter = tomorrow.plusDays(1);
        final Interval threeDays = new Interval(today, today.plusDays(3));
        final Interval tomorrowWorkHours = new Interval(tomorrow.plusHours(9), tomorrow.plusHours(17));
        final Interval dayAfterMorning = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(12));
        final Interval dayAfterAfternoon = new Interval(theDayAfter.plusHours(13), theDayAfter.plusHours(17));
        final Interval dayAfterWorkHours = new Interval(theDayAfter.plusHours(9), theDayAfter.plusHours(17));
        AvailabilityItem existingItem1 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_1_ID, tomorrow, 1, tomorrowWorkHours));
        AvailabilityItem existingItem2 = entryConverter.convert(dataUtil.createTestAvailability(ENTRY_2_ID, theDayAfter, 1, dayAfterWorkHours));
        AvailabilityItem existingItem3 = entryConverter.convert(dataUtil.createUnavailableInterval("excluding lunchtime", dayAfterMorning.getEnd(), Hours.ONE.toStandardDuration()));

        calendarItem.setChildren(new HashSet<>(Arrays.asList(existingItem1, existingItem2, existingItem3)));

        Availability availability = service.findAvailability(CosmoTestDataUtil.CAL_1_ID, threeDays, false);
        assertEquals("Expect availability to cover interval requested", threeDays, availability.getInterval());
        assertEquals("Expect availability", 3, availability.getAvailableIntervals().size());
        assertThat(availability.getAvailableIntervals(), Matchers.allOf(Matchers.hasItems(CosmoTestFixtureUtil.withInterval(tomorrowWorkHours), CosmoTestFixtureUtil.withInterval(dayAfterMorning), CosmoTestFixtureUtil.withInterval(dayAfterAfternoon))));
    }

    private void assertCalEntriesValues(CalendarEntries calEntries, String expectedCalId, Interval expectedInterval, int expectedCalEntriesSize) {
        assertNotNull(calEntries);
        assertEquals(expectedCalId, calEntries.getCalendarId());
        assertEquals(expectedInterval, calEntries.getInterval());
        assertEquals(expectedCalEntriesSize, calEntries.getEntries().size());
        for (Entry entry : calEntries.getEntries()) {
            assertEquals(userRefUri.toString(), entry.getCalendarIdUserReferenceUri());
        }
    }

    private CosmoCalendarService createCalendarService() {
        // TODO: this is not a unit test - uses concrete EntityFactory and CosmoConverter (entryConverter)
        var recurringService = new CosmoCalendarRecurringService(mockCosmoContentService, mockCosmoUserService,
                entityFactory, mockEntityUriMapper, mockMessageBus, entryConverter);
        return new CosmoCalendarService(mockCosmoContentService, mockCosmoUserService, recurringService,
                mockEntityUriMapper, mockMessageBus, entryConverter);
    }

    private CosmoCalendarService createCalendarServiceUnit() {
        var recurringService = new CosmoCalendarRecurringService(mockCosmoContentService, mockCosmoUserService,
                mockEntityFactory, mockEntityUriMapper, mockMessageBus, mockEntryConverter);
        return new CosmoCalendarService(mockCosmoContentService, mockCosmoUserService, recurringService,
                mockEntityUriMapper, mockMessageBus, mockEntryConverter);
    }
}
