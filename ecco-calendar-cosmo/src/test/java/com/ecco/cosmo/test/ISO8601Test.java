package com.ecco.cosmo.test;

import net.fortuna.ical4j.model.Date;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.Assert;
import org.junit.Test;

public class ISO8601Test {

    DateTimeFormatter fmt = ISODateTimeFormat.dateTime();

    @Test // NOTE: Requires build server timezone to be set to Europe/London or other UTC zone
    public void testISO8601() {
        // construct a known date
        DateTime instance = new DateTime(2009, 11, 20, 13, 16, 43, 55, DateTimeZone.UTC);
        long instanceMS = instance.getMillis();

        // ical4j ISO date
        Date ical4jDate = new Date(instanceMS);
        String ical4jISO = ical4jDate.toString();
        // this is NOT iso!
        // produces 20091120T131643
        Assert.assertNotSame("2009-11-20T13:16:43.055Z", ical4jISO);

        // joda ISO date
        String jodaISO = fmt.print(instanceMS);
        Assert.assertEquals("2009-11-20T13:16:43.055Z", jodaISO);
    }
}
