STRATEGY
========

nb
	use AOP for method level interception or filter interception at the url level

also - look at scheduler in cosmo - the applicationContext-scheduler

q4e for maven
SpringToolSuite? - run on server (not jetty?)
no need for acl on collections - just using one account



sql
---
get sql (this is auto generated from hibernate) - see http://chandlerproject.org/Projects/CosmoBuildInstructions#Using%20a%20relational%20database%20back
backup the dbase and insert into managed_schemas (check ok for version 4, since live is version 4)
D:\temp\med\cosmo\osaf-server-bundle-0.6.1>mysqldump -u cosmo -pc0sm0 cosmo > mysql5_schema.sql

debugging
---------
although spring etc, the run 'bundle' comes with configured tomcat
so its easier to run the bundle and try attaching a remote debugger (there is also a WAR download!)
this seems to work - but is unlikely to be an exact version match
could get the matched version with http://svn.osafoundation.org/server/cosmo/tags/rel_0.6.1
 - now downloaded _1.0.0
(as opposed to latest on 4th aug 08)

integrate
---------
browse /pim in servlet context...
seems not much goes on - its in the dojo front end

we could plug the servlet directly in to web.xml, and use the existing applicationContext files!!
configure the beans required? (ignore security)
bring in the libraries required (including js)
configure a page to load them
tidy the page - colours etc
integrate with reminders! - or integrate our reminders with it!



IMPL
========
caching - we need to do ourselves - or have cosmo running and get the feed from cosmo
change cosmo db to be inside med - so prefix tables: avoids xa

alterations
	- see code comparison to repo
	- password unlimited length change could be problematic (<32 chars used to indicate user not encoded password - see StandardUserService.updateUser
	- if need to delete users this clears out cosmo
		delete from cosmo_collection_item;
		delete from cosmo_event_stamp;
		delete from cosmo_stamp;
		delete from cosmo_attribute;
		delete from cosmo_tombstones;
		delete from cosmo_event_log;
		delete from cosmo_item;
		delete from cosmo_users where username <> 'root';
	- added a class to change the default timezone

or delete...
	drop table cosmo_collection_item;
	drop table cosmo_event_stamp;
	drop table cosmo_stamp;
	drop table cosmo_attribute;
	drop table cosmo_tombstones;
	drop table cosmo_event_log;

	drop table cosmo_dictionary_values;
	drop table cosmo_multistring_values;
	drop table cosmo_pwrecovery;
	drop table cosmo_server_properties;
	drop table cosmo_subscription;
	drop table cosmo_ticket_privilege;
	drop table cosmo_tickets;
	drop table cosmo_user_preferences;

	drop table cosmo_item;
	drop table cosmo_users;
	drop table cosmo_content_data;


create schema...
	to create the db requires cosmo to run (hibernate creates the tables)
	this can be done by running med/ecco package and simply enabling part of the config file in applicationContext-cosmo-event-integration
	we then disable it again because the validation fails - we since modified some fields?

	however we can copy an existing database - structure only (see mysql.txt)
	but it appears that this corrupts the indexes
	and	import the static/example data from ecco/managed_schema
	(doesn't need root if not using cosmo standalone)

	insert into cosmo_users values (2, now(), 'Nn6oWcvQbsbO/4tExbzaK5OaYh8=', now(), null, 1, 'adam@null', 'NULL', 'NULL', 0, '11ea881985e5eacc41aed87ed4f01fc5', '46a92652-b5b7-4963-b9ea-e77b53bcb576', 'sysadmin');
	insert into cosmo_item values ('homeCollection', 1, now(), 'wX78TgglhlljGT0tCa0Bw+psGJQ=', now(), NULL, NULL, 'sysadmin', 'sysadmin', '99e01244-1aea-4cbf-b6c8-ffa5880f69b4', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, NULL, NULL); 
	insert into cosmo_item values ('collection', 2, now(), 'pEaA5OECV3LU5/ZkbGDhREPZfdM=', now(), NULL, NULL, 'SystemCollection', 'SystemCollection', 'e37aede8-9f45-4bef-b6a5-d7ad27ca7841', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, NULL, NULL); 
	insert into cosmo_collection_item values (now(), 2, 1);
	insert into cosmo_stamp values ('calendar', 1, NULL, '', now(), 2);
	-- insert into cosmo_user_preferences values (1, now(), 'A2XF9TQGRMeH8d/V+T8sM4Ib0sw=', now(), 'Login.Url', '/account/view', 2);
	-- insert into cosmo_server_properties values (1, 'cosmo.schemaVersion', 100);






keep our org/osaf/cosmo/db/DbInitializer.java
	modified table name: ps = conn.prepareStatement("select count(*) from cosmo_server_properties");

keep our org/osaf/cosmo/TimeZoneDefault.java
	new

keep our osaf/cosmo/model/hibernate/*
	table name prefixes
	also, org/osaf/cosmo/model/hibernate/HibUser.java - where we altered the password restrictions to be more lenient

ensure we don't upload our jdbc.properties

renamed 'applicationContext-scheduler.xml' to 'ignore-applicationContext-scheduler.xml'
though I read a r1.2 change enabling this as an option

probably remove our log4j.xml - its got med stuff in it

keep our webapp/WEB-INF/jsp/taglibs.jsp??
keep our webapp/WEB-INF/tags/jsurl.tag??
keep our webapp/WEB-INF/cosmo.tld??
change webapp/WEB-INF/web.xml??
	- hide the jdbc/cosmo and mail/cosmo beans

keep our pom.xml


=====================================

is set for mysql...localhost cosmo, cosmo
	cosmo.hibernate.dialect [see cosmo.properties]

	jdbc.driverClassName=com.mysql.jdbc.Driver
	jdbc.showSql=false
	jdbc.url=***********************************************************************
	jdbc.username=med
	jdbc.password=my3asydiary
	[see jdbc.properties]

	and the mysql-connector in the pom

	cosmo.tld deleted?

retain some stuff - see cosmo svn
	eg xa, search for atomikos

JS dir


