package com.ecco.dom;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

public interface IdName<ID extends Serializable> extends Identified<ID> {

    String getName();


    class Collections {

        public static final class ExtractId<ID extends Serializable> implements Function<Identified<ID>, ID> {
            @Override
            public ID apply(Identified<ID> input) {
                return input.getId();
            }
        }

        public static class ExtractName<ID extends Serializable> implements Function<IdName<ID>, String> {
            @Override
            public
            String apply(IdName<ID> input) {
                return input.getName(); // throw's NPE as per contract if null
            }
        }

        public static final class NameFoundIn<ID extends Serializable> implements Predicate<IdName<ID>> {

            private final Collection<String> names;

            public NameFoundIn(Collection<String> names) {
                this.names = names;
            }

            @Override
            public boolean test(@Nullable IdName<ID> entity) {
                return names != null && names.contains(entity.getName()); // throw's NPE as per contract if enitity is null
            }

            public static <ID extends Serializable> NameFoundIn<ID> nameFoundIn(final Collection<String> names) {
                return new NameFoundIn<>(names);
            }
        }

        public static <T extends IdName<?>> List<T> orderedFilter(final Set<T> namedItems,
                Collection<String> orderedFilterKeys) {

            final Map<String,T> lookup = Maps.uniqueIndex(namedItems, T::getName);

            ArrayList<T> result = new ArrayList<>(orderedFilterKeys.size());
            for (String key : orderedFilterKeys) {
                T entry = lookup.get(key);
                if (entry == null) {
                    String keys = StringUtils.join(lookup.keySet(), ",");
                    Assert.notNull(entry, "Couldn't find '" + key
                            + "'. Perhaps a typo or a comma or backslash needs escaping with '\\'. Full list is:" + keys);

                }
                result.add(entry);
            }
            return result;
        }

        public static <T extends IdName<? extends Number>> List<T> orderedFilterByNumericId(final Set<T> namedItems,
                Collection<String> orderedFilterIds) {

            final Map<? extends Number,T> lookup = Maps.uniqueIndex(namedItems, (T t) -> t.getId());

            ArrayList<T> result = new ArrayList<>(orderedFilterIds.size());
            for (String idStr : orderedFilterIds) {
                Long key = Long.parseLong(idStr);
                T entry = lookup.get(key);
                if (entry == null) {
                    String keys = StringUtils.join(lookup.keySet(), ",");
                    Assert.notNull(entry, "Couldn't find '" + key
                            + "' in outcomeIds for service. Full list is:" + keys);
                }
                result.add(entry);
            }
            return result;
        }

    }
}
