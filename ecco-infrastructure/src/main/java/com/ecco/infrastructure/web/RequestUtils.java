package com.ecco.infrastructure.web;

import javax.servlet.http.HttpServletResponse;
import java.net.*;

public class RequestUtils {
    /**
     * Caching to be applied to service config stuff that only usually changes when ecco updates a system.
     * TODO: We could go with cache-busting on this... or ETag in combo with Cache-Control
     */
    static public void cacheModerately(HttpServletResponse response) {
        response.addHeader("Cache-Control", "max-age="+3600);
    }

    // this means the browser won't look for a refresh for hours - although it might simply refresh from a server-side cache
    static public void cacheForXHoursExtendRandom(HttpServletResponse response, int minHours, int extendHoursRandom) {
        int maxAge = (minHours * 3600) + (int) (Math.random() * (extendHoursRandom * 3600));
        response.addHeader("Cache-Control", "max-age=" + maxAge);
    }

    static public void cacheForXHours(int numHours, HttpServletResponse response) {
        cacheForXSecs(numHours * 3600, response);
    }

    static public void cacheForXSecs(int numSeconds, HttpServletResponse response) {
        response.addHeader("Cache-Control", "max-age=" + numSeconds);
    }

    /**
     * Call this with the response to prevent caching
     */
    static public void dontCache(HttpServletResponse response) {
        response.setHeader("Cache-Control","no-cache, no-store, max-age=0, must-revalidate");
        response.setHeader("Pragma","no-cache");
        response.setHeader("Expires","0");
    }

    /**
     * Used for debugging - NOT to be used for any logic processing given the complexities of networking.
     */
    static public boolean isRequestFromLocalhostForDevOnly(String uriHost) throws UnknownHostException {
        var host = InetAddress.getByName(uriHost).getHostAddress();
        // nb can be much more complicated, but this does us fine in development
        return host.contains("localhost") || host.contains("127.0") || host.contains("0:0:0:0:0:0:0:1");
    }

}
