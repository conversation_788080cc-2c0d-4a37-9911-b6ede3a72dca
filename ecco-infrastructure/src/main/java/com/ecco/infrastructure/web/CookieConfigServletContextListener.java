package com.ecco.infrastructure.web;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * Can also be used via web.xml specify
 * <pre>
 *   &lt;listener>
 *       &lt;listener-class>com.ecco.infrastructure.web.CookieConfigServletContextListener&lt;/listener-class>
 *   &lt;/listener>
 * </pre>
 */
@WebListener
public class CookieConfigServletContextListener implements ServletContextListener {

    @Override
    public void contextInitialized(ServletContextEvent event) {
        // We set .csrf().disable() because we use Secure (cookies only over https), but this setting turns Secure off.
        // So we can use -Dcookie.insecure=true on localhost to prevent adding of secure to our session cookies
        event.getServletContext().getSessionCookieConfig().setSecure(!Boolean.getBoolean("cookie.insecure"));
    }

    @Override
    public void contextDestroyed(ServletContextEvent event) {}

}
