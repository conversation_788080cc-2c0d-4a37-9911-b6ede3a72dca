package com.ecco.infrastructure.web;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.function.Predicate;
import org.springframework.core.annotation.AnnotationUtils;

/**
 * Annotation to mark a class or method as a web slice.
 * A web slice is a part of a web application that can be relocated to a different URL path.
 * This is useful for modularizing a web application into separate parts that can be developed and deployed independently.
 * <p>
 * To relocate a component, add this annotation to the component class with a component name, e.g. <code>@WebSlice("nav")</code>.
 * Then ensure there is a mapping for this component in WebMvcConfigurer.configurePathMatch:
 * <pre>
 *     configurer.addPathPrefix("/prefixedNav", new WebSliceMatch("nav"));
 * </pre>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface WebSlice {

    String value();

    /**
     * Predicate to match a class that is annotated with  component name to relocate. To relocate a component, add this
     * annotation to the component class with a component name, e.g. <code>@WebSlice("nav")</code>. Then ensure there is
     * a mapping for this component in WebMvcConfigurer.configurePathMatch:
     * <pre>
     *     configurer.addPathPrefix("/prefixedNav", new WebSliceMatch("nav"));
     * </pre>
     */
    record WebSliceMatch(String moduleName) implements Predicate<Class<?>> {
        @Override
        public boolean test(Class<?> aClass) {
            var annotation = AnnotationUtils.findAnnotation(aClass, WebSlice.class);
            return annotation != null && annotation.value().equals(moduleName);
        }
    }
}
