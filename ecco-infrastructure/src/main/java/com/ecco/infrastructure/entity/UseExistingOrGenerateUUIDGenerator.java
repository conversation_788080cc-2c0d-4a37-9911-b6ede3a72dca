package com.ecco.infrastructure.entity;

import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.UUIDGenerator;

import java.io.Serializable;

/**
 * Use an id if provided. Allows our UUID to be generated by the client.
 * See http://stackoverflow.com/a/8535006
 */
public class UseExistingOrGenerateUUIDGenerator extends UUIDGenerator {

    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object)
                        throws HibernateException {

        Serializable id = session.getEntityPersister(null, object)
                      .getClassMetadata().getIdentifier(object, session);
        return id != null ? id : super.generate(session, object);
    }

}
