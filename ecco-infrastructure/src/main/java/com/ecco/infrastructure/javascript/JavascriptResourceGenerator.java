package com.ecco.infrastructure.javascript;

import java.io.IOException;
import java.io.Writer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

public abstract class JavascriptResourceGenerator {

    /**
     * Exposes a JavaScript AMD module containing the data represented by the
     * {@code properties} object.
     * <p/>
     * The {@code properties} object will be serialized to a JavaScript object
     * using <PERSON>. The resulting JavaScript object will be exposed as an
     * AMD module.
     *
     * @param globalJsPropName the property name to assign properties to on window object in Javascript
     * @param properties An object containing data that will be exposed to
     *                   JavaScript. Must be serializable using Jackson.
     *@param writer    The writer to write the JavaScript content to.
     */
    public static void writeObjectAsAMDModule(String globalJsPropName, Object properties, Writer writer) throws IOException {
        // Put the name of this class in a comment so someone looking at
        // the output JavaScript can figure out where it came from.
        writer.write("// see: ");
        writer.write(JavascriptResourceGenerator.class.getCanonicalName());
        writer.write("\n");

        writer.write("window."+ globalJsPropName + " = ");
        new ObjectMapper()
                .configure(SerializationFeature.INDENT_OUTPUT, false)
                .configure(SerializationFeature.WRAP_ROOT_VALUE, false)
                .configure(JsonGenerator.Feature.AUTO_CLOSE_TARGET, false)
                .writeValue(writer, properties);
        writer.write(";window.define && define(window." + globalJsPropName + ");");
    }

}
