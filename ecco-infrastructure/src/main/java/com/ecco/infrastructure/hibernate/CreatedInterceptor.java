package com.ecco.infrastructure.hibernate;

import java.io.Serializable;

import org.hibernate.EmptyInterceptor;
import org.hibernate.type.Type;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import com.ecco.infrastructure.Created;

public class CreatedInterceptor extends EmptyInterceptor {

    private static final long serialVersionUID = 1L;

    @Override
    public boolean onSave(Object entity,
            Serializable id,
            Object[] state,
            String[] propertyNames,
            Type[] types)
    {
        if (entity instanceof Created) {
            // only need one 'entity' test - hibernate runs the interceptor for each entity
            // which means the datetime will be fractionally out to each other - which doesn't matter
            Created created = (Created) entity;
            // don't test on isNew since using oracle sequences the identifier is already assigned here
            // we simply test for blank created - this should have no consequences since the whole point of this interceptor is to set a created date
            //if (created.isNewEntity()) {
            if (created.getCreated() == null) {
                return setPropertyState(state, propertyNames, "created", new DateTime(DateTimeZone.UTC));
            }
        }
        return false;
    }

    /**
     *
     * @return true if property was found and changed
     */
    public static boolean setPropertyState(Object[] state, String[] propertyNames, String propertyName,
            Object newValue) {
        // don't change the entity, but do the state!
        // see http://blog.janjonas.net/2010-02-17/hibernate-interceptor-update-entity-property-on-update-on-save
        for (int i = 0; i < propertyNames.length; i++) {
            if (propertyName.equals(propertyNames[i])) {
                state[i] = newValue;
                return true;
            }
        }
        return false;
    }
}