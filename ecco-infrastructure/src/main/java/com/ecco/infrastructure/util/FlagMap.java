package com.ecco.infrastructure.util;

import java.io.Serializable;
import java.util.AbstractMap;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections.map.ListOrderedMap;
import org.jspecify.annotations.NonNull;
import org.springframework.util.StringUtils;


public class FlagMap extends AbstractMap<String,Boolean> implements Serializable {

    private static final long serialVersionUID = 1L;

    @SuppressWarnings("unchecked")
    private Map<String, Boolean> map = new ListOrderedMap();

    private String sourceValue;

    /**
     * Allow a temporary construct from jsp
     */
    public FlagMap() {
    }

    /**
     * Use comma sep list, where commas in a key are escaped with a preceding backslash, as
     * are backslashes.
     */
    public FlagMap(String commaSepList) {
        this.setCommaSepList(commaSepList);
    }

    public void setCommaSepList(String commaSepList) {
        this.sourceValue = commaSepList;
        if (!StringUtils.hasText(commaSepList)) {
            map = Collections.emptyMap();
            return;
        }

        Pattern pattern = Pattern.compile("[^\\\\](,)");
        Matcher matcher = pattern.matcher(commaSepList);
        int start = 0;
        while(matcher.find()){
            String group = commaSepList.substring(start, matcher.end()-1);
            String replaced = clean(group);
            map.put(replaced, Boolean.TRUE);
            start = matcher.end();
        }
        String last = commaSepList.substring(start);
        last = clean(last);
        map.put(last, Boolean.TRUE);
    }

    public String clean(String group) {
        return group.trim().replace("\\,", ",").replace("\\\\", "\\");
    }

    @Override
    public Boolean get(Object value) {
        return Boolean.valueOf(map.containsKey(value));
    }

    public Boolean put(String key, Boolean value) {
        return map.put(key, value);
    }

    @NonNull
    @Override
    public Set<Entry<String, Boolean>> entrySet() {
        return map.entrySet();
    }

    /**
     * Get the source value of this entry falling back to the provided default
     */
    public String orDefault(String defaultValue) {
        return map.isEmpty() ? defaultValue : sourceValue;
    }
}
