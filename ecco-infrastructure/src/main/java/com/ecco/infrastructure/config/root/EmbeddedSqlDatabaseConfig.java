package com.ecco.infrastructure.config.root;

import lombok.SneakyThrows;
import org.apache.tomcat.jdbc.pool.DataSource;
import org.h2.Driver;
import org.h2.server.TcpServer;
import org.h2.tools.Server;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.orm.jpa.vendor.Database;

import java.nio.file.Path;
import java.sql.SQLException;

@Configuration
@Profile({Profiles.DEFAULT, Profiles.EMBEDDED})
public class EmbeddedSqlDatabaseConfig extends AbstractSqlDatabaseConfig<DataSource> {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private EccoEnvironment env;

    @Override
    @Bean(destroyMethod = "close")
    public DataSource dataSource() {
        return createDatabaseFactory();
    }

    @Bean(initMethod = "start", destroyMethod = "stop") // allows us to connect to the embedded H2 database using an external client
    public Server embeddedServer() throws SQLException {
        TcpServer service = new TcpServer();
        Server server = new Server(service, "-tcpPort", "9093", "-ifExists") {
            @Override
            public Server start() {
                try {
                    return super.start();
                } catch (SQLException e) {
                    logger.warn("Could not start embedded H2 server (maybe another instance is already running?)", e);
                    return this;
                }
            }
        };
        service.setShutdownHandler(server);
        return server;
    }

    @SneakyThrows
    private DataSource createDatabaseFactory() {
        DataSource ds = getConnectionPool(env);
        ds.setDriverClassName(Driver.class.getName());
        ds.setMaxActive(30); // FIXME: fail ecco-war tests with default due to lockup waiting for connection

        String url = env.getEmbeddedPersistPath() != null
                ? "jdbc:h2:" + env.getEmbeddedPersistPath() // e.g. file:~/h2/ecco
                : "jdbc:h2:mem:ecco";        // ";TRACE_LEVEL_SYSTEM_OUT=2;TRACE_LEVEL_FILE=0"

        logger.info("=================================");
        if (env.getEmbeddedPersistPath() != null) {
            if (url.contains(":~")) {
                logger.info("\n====\nH2 storing data in user home directory: {}\n====", env.getEmbeddedPersistPath());
            } else {
                var path = Path.of(".").toRealPath().toAbsolutePath();
                var absolutePath = path.resolve(url.substring("jdbc:h2:file:".length())).toAbsolutePath().normalize();
                if (path.resolve("target").toFile().exists()) {
                    logger.info("\n====\nH2 storing data in your project workspace at: {}\n====", absolutePath);
                } else {
                    logger.info("\n====\nH2 storing data at: {}\n====", absolutePath);
                }
            }
        } else {
            logger.info("\n====\nH2 storing data at: {}\n====", url);
        }

        ds.setUrl(url);
        ds.setUsername("sa");
        ds.setPassword("");
        return ds;
    }

    @Override
    public Database database() {
        return Database.H2;
    }
}
