package com.ecco.infrastructure.config.web;

import org.joda.time.LocalDateTime;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.Jsr310Converters.StringToLocalDateTimeConverter;

import org.jspecify.annotations.NonNull;

@Configuration
public class MoreConverters {

    @Bean
    public Converter<String, java.time.LocalDateTime> stringToLocalDateTimeConverter() {
        return StringToLocalDateTimeConverter.INSTANCE;
    }

    @Bean Converter<String, LocalDateTime> stringToJodaLocalDateTimeConverter() {
        return new Converter<>() {
            @Override
            public LocalDateTime convert(@NonNull String source) {
                return LocalDateTime.parse(source);
            }
        };
        // NOTE: we cannot do the following, and prior to now, we somehow weren't hitting this.
        // see https://github.com/spring-projects/spring-framework/issues/22509
        // return LocalDateTime::parse;
    }
}
