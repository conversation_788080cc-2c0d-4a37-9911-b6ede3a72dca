package com.ecco.infrastructure.config.web;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.std.FromStringDeserializer;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdScalarSerializer;
import com.fasterxml.jackson.module.kotlin.KotlinModule;
import org.springframework.http.converter.*;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.support.AllEncompassingFormHttpMessageConverter;
import org.springframework.http.converter.xml.SourceHttpMessageConverter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.util.Assert;


public class ConvertersConfig {

    public static void addWebApiConvertersTo(List<HttpMessageConverter<?>> converters) {
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter();
        stringConverter.setWriteAcceptCharset(false);

        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        ObjectMapper jacksonObjectMapper = getObjectMapper();
        jacksonConverter.setObjectMapper(jacksonObjectMapper);

        Assert.state(converters.isEmpty(), "Converters must be empty");
        converters.add(new ByteArrayHttpMessageConverter());
        converters.add(stringConverter);
        converters.add(new ResourceHttpMessageConverter());
        converters.add(new BufferedImageHttpMessageConverter());
        converters.add(new SourceHttpMessageConverter<>());
        converters.add(new AllEncompassingFormHttpMessageConverter());
        converters.add(jacksonConverter);
    }

    public static ObjectMapper getObjectMapper() {
        // NB we could use 'new Jackson2ObjectMapperBuilder()..' to inherit some defaults
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        // see commit msg: jacksonObjectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        jacksonObjectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        jacksonObjectMapper.registerModule(new KotlinModule());
        jacksonObjectMapper.registerModule(new JodaModule());
        jacksonObjectMapper.registerModule(new GuavaModule());
        jacksonObjectMapper.registerModule(new Jdk8Module()); // NOTE: Only deals with Optional
        jacksonObjectMapper.registerModule(new JavaTimeModule());
        jacksonObjectMapper.registerModule(new StreamModule());

        // FIXME: Remove NOW we've upgraded to > Jackson 2.7.0
        // Workaround for https://github.com/FasterXML/jackson-module-jsonSchema/issues/77 and
        // https://github.com/FasterXML/jackson-module-jsonSchema/issues/85 due to
        // lack of pull request https://github.com/FasterXML/jackson-module-jsonSchema/pull/81 in this version.
        SimpleModule jsonValueFormatSerializerModule = new SimpleModule("JsonValueFormatSerializer", new Version(0, 0, 1, null, null, null));
        jsonValueFormatSerializerModule.addSerializer(JsonValueFormat.class, new StdScalarSerializer<>(JsonValueFormat.class) {
            @Override
            public void serialize(JsonValueFormat value, JsonGenerator gen, SerializerProvider provider) throws IOException {
                gen.writeString(value.toString());
            }
        });
        jsonValueFormatSerializerModule.addDeserializer(JsonValueFormat.class, new FromStringDeserializer<>(JsonValueFormat.class) {
            final Map<String, JsonValueFormat> allFormats = Arrays.stream(JsonValueFormat.values())
                    .collect(Collectors.toMap(JsonValueFormat::toString, Function.identity()));

            @Override
            protected JsonValueFormat _deserialize(String value, DeserializationContext ctxt) throws IOException {
                return allFormats.get(value);
            }
        });
        jacksonObjectMapper.registerModule(jsonValueFormatSerializerModule);
        return jacksonObjectMapper;
    }
}
