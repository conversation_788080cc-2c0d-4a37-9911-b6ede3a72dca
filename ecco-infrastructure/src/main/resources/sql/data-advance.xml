<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!--This supports -Ddb.extraContexts=data-advance
        data-advance
            This will advance data in the database by the number of days between the latest referral command and now,
            such that everything is moved up to make current demo data.
    -->
    <changeSet id="data-advance" author="nealeu" context="data-advance" runAlways="true">
        <sqlFile path="data-advance.sql" relativeToChangelogFile="true"/>
    </changeSet>

</databaseChangeLog>
