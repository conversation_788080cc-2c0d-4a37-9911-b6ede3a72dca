<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-1852-repDef-badge-referrals" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-03-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="number of referrals received/closed per month (with live)"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <!-- change the referrals dashboard to be badges to a pie chart without clicks -->
    <changeSet id="DEV-1852-repDef-badge-referrals-body-multiBadges4" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard referrals"/>
            <column name="body">
                {
                "description": "referrals in the period",
                "selectionMultiCriteria": [
                {
                "groupBy": "receivedDate",
                "referralStatus": "received",
                "selectionRootEntity": "ReferralsByMonth",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                {
                "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
                },
                "groupBy": "closedDate",
                "referralStatus": "closed",
                "selectionRootEntity": "ReferralsByMonth",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                {
                "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
                },
                "groupBy": "liveAtEnd",
                "referralStatus": "liveAtEnd",
                "selectionRootEntity": "ReferralsByService",
                "selectorType": "byReferralStatus",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                }
                ],
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "groupByGroupBy"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "referralGroupToSingleSummary"
                }
                },
                {
                "description": "live",
                "stageType": "BADGE",
                "selectionAnalyser": "liveAtEnd",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-user",
                "recordRepresentationClassName": "byReferralGroupAnalysis",
                "mainIndicatorValue": "live"
                }
                },
                {
                "description": "new",
                "stageType": "BADGE",
                "selectionAnalyser": "receivedDate",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-plus",
                "recordRepresentationClassName": "byReferralGroupAnalysis",
                "mainIndicatorValue": "new"
                }
                },
                {
                "description": "closed",
                "stageType": "BADGE",
                "selectionAnalyser": "closedDate",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-minus",
                "recordRepresentationClassName": "byReferralGroupAnalysis",
                "mainIndicatorValue": "closed"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "regroupByEntityName"
                }
                },
                {
                "description": "by service",
                "stageType": "CHART",
                "canClickThrough": false,
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                }
                ]
                }
            </column>
            <where>uuid='05500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-1852-repDef-badge-tasks" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05600000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-03-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="number of tasks due per month by service"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-1852-repDef-badge-tasks-body-multiBadges" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "tasks in the period",
                "selectionMultiCriteria": [
                {
                "groupBy": "dueByService",
                "selectionRootEntity": "TasksByMonth",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                {
                "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
                },
                "groupBy": "completedWithDueByService",
                "selectionRootEntity": "TasksByMonth",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                {
                "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
                },
                "groupBy": "overdueByService",
                "selectionRootEntity": "TasksByMonth",
                "selectorType": "byStartOfMonth",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                }
                ],
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "tasksByService"
                }
                },
                {
                "description": "by service",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "tasksByTask"
                }
                },
                {
                "description": "by task",
                "stageType": "CHART",
                "canSkip": "true",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "tasksByStatus"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "taskGroupToSingleSummary"
                }
                },
                {
                "description": "due",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "due"
                }
                },
                {
                "description": "overdue",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks badge-amber",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "overdue"
                }
                },
                {
                "description": "completed",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "completed"
                }
                }
                ]
                }
            </column>
            <where>uuid='05600000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1852-repDef-badge-tasks-user" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05700000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-03-04T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="-20"/>
            <column name="name" value="number of tasks due per month by user"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-1852-repDef-badge-tasks-user-body3" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard tasks"/>
            <column name="body">
                {
                "description": "tasks in the period",
                "selectionCriteria": {
                "selectionRootEntity": "TaskStatus",
                "selectorType": "byStartOfMonth",
                "entityStatus": "dueDateOrCompleted",
                "relativeStartIndex": -2,
                "relativeEndIndex": 1
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "tasksToSingleSummary"
                }
                },
                {
                "description": "due",
                "stageType": "BADGE",
                "selectionAnalyser": "selectAllDue",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "due"
                }
                },
                {
                "description": "overdue",
                "stageType": "BADGE",
                "selectionAnalyser": "selectAllOverdue",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks badge-amber",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "overdue"
                }
                },
                {
                "description": "completed",
                "stageType": "BADGE",
                "selectionAnalyser": "selectAllCompleted",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "completed"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "groupByService"
                }
                },
                {
                "description": "by service",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "groupByAssignee"
                }
                },
                {
                "description": "by assignee",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "value",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "breakdown of tasks",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "TaskStatusOnlyColumns",
                "columns": [
                "r-id",
                "c-id",
                "client",
                "service",
                "project",
                "task",
                "description",
                "due",
                "assignee",
                "created",
                "completed",
                "edit"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='05700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1929-repDef-clientfile-default" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05800000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-04-09T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="52"/>
            <column name="name" value="client file 'reports' tab"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-1929-repDef-clientfile-default-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "on support work this quarter",
                "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": [
                "supportWork"
                ]
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "workFromReferrals_JOIN_workByCommentType"
                }
                },
                {
                "description": "by type",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "breakdown of support work",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "Work",
                "columns": [
                "task",
                "r-id",
                "client",
                "created",
                "author",
                "task",
                "work date",
                "type",
                "location",
                "time (mins)",
                "comment"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='05800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- all incomplete tasks with due dates - used for 'my' tasks, therefore removed the assigned -->
    <changeSet id="DEV-1852-repDef-tasksIncomplete-body3" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on all uncompleted tasks&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;TaskStatus&quot;,
                "entityStatus": "incomplete_hasDueDate"
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;filterByNotCompleted&quot;
                }
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;groupByDue&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by due date&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;value&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;groupByService&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by service&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;value&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of tasks&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;TaskStatusOnlyColumns&quot;,
                &quot;columns&quot;: [
                &quot;srId&quot;,
                &quot;r-id&quot;,
                &quot;c-id&quot;,
                &quot;client&quot;,
                &quot;service&quot;,
                &quot;project&quot;,
                &quot;task&quot;,
                &quot;description&quot;,
                &quot;due&quot;,
                &quot;assignee&quot;,
                "edit"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='04900000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1979-repDef-addresshisory" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05900000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-05-21T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="40"/>
            <column name="name" value="address history"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-1929-repDef-addresshistory-default-body2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "all address history",
                "selectionCriteria": {
                "selectionRootEntity": "AddressHistory",
                "selectionPropertyPath": "validFrom",
                "selectorType": "byStartOfQuarter",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["referral"]
                },
                "stages": [
                {
                "description": "breakdown of address history",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "AddressHistoryWithReferralSummary",
                "columns": ["id", "sr-id", "r: name", "valid from", "valid to", "full address"]
                }
                }
                ]
                }
            </column>
            <where>uuid='05900000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1979-repDef-outcomeprogress" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-05-21T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="40"/>
            <column name="name" value="outcome progress (smart steps)"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-1929-repDef-outcomesprogress-default-body2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "on all pre/post snapshot smartsteps for the period",
                "selectionCriteria": {
                "selectionRootEntity": "SmartStepMultiSnapshot",
                "supportEvidenceGroup": "needs",
                "selectorType": "byEndOfQuarter",
                "absoluteFromDate": "1970-01-01",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": [
                "referral"
                ]
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "smartStepsSnapshotFlatten"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "smartStepsSnapshotDifferenceByActionInstance"
                }
                },
                {
                "description": "breakdown of entries",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "differenceColumnsWithReferral",
                "columns": [
                "r: c-id",
                "r: r-id",
                "r: service",
                "r: worker",
                "r: status now",
                "a: o-name",
                "a: a-name",
                "a: instance-id",
                "pre-a: workDate",
                "pre-a: a-status",
                "post-a: workDate",
                "post-a: a-status"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-2035-repDef-KPI1-body-selectAll" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
                {
                "description": "client contact summary",
                "selectionCriteria": {
                "groupBy": "serviceRecipient.service",
                "selectionRootEntity": "GroupedWorkAnalysis",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "by service",
                "stageType": "CHART",
                "canSkip": "true",
                "selectionAnalyser": "single",
                "selectionAnalyserMany": "merge",
                "selectionAnalyserAll": "merge",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "Time spent with clients in period",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comments",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "time spent"
                }
                },
                {
                "description": "average time per visit",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comment",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "average time spent"
                }
                },
                {
                "description": "latest work",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-calendar",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "latest work"
                }
                }
                ]
                }
            </column>
            <where>uuid='10000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <changeSet id="ECCO-2035-repDef-KPI2-body-selectAll" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
                {
                "description": "client contact summary",
                "selectionCriteria": {
                "groupBy": "author",
                "selectionRootEntity": "GroupedWorkAnalysis",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "by author",
                "stageType": "CHART",
                "canSkip": "true",
                "selectionAnalyser": "single",
                "selectionAnalyserMany": "merge",
                "selectionAnalyserAll": "merge",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "Time spent with clients in period",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comments",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "time spent"
                }
                },
                {
                "description": "average time per visit",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comment",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "average time spent"
                }
                },
                {
                "description": "latest work",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-calendar",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "latest work"
                }
                }
                ]
                }
            </column>
            <where>uuid='10010000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- update the body with escape xml -->
    <changeSet id="DEV-2035-repDef-riskFlagsLive-bodyLatestFlags" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;all risk flags&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;RiskFlags&quot;,
                &quot;entityStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;fetchRelatedEntities&quot;: [&quot;referral&quot;]
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;riskFlagsCountsByValue&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by value&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                "canSKip": true,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of risk flags&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;RiskFlagsWithReferralSummary&quot;,
                &quot;columns&quot;: [&quot;r: r-id&quot;, &quot;r: c-id&quot;, &quot;r: name&quot;, &quot;r: service&quot;, &quot;r: project&quot;, &quot;r: received&quot;, &quot;r: worker&quot;, &quot;f-id&quot;, &quot;work date&quot;, &quot;name&quot;, &quot;value&quot;]
                }
                }
                ]
                }
            </column>
            <where>uuid='04600000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2112-repDef-checks" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-09-05T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="41"/>
            <column name="name" value="checks"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2112-repDef-checks-body6" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    "description": "on all checks snapshot smartsteps for the period",
    "selectionCriteria": {
        "selectionRootEntity": "SmartStepSingleSnapshot",
        "selectionPropertyPath": "targetDate",
        "supportEvidenceGroup": "needs",
        "entityStatus": "liveAtEnd",
        "fetchRelatedEntities": [
           "referral"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "smartStepsSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "smartStepsSnapshotByTargetDate"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "smartStepsSnapshotGroupByDue"
            }
        },
        {
            "description": "checks by due",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of entries",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SmartStepSingleSnapshotColumns",
                "columns": [
                    "r: c-id",
                    "r: r-id",
                    "check date",
                    "goal name",
                    "r: service",
                    "r: name",
                    "r: worker"
                ]
            }
        }
    ]
}
            </column>
            <where>uuid='06100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2137-repDef-riskFlagsInRange" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06200000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2021-10-05T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="31"/>
            <column name="name" value="risk flags in range"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2137-repDef-riskFlagsInRange-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "all risk flags",
                "selectionCriteria": {
                "selectionRootEntity": "RiskFlags",
                "referralStatus": "live",
                "selectorType": "byStartOfQuarter",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "newReferralsOnly": false,
                "fetchRelatedEntities": [
                "referral"
                ]
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "riskFlagsCountsByValue"
                }
                },
                {
                "description": "by value",
                "stageType": "CHART",
                "canSkip": true,
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "breakdown of risk flags",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "RiskFlagsWithReferralSummary",
                "columns": [
                "r: r-id",
                "r: c-id",
                "r: name",
                "r: service",
                "r: project",
                "r: received",
                "r: worker",
                "r: status now",
                "f-id",
                "work date",
                "name",
                "value"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06200000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>
    <!-- *** STOP:
     DO NOT ADD ANYTHING MORE HERE
     - USE A CHANGELOG
     in the correct YEAR folder
     *** -->

</databaseChangeLog>
