<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="enable-spidergraphs-on-demo-all-workflow" author="neale" context="1.1-base-data">
<!--
    Could also do: 
 insert into st_referralaspectsettings (id, version, name, value, referralaspectid, servicetypeid) 
    values (317, 0, 'evidenceGraphOptions', 'joinSpindlesOnThreat', 28, 99);
 -->
 
         <insert tableName="servicetypes_referralaspects">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="referralaspectId" valueNumeric="19"/>
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="5"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="315"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="actAs"/>
            <column name="value" value="reduction"/>
            <column name="referralaspectId" valueNumeric="19"/>
            <column name="servicetypeId" valueNumeric="99"/>
        </insert>

         <insert tableName="servicetypes_referralaspects">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="referralaspectId" valueNumeric="28"/>
            <column name="allowNext" valueBoolean="true"/>
            <column name="orderby" valueNumeric="6"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="316"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="actAs"/>
            <column name="value" value="assessment"/>
            <column name="referralaspectId" valueNumeric="28"/>
            <column name="servicetypeId" valueNumeric="99"/>
        </insert>
    </changeSet>

    <changeSet id="appttypes-for-demo-all" author="neale" context="1.1-base-data" >
        <insert tableName="appointmenttypes">
            <column name="id" valueNumeric="1" />
            <column name="version" valueNumeric="0" />
            <column name="serviceId" valueNumeric="99" />
            <column name="disabled" valueBoolean="false" />
            <column name="name" value="one-to-one" />
            <column name="recommendedDuration" value="30" />
            <column name="isDefault" valueBoolean="true" />
        </insert>
    </changeSet>

    <!-- we configure risk management sql above, which shows the spidergraph, but don't give the service type and threat outcomes -->
    <changeSet id="demo-all-servicetypes_outcomethreats" author="adamjhamer" context="1.1-base-data" >
        <insert tableName="servicetypes_outcomethreats">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="threatoutcomeId" valueNumeric="70"/>
        </insert>
        <insert tableName="servicetypes_outcomethreats">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="threatoutcomeId" valueNumeric="71"/>
        </insert>
        <insert tableName="servicetypes_outcomethreats">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="threatoutcomeId" valueNumeric="72"/>
        </insert>
        <insert tableName="servicetypes_outcomethreats">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="threatoutcomeId" valueNumeric="73"/>
        </insert>
    </changeSet>
    <!-- our risk is configured for a 'matrix' so we need likelihood and severity -->
    <changeSet id="demo-all-servicetypes_riskMatrix" author="adamjhamer" context="1.1-base-data" >
        <insert tableName="likelihoods">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="unlikely"/>
            <column name="score" valueNumeric="1"/>
        </insert>
        <insert tableName="likelihoods">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="possible"/>
            <column name="score" valueNumeric="2"/>
        </insert>
        <insert tableName="likelihoods">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="very likely"/>
            <column name="score" valueNumeric="3"/>
        </insert>
        <insert tableName="likelihoods">
            <column name="id" valueNumeric="4"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="almost certain"/>
            <column name="score" valueNumeric="4"/>
        </insert>
        <insert tableName="severity">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="minor"/>
            <column name="score" valueNumeric="1"/>
        </insert>
        <insert tableName="severity">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="moderate"/>
            <column name="score" valueNumeric="2"/>
        </insert>
        <insert tableName="severity">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="significant"/>
            <column name="score" valueNumeric="3"/>
        </insert>
        <insert tableName="severity">
            <column name="id" valueNumeric="4"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="serious"/>
            <column name="score" valueNumeric="4"/>
        </insert>
        <insert tableName="severity">
            <column name="id" valueNumeric="5"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="major"/>
            <column name="score" valueNumeric="5"/>
        </insert>
    </changeSet>
    <!-- the riskMatrix we want above doesn't show because we have no actionssmart steps on the configured outcomes -->
    <!-- configure one more just to show the feature, rather than remove so we don't break existing flows -->
    <!-- can choose between 50,51,52 -->
    <changeSet id="demo-all-servicetypes_outcomethreats-more" author="adamjhamer" context="1.1-base-data" >
        <insert tableName="servicetypes_outcomethreats">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="threatoutcomeId" valueNumeric="51"/>
        </insert>
     </changeSet>

    <changeSet id="demo-all-managerNotes-section" author="adamjhamer" context="1.1-base-data" >
         <insert tableName="servicetypes_referralaspects">
            <column name="servicetypeId" valueNumeric="1"/>
            <column name="referralaspectId" valueNumeric="103"/>
            <column name="allowNext" valueBoolean="true"/>
            <column name="orderby" valueNumeric="12"/>
            <column name="version" valueNumeric="0"/>
        </insert>
     </changeSet>

    <changeSet id="demo-all-boldTabs" author="adamjhamer" context="1.1-base-data" >
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="317"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="showOutcomeIndicators"/>
            <column name="value" value="hasHistory"/>
            <column name="referralaspectId" valueNumeric="31"/>
            <column name="servicetypeId" valueNumeric="1"/>
        </insert>
     </changeSet>

    <changeSet id="demo-all-endTime" author="adamjhamer" context="1.1-base-data" >
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="318"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="tookPlaceOn"/>
            <column name="value" value="startEnd"/>
            <column name="referralaspectId" valueNumeric="31"/>
            <column name="servicetypeId" valueNumeric="1"/>
        </insert>
     </changeSet>
    <changeSet id="demo-all-data-protection-in-wizard" author="nealeu" context="1.1-base-data" >
        <update tableName="servicetypes_referralaspects">
            <column name="orderby" valueNumeric="7"/>
            <where>servicetypeId=1 AND referralaspectId=18</where>
        </update>
     </changeSet>

</databaseChangeLog>