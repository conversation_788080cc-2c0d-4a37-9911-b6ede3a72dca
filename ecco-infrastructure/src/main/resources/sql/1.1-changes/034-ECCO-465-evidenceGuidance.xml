<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet author="nealeu" id="ECCO-465-createTable">
        <createTable tableName="evidenceguidance">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="guidanceMarkdown" type="CLOB"/>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-465-add-outcomes-column" author="nealeu">
        <addColumn tableName="outcomes">
            <column name="evidenceguidanceId" type="BIGINT">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-465-add-outcomes-column-fk" author="nealeu">
        <addForeignKeyConstraint baseColumnNames="evidenceguidanceId" baseTableName="outcomes"
            constraintName="fk_outcomes_evidenceguidanceId"
            deferrable="false" initiallyDeferred="false"
            onDelete="NO ACTION" onUpdate="NO ACTION"
            referencedColumnNames="id" referencedTableName="evidenceguidance"
            referencesUniqueColumn="false" />
    </changeSet>

    <changeSet id="ECCO-465-add-qg-column" author="nealeu">
        <addColumn tableName="questiongroups">
            <column name="evidenceguidanceId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-465-add-qg-column-fk" author="nealeu">
        <addForeignKeyConstraint baseColumnNames="evidenceguidanceId" baseTableName="questiongroups"
            constraintName="fk_qugroups_evidenceguidanceId"
            deferrable="false" initiallyDeferred="false"
            onDelete="NO ACTION" onUpdate="NO ACTION"
            referencedColumnNames="id" referencedTableName="evidenceguidance"
            referencesUniqueColumn="false" />
    </changeSet>

</databaseChangeLog>
