<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- NB THREAT FLAGS MIGRATION to listdef is cloned from religions migration to listdef -->

    <!-- for mssql, not oracle, we can't modify a foreign key easily without removing the hidden default first -->
    <!-- mssql creates its own constraints for 'default' values which don't appear in information_schema -->
    <!-- so we need a way of getting the name to delete them -->
    <!-- useful http://stackoverflow.com/questions/7663390/why-does-sql-keep-creating-a-df-constraint -->
    <!-- and http://stackoverflow.com/questions/1430456/how-to-drop-sql-default-constraint-without-knowing-its-name -->
    <changeSet id="DEV-1617-listDef-flags-types-mssql" author="adamjhamer" dbms="mssql">
        <!-- NOTE there are no ';' or GO apart from where we specifically want to break up the batch -->
        <!-- and there are no line breaks between the batched statements -->
        <sql>
            DECLARE @ObjectName NVARCHAR(100)
            SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
            WHERE [object_id] = OBJECT_ID('[dbo].[servicetypes_flagthreats]') AND [name] = 'flagId'
            EXEC('ALTER TABLE [dbo].[servicetypes_flagthreats] DROP CONSTRAINT ' + @ObjectName)
            ;
            DECLARE @ObjectName NVARCHAR(100)
            SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
            WHERE [object_id] = OBJECT_ID('[dbo].[supportthreatflags]') AND [name] = 'flagId'
            EXEC('ALTER TABLE [dbo].[supportthreatflags] DROP CONSTRAINT ' + @ObjectName)
            ;
        </sql>
    </changeSet>

    <!-- STEP 1: drop fks, convert to int -->
    <changeSet id="DEV-1617-listDef-flags-prep" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="servicetypes_flagthreats" constraintName="FKEB3A67D24D12C76"/>
        <dropForeignKeyConstraint baseTableName="supportthreatflags" constraintName="FK5BC2797E4D12C76"/>
    </changeSet>
    <!-- STEP 1: rename to defId, and convert to int -->
    <changeSet id="DEV-1617-listDef-flags-defs" author="adamjhamer">
        <renameColumn tableName="servicetypes_flagthreats" oldColumnName="flagId" newColumnName="flagDefId" columnDataType="BIGINT"/>
        <renameColumn tableName="supportthreatflags" oldColumnName="flagId" newColumnName="flagDefId" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="DEV-1617-listDef-flags-types" author="adamjhamer">
        <!-- NB casting long->int unlikely to work on other dbs -->
        <modifyDataType tableName="servicetypes_flagthreats" columnName="flagDefId" newDataType="INT"/>
        <modifyDataType tableName="supportthreatflags" columnName="flagDefId" newDataType="INT"/>
    </changeSet>

    <!-- STEP 2: fill migrationId from the sequence -->
    <changeSet id="DEV-1617-flags-flagDefId" author="adamjhamer">
        <addColumn tableName="flags">
            <column name="migrationId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1617-listDef-flags-populate-seq" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="flags" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>

    <!-- STEP 3: migrate -->
    <changeSet id="DEV-1617-listDef-flags-copy-rows" author="adamjhamer">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'flags-risk', name, concat('risk-', id) FROM flags;
        </sql>
    </changeSet>
    <!-- STEP 4: fks -->
    <changeSet id="DEV-1617-listDef-flags-update-refs" author="adamjhamer">
        <update tableName="servicetypes_flagthreats">
            <column name="flagDefId" valueComputed="(SELECT migrationId FROM flags WHERE id = servicetypes_flagthreats.flagDefId)"/>
        </update>
        <update tableName="supportthreatflags">
            <column name="flagDefId" valueComputed="(SELECT migrationId FROM flags WHERE id = supportthreatflags.flagDefId)"/>
        </update>
        <addForeignKeyConstraint baseTableName="servicetypes_flagthreats" baseColumnNames="flagDefId"
                                 constraintName="fk_stthreats_flagDefId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="supportthreatflags" baseColumnNames="flagDefId"
                                 constraintName="fk_suppthreats_flagDefId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <!--
     - migrate the non-FKs
     - TODO we could remove servicetypes_flagthreats and flagThreatsById if we created a list def per service (to cope with the combinations of disabled ones)
    -->
    <changeSet id="DEV-1617-listDef-flags-populate-settings" author="adamjhamer">
        <validCheckSum>8:95db5ce85a6a8350612bfa5cfc408da7</validCheckSum>
        <customChange class="com.ecco.infrastructure.liquibase.CsvTransposeChange"
                      tableName="st_referralaspectsettings" primaryKeyColumns="id" whereClause="where name='flagThreatsById'"
                      csvColumnName="value"
                      mappingTable="flags" mappingSrcColumnName="id" mappingDstColumnName="migrationId">
        </customChange>
    </changeSet>

    <!-- SUPPORT PLAN FLAGS -->
    <!-- foreign keys can't be in evidenceDomainChangeLog only when evidence-domain is before generalChangeLog where these referenced tables are created -->
    <changeSet id="DEV-1617-supportplanflags-fks" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_ev_flags_work"
                                 baseTableName="supportplanflags" baseColumnNames="workUuid"
                                 referencedTableName="supportplanwork" referencedColumnNames="uuid" referencesUniqueColumn="true"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>

        <addForeignKeyConstraint constraintName="fk_ev_flags_svcrec"
                                 baseTableName="supportplanflags" baseColumnNames="serviceRecipientId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id" referencesUniqueColumn="true"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>

        <addForeignKeyConstraint constraintName="fk_ev_flags_listdef"
                                 baseTableName="supportplanflags" baseColumnNames="flagDefId"
                                 referencedColumnNames="id" referencedTableName="cfg_list_definitions" referencesUniqueColumn="true"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>
    </changeSet>

    <!-- *** STOP: DO NOT ADD ANYTHING MORE HERE - USE A CHANGELOG in the correct YEAR folder *** -->

</databaseChangeLog>
