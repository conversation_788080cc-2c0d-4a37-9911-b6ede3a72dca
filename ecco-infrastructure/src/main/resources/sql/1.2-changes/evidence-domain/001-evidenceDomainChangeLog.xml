<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- HANDLES: (based on search for <createTable)
     - see main evidenceDomainChangeLog.xml for what tables are involved in the domain
    -->

    <changeSet id="DEV-1617-supportplanflags" author="adamjhamer">
        <createTable tableName="supportplanflags">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="workUuid" type="CHAR(36)"><constraints nullable="false"/></column>
            <column name="serviceRecipientId" type="INT"/>
            <column name="version" type="INT"/>
            <column name="created" type="DATETIME"/>
            <column name="value" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="flagDefId" type="INT"/>
        </createTable>

        <createIndex tableName="supportplanflags" indexName="idx_ev_flags_workuuid">
            <column name="workUuid"/>
        </createIndex>

        <createIndex tableName="supportplanflags" indexName="idx_ev_flags_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1617-supportplanflags-flagDefId-idx" author="adamjhamer">
        <createIndex tableName="supportplanflags" indexName="idx_ev_flags_fid">
            <column name="flagDefId"/>
        </createIndex>
    </changeSet>

    <!-- see also DEV-2223-riskplanflags-workdate -->
    <changeSet id="DEV-1617-supportplanflags-workdate" author="adamjhamer">
        <addColumn tableName="supportplanflags">
            <column name="workdate" type="DATETIME"/>
        </addColumn>
    </changeSet>

    <!-- *** STOP: DO NOT ADD ANYTHING MORE HERE - USE A CHANGELOG in the correct YEAR folder *** -->

</databaseChangeLog>
