<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- if this fails on a site, then just create ecco_external manually BUT NEEDS TO HAVE id 3 in both for audits to be ecco_external -->
    <!-- the full solution is in move_cosmo_ids.sql -->
    <!--
    select max(orderexecuted) from DATABASECHANGELOG;
    insert into DATABASECHANGELOG (id, author, filename, dateexecuted, orderexecuted, exectype, md5sum, description, comments, tag, liquibase)
    values ('DEV-513-ecco_external-user', 'adamjhamer', 'classpath:sql/1.2-changes/general-domain/004-dataImportUser.xml', now(), [ID], 'EXECUTED', null, 'manual ignored', '', null, '3.5.3');
    -->
    <changeSet id="DEV-513-ecco_external-user" author="adamjhamer" context="!test-data AND !1.1-apply-encryption" dbms="!oracle">
    <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from users where username='ecco_external'</sqlCheck>
        </preConditions>
        <insert tableName="users">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="username" value="ecco_external"/>
            <column name="activedirectoryuser" valueBoolean="false"/>
            <column name="country" value="GB"/>
            <column name="domain"/>
            <column name="enabled" valueBoolean="false"/>
            <column name="failedloginattempts" valueNumeric="0"/>
            <column name="lastloggedin"/>
            <column name="locale" value="en_GB"/>
            <column name="password" value="8b139355b09ea41e0e11be8cbde37114056b9bae"/>
            <column name="registered" valueDate="2019-03-15T15:22:39.0"/>
            <column name="timezone" value="Europe/London"/>
        </insert>
        <insert tableName="contacts">
            <column name="discriminator_orm" value="individual"/>
            <column name="usersid" valueNumeric="3"/>
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="addresscountry" value="GB"/>
            <column name="email" value="<EMAIL>"/>
            <column name="keyholder" valueBoolean="false"/>
            <column name="livingwithclient" valueBoolean="false"/>
            <column name="firstname" value="data"/>
            <column name="lastname" value="import"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="10"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_id" valueNumeric="2"/> <!-- staff -->
            <column name="member_id" value="3"/>
        </insert>
    </changeSet>
    <!-- NB probably best as part of calendar-domain?? -->
    <changeSet id="DEV-513-ecco_external-user-calendar" author="adamjhamer" context="!test-data AND !1.1-apply-encryption" dbms="!oracle">
        <validCheckSum>8:f3ebb3d6092dd6f34b331935923c6dfa</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from cosmo_users where username='ecco_external'</sqlCheck>
        </preConditions>
        <insert tableName="cosmo_users">
            <column name="id" valueNumeric="3"/> <!-- 2 is syadmin for some reason, but don't use 1 as that is sysadmin on ecco -->
            <column name="createdate" valueNumeric="1590098323264"/>
            <column name="etag" value="vE907P+jncJK83ynoDrmAk+sEFU="/>
            <column name="modifydate" valueNumeric="1590098323264"/>
            <column name="activationid"/>
            <column name="admin" valueBoolean="true"/>
            <column name="email" value="import@null"/>
            <column name="firstname"/>
            <column name="lastname"/>
            <column name="locked" valueBoolean="false"/>
            <column name="password" value="11ea881985e5eacc41aed87ed4f01fc5"/>
            <column name="user_uid" value="9b7a6a4b-65b1-48e7-a44b-42827068884d"/>
            <column name="username" value="ecco_external"/>
        </insert>
        <insert tableName="cosmo_item">
            <column name="itemtype" value="homecollection"/>
            <column name="id" valueNumeric="3"/>
            <column name="createdate" valueNumeric="1590098323300"/>
            <column name="etag" value="LAq/Zs0YyFe0sDr1koYOmtxjrMU="/>
            <column name="modifydate" valueNumeric="1590098323300"/>
            <column name="clientcreatedate"/>
            <column name="clientmodifieddate"/>
            <column name="displayname" value="ecco_external"/>
            <column name="itemname" value="ecco_external"/>
            <column name="item_uid" value="171cba1c-f3db-452c-9e6e-5e85c830b36e"/>
            <column name="version" valueNumeric="0"/>
            <column name="lastmodification"/>
            <column name="lastmodifiedby"/>
            <column name="needsreply"/>
            <column name="sent"/>
            <column name="isautotriage"/>
            <column name="triagestatuscode"/>
            <column name="triagestatusrank"/>
            <column name="icaluid"/>
            <column name="contentencoding"/>
            <column name="contentlanguage"/>
            <column name="contentlength"/>
            <column name="contenttype"/>
            <column name="hasmodifications"/>
            <column name="ownerid" valueNumeric="3"/>
            <column name="contentdataid"/>
            <column name="modifiesitemid"/>
        </insert>
        <insert tableName="cosmo_item">
            <column name="itemtype" value="collection"/>
            <column name="id" valueNumeric="4"/>
            <column name="createdate" valueNumeric="1590098323325"/>
            <column name="etag" value="gmZmSKPbsFv/ngvKCSvXRI0JnAU="/>
            <column name="modifydate" valueNumeric="1590098323325"/>
            <column name="clientcreatedate"/>
            <column name="clientmodifieddate"/>
            <column name="displayname" value="SystemCollection"/>
            <column name="itemname" value="SystemCollection"/>
            <column name="item_uid" value="f7390ae6-61c4-4abb-9a54-6adee060fe17"/>
            <column name="version" valueNumeric="0"/>
            <column name="lastmodification"/>
            <column name="lastmodifiedby"/>
            <column name="needsreply"/>
            <column name="sent"/>
            <column name="isautotriage"/>
            <column name="triagestatuscode"/>
            <column name="triagestatusrank"/>
            <column name="icaluid"/>
            <column name="contentencoding"/>
            <column name="contentlanguage"/>
            <column name="contentlength"/>
            <column name="contenttype"/>
            <column name="hasmodifications"/>
            <column name="ownerid" valueNumeric="3"/>
            <column name="contentdataid"/>
            <column name="modifiesitemid"/>
        </insert>
        <insert tableName="cosmo_collection_item">
            <column name="createdate" valueNumeric="1590098323320"/>
            <column name="itemid" valueNumeric="4"/>
            <column name="collectionid" valueNumeric="3"/>
        </insert>
        <update tableName="contacts">
            <column name="calendarId" value="f7390ae6-61c4-4abb-9a54-6adee060fe17"/>
            <where>id=3</where>
        </update>
    </changeSet>


    <!-- CLONED for oracle -->
    <changeSet id="DEV-513-ecco_external-user" author="adamjhamer" context="!test-data AND !1.1-apply-encryption" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from users where username='ecco_external'</sqlCheck>
        </preConditions>
        <insert tableName="users">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="username" value="ecco_external"/>
            <column name="activedirectoryuser" valueBoolean="false"/>
            <column name="country" value="GB"/>
            <column name="domain"/>
            <column name="enabled" valueBoolean="false"/>
            <column name="failedloginattempts" valueNumeric="0"/>
            <column name="lastloggedin"/>
            <column name="locale" value="en_GB"/>
            <column name="PASSWORD" value="8b139355b09ea41e0e11be8cbde37114056b9bae"/>
            <column name="registered" valueDate="2019-03-15T15:22:39.0"/>
            <column name="timezone" value="Europe/London"/>
        </insert>
        <insert tableName="contacts">
            <column name="discriminator_orm" value="individual"/>
            <column name="usersid" valueNumeric="3"/>
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="addresscountry" value="GB"/>
            <column name="email" value="<EMAIL>"/>
            <column name="keyholder" valueBoolean="false"/>
            <column name="livingwithclient" valueBoolean="false"/>
            <column name="firstname" value="data"/>
            <column name="lastname" value="import"/>
        </insert>
        <insert tableName="group_members">
            <column name="id" valueNumeric="10"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_id" valueNumeric="2"/> <!-- staff -->
            <column name="member_id" value="3"/>
        </insert>
    </changeSet>
    <!-- NB probably best as part of calendar-domain?? -->
    <changeSet id="DEV-513-ecco_external-user-calendar" author="adamjhamer" context="!test-data AND !1.1-apply-encryption" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from cosmo_users where username='ecco_external'</sqlCheck>
        </preConditions>
        <insert tableName="cosmo_users">
            <column name="id" valueNumeric="3"/> <!-- 2 is syadmin for some reason, but don't use 1 as that is sysadmin on ecco -->
            <column name="createdate" valueNumeric="1590098323264"/>
            <column name="etag" value="vE907P+jncJK83ynoDrmAk+sEFU="/>
            <column name="modifydate" valueNumeric="1590098323264"/>
            <column name="activationid"/>
            <column name="admin" valueBoolean="true"/>
            <column name="email" value="import@null"/>
            <column name="firstname"/>
            <column name="lastname"/>
            <column name="locked" valueBoolean="false"/>
            <column name="PASSWORD" value="11ea881985e5eacc41aed87ed4f01fc5"/>
            <column name="user_uid" value="9b7a6a4b-65b1-48e7-a44b-42827068884d"/>
            <column name="username" value="ecco_external"/>
        </insert>
        <insert tableName="cosmo_item">
            <column name="itemtype" value="homecollection"/>
            <column name="id" valueNumeric="3"/>
            <column name="createdate" valueNumeric="1590098323300"/>
            <column name="etag" value="LAq/Zs0YyFe0sDr1koYOmtxjrMU="/>
            <column name="modifydate" valueNumeric="1590098323300"/>
            <column name="clientcreatedate"/>
            <column name="clientmodifieddate"/>
            <column name="displayname" value="ecco_external"/>
            <column name="itemname" value="ecco_external"/>
            <column name="item_uid" value="171cba1c-f3db-452c-9e6e-5e85c830b36e"/>
            <column name="version" valueNumeric="0"/>
            <column name="lastmodification"/>
            <column name="lastmodifiedby"/>
            <column name="needsreply"/>
            <column name="sent"/>
            <column name="isautotriage"/>
            <column name="triagestatuscode"/>
            <column name="triagestatusrank"/>
            <column name="icaluid"/>
            <column name="contentencoding"/>
            <column name="contentlanguage"/>
            <column name="contentlength"/>
            <column name="contenttype"/>
            <column name="hasmodifications"/>
            <column name="ownerid" valueNumeric="3"/>
            <column name="contentdataid"/>
            <column name="modifiesitemid"/>
        </insert>
        <insert tableName="cosmo_item">
            <column name="itemtype" value="collection"/>
            <column name="id" valueNumeric="4"/>
            <column name="createdate" valueNumeric="1590098323325"/>
            <column name="etag" value="gmZmSKPbsFv/ngvKCSvXRI0JnAU="/>
            <column name="modifydate" valueNumeric="1590098323325"/>
            <column name="clientcreatedate"/>
            <column name="clientmodifieddate"/>
            <column name="displayname" value="SystemCollection"/>
            <column name="itemname" value="SystemCollection"/>
            <column name="item_uid" value="f7390ae6-61c4-4abb-9a54-6adee060fe17"/>
            <column name="version" valueNumeric="0"/>
            <column name="lastmodification"/>
            <column name="lastmodifiedby"/>
            <column name="needsreply"/>
            <column name="sent"/>
            <column name="isautotriage"/>
            <column name="triagestatuscode"/>
            <column name="triagestatusrank"/>
            <column name="icaluid"/>
            <column name="contentencoding"/>
            <column name="contentlanguage"/>
            <column name="contentlength"/>
            <column name="contenttype"/>
            <column name="hasmodifications"/>
            <column name="ownerid" valueNumeric="3"/>
            <column name="contentdataid"/>
            <column name="modifiesitemid"/>
        </insert>
        <insert tableName="cosmo_collection_item">
            <column name="createdate" valueNumeric="1590098323320"/>
            <column name="itemid" valueNumeric="4"/>
            <column name="collectionid" valueNumeric="3"/>
        </insert>
        <update tableName="contacts">
            <column name="calendarId" value="f7390ae6-61c4-4abb-9a54-6adee060fe17"/>
            <where>id=3</where>
        </update>
    </changeSet>

</databaseChangeLog>
