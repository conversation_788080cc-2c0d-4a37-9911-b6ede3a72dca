<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
    logicalFilePath="classpath:sql/1.2-changes/security-domain/001-securityDomainChangeLog.xml"
>

    <!-- HANDLES: (based on search for <createTable)
     - see main securityDomainChangeLog.xml for what tables are involved im the domain
    -->

    <!-- schema context as all want this change -->
    <changeSet author="adamjhamer" id="DEV-714-adminevidence-to-sysadmin" context="!test-data">
        <!-- Disabled for test-data for now as we don't insert sysadmin for that as it is 1.1-base-data context -->
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from group_members where member_id=1 and group_id=23</sqlCheck>
        </preConditions>
        <insert tableName="group_members">
            <column name="id" valueNumeric="9"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_id" valueNumeric="23"/>
            <column name="member_id" valueNumeric="1"/>
        </insert>
    </changeSet>

    <!-- 'admin referral' as an additional security role, for more fine grained control over seeing the deletion requests, and acting on them -->
    <!-- to switch for a customer, do: -->
    <!--        delete from group_authorities where group_id=11 and authority='ROLE_DELETEREFERRAL'; -->
    <!--        select * from setting where keyname='GroupsToExclude'; -->
    <!--        update setting set keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,admin evidence' where keyname='GroupsToExclude'; -->
    <changeSet author="adamjhamer" id="z3041-adminreferral-group">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="24"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="admin referral"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="24"/>
            <column name="authority" value="ROLE_SOFTDELETE"/> <!-- see the menu item -->
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="24"/>
            <column name="authority" value="ROLE_DELETEREFERRAL"/>
        </insert>
    </changeSet>

    <!-- ROLE_OVERVIEWREFERRAL_TASKS - for more fine grained control over seeing just the tasks info on a referral -->
    <changeSet author="adamjhamer" id="DEV-1228-overseerAccess-tasks-agreements">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="25"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="overseer-tasks-1"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="25"/>
            <column name="authority" value="ROLE_OVERVIEWREFERRAL_TASKS"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="25"/>
            <column name="authority" value="ROLE_OVERVIEWREFERRAL_TASKS_1"/>
        </insert>
    </changeSet>

    <!-- carer so that we can distinguish the menu item for them -->
    <changeSet id="DEV-1125-carerAccess" author="adamjhamer">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="26"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="carer"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="26"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="26"/>
            <column name="authority" value="ROLE_STAFF"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="26"/>
            <column name="authority" value="ROLE_CARER"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1486-financeAccess" author="adamjhamer">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="27"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="finance"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="27"/>
            <column name="authority" value="ROLE_FINANCE"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1629-usr-commands" author="adamjhamer">
        <createTable tableName="usr_commands">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="commandname" type="VARCHAR(63)">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="true"/>
            </column>
            <column name="commandCreated" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="userid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="userIdSubject" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint constraintName="fk_usr_cmds_author" baseTableName="usr_commands"
                                 baseColumnNames="userid" referencedTableName="users" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_usr_cmds_user" baseTableName="usr_commands"
                                 baseColumnNames="userIdSubject" referencedTableName="users" referencedColumnNames="id" />
        <createIndex tableName="usr_commands" indexName="idx_usr_commands_uuid">
            <column name="uuid"/>
        </createIndex>
        <createIndex tableName="usr_commands" indexName="idx_usr_cmd_created">
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="usr_commands" indexName="idx_usr_cmd_subj_created">
            <column name="userIdSubject"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="usr_commands" indexName="idx_usr_cmd_name_subj_ctd">
            <column name="userIdSubject"/>
            <column name="commandname"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>


    <changeSet id="DEV-1668-buildingAdmin" author="adamjhamer">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="28"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="admin building"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="28"/>
            <column name="authority" value="ROLE_ADMINBUILDING"/>
        </insert>
    </changeSet>

    <!-- schema context as all want this change -->
    <changeSet id="DEV-1668-adminbuilding-sysadmin" author="adamjhamer" context="!test-data">
        <!-- disabled for test-data for now as we don't insert sysadmin for that as it is 1.1-base-data context -->
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from group_members where member_id=1 and group_id=28</sqlCheck>
        </preConditions>
        <insert tableName="group_members">
            <column name="id" valueNumeric="11"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_id" valueNumeric="28"/>
            <column name="member_id" valueNumeric="1"/>
        </insert>
    </changeSet>

    <!-- *** STOP: DO NOT ADD ANYTHING MORE HERE - USE A CHANGELOG in the correct YEAR folder *** -->

</databaseChangeLog>
