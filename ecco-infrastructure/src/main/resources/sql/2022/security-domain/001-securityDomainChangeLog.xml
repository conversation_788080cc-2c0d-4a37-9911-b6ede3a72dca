<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2022/security-domain">

    <changeSet id="DEV-2253-admincalendar" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="30"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="admin calendar"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="30"/>
            <column name="authority" value="ROLE_ADMINCALENDAR"/>
        </insert>
    </changeSet>

    <!-- schema context as all want this change -->
    <changeSet id="DEV-2253-admincalendar-sysadmin" author="adamjhamer" context="!test-data">
        <!-- disabled for test-data for now as we don't insert sysadmin for that as it is 1.1-base-data context -->
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from group_members where member_id=1 and group_id=30</sqlCheck>
        </preConditions>
        <insert tableName="group_members">
            <column name="id" valueNumeric="18"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_id" valueNumeric="30"/>
            <column name="member_id" valueNumeric="1"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2254-mfa-users" author="adamjhamer">
        <addColumn tableName="users">
            <column name="mfaSecret" type="VARCHAR(32)"/>
            <column name="mfaRequired" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-2254-mfa-users-AUD" author="adamjhamer">
        <addColumn tableName="users_AUD">
            <column name="mfaSecret" type="VARCHAR(32)"/>
            <column name="mfaRequired" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- ROLE_OVERVIEWREFERRAL_TASKS - seeing just the tasks info on a referral -->
    <changeSet author="adamjhamer" id="DEV-1228-overseerAccess-tasks">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="31"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="overseer-tasks"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="31"/>
            <column name="authority" value="ROLE_OVERVIEWREFERRAL_TASKS"/>
        </insert>
    </changeSet>

    <!-- for staff: insert into group_authorities (group_id, authority) values (2, 'ROLE_EDITEVIDENCEPARKED'); -->
    <!--<changeSet id="DEV-2329-parked-edit-staff" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="2"/> &lt;!&ndash; staff &ndash;&gt;
            <column name="authority" value="ROLE_EDITEVIDENCEPARKED"/>
        </insert>
    </changeSet>-->
    <changeSet id="DEV-2329-parked-edit-manager-and-senior" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager, as per ROLE_ADMINEVIDENCE -->
            <column name="authority" value="ROLE_EDITEVIDENCEPARKED"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_EDITEVIDENCEPARKED"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-2329-parked-edit-for-all" author="adamjhamer" context="!test-data">
        <delete tableName="group_authorities">
            <where>authority='ROLE_EDITEVIDENCEPARKED'</where>
        </delete>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="2"/> <!-- staff -->
            <column name="authority" value="ROLE_EDITEVIDENCEPARKED"/>
        </insert>
    </changeSet>
    <!-- we do need to duplicate for managers! -->
    <changeSet id="DEV-2329-parked-edit-manager-and-senior-dupl" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager, as per ROLE_ADMINEVIDENCE -->
            <column name="authority" value="ROLE_EDITEVIDENCEPARKED"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_EDITEVIDENCEPARKED"/>
        </insert>
    </changeSet>

    <changeSet id="OPS-101-rename-AUD-to-aud" author="nealeu" dbms="!h2">
        <renameTable oldTableName="group_members_AUD" newTableName="group_members_aud"/>
        <renameTable oldTableName="userdevices_AUD" newTableName="userdevices_aud"/>
        <renameTable oldTableName="users_AUD" newTableName="users_aud"/>
    </changeSet>
</databaseChangeLog>