<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
                   logicalFilePath="classpath:sql/1.1-changes/008-migrate-menuReferral-to-menu.xml">

    <changeSet author="default" id="ECCO-133-migrate-menuReferral">
        <insert tableName="menu">
            <column name="name" value="referrals" />
        </insert>

        <insert tableName="menuitem">
            <column name="id" valueNumeric="31"/>
            <column name="imageUrl" value="/icons/crystal/documentadd/png/48.png"/>
            <column name="linkText" value="referralView.newMultipleReferral"/>
            <column name="roles" value="ROLE_SYSADMIN,ROLE_STAFF"/>
            <column name="url" value="/dynamic/secure/referralAspectFlow.html?newReferral=true"/>
            <column name="module_name" value="core"/>
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="referrals"/>
            <column name="menuItems_id" valueNumeric="31"/>
        </insert>

        <insert tableName="menuitem">
            <column name="id" valueNumeric="32"/>
            <column name="imageUrl" value="/icons/crystal/search/png/48.png"/>
            <column name="linkText" value="menu.linktext.find_referral"/>
            <column name="roles" value="ROLE_SYSADMIN,ROLE_STAFF"/>
            <column name="url" value="/dynamic/secure/referralAspectClientFlow.html"/>
            <column name="module_name" value="core"/>
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="referrals"/>
            <column name="menuItems_id" valueNumeric="32"/>
        </insert>

        <insert tableName="menuitem">
            <column name="id" valueNumeric="33"/>
            <column name="imageUrl" value="/icons/crystal/import/png/48.png"/>
            <column name="linkText" value="menu.linktext.list_referrals"/>
            <column name="roles" value="ROLE_SYSADMIN,ROLE_STAFF,ROLE_COMMISSIONER"/>
            <column name="url" value="/dynamic/secure/entities/referrals/get.html"/>
            <column name="module_name" value="core"/>
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="referrals"/>
            <column name="menuItems_id" valueNumeric="33"/>
        </insert>
    </changeSet>

    <!-- all sites have the 'list' first by default, so make that the default -->
    <changeSet author="adamjhamer" id="ECCO-133-2">
        <update tableName="menuitem">
            <column name="imageUrl" value="/icons/crystal/import/png/48.png"/>
            <column name="linkText" value="menu.linktext.list_referrals"/>
            <column name="roles" value="ROLE_SYSADMIN,ROLE_STAFF,ROLE_COMMISSIONER"/>
            <column name="url" value="/dynamic/secure/entities/referrals/get.html"/>
            <column name="module_name" value="core"/>
            <where>id = 31</where>
        </update>
        <update tableName="menuitem">
            <column name="imageUrl" value="/icons/crystal/documentadd/png/48.png"/>
            <column name="linkText" value="referralView.newMultipleReferral"/>
            <column name="roles" value="ROLE_SYSADMIN,ROLE_STAFF"/>
            <column name="url" value="/dynamic/secure/referralAspectFlow.html?newReferral=true"/>
            <column name="module_name" value="core"/>
            <where>id = 33</where>
        </update>
    </changeSet>

</databaseChangeLog>
