<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- HANDLES: (based on search for <createTable)
     - TODO for supportplanwork etc, but just a placeholder for now until we move changes here
    -->

    <changeSet id="hibernate_sequences" author="bodeng">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="hibernate_sequences"/>
            </not>
        </preConditions>
        <createTable tableName="hibernate_sequences">
            <column name="sequence_name" type="VARCHAR(40)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="next_val" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <include file="classpath:sql/1.2-changes/evidence-domain/001-evidenceDomainChangeLog.xml"/>

    <!-- Feel free to add to existing file above or do a new file, by year-month-onwards for larger changes
         We don't need to create a new changelog file for every change -->

</databaseChangeLog>
