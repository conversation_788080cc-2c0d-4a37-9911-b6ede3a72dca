<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/0002-security-domain/901-security-domain-2015-12-onwards-changes.xml">

    <changeSet id="change_group_members_to_use_user_id" author="bodeng">
        <comment>Hibernate Envers gets confused using the natural key join column so we need to use the primary key.</comment>
        <addColumn tableName="group_members">
            <column name="member_id" type="BIGINT"/>
        </addColumn>
        <update tableName="group_members">
            <column name="member_id"
                valueComputed="(SELECT id FROM users WHERE users.username = group_members.username)"/>
        </update>
        <addNotNullConstraint tableName="group_members" columnName="member_id" columnDataType="BIGINT"/>
        <addForeignKeyConstraint baseTableName="group_members" baseColumnNames="member_id" constraintName="fk_group_members_member_id"
                                 referencedTableName="users" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="rename_group_members_aud_username" author="bodeng">
        <comment>In Hibernate 3, Envers was recording the member_id as username. Rename it so it's correct.</comment>
        <renameColumn tableName="group_members_AUD" oldColumnName="username" newColumnName="member_id" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="first-remove-dependent-index" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="group_members" columnName="username"/>
            <indexExists tableName="group_members" indexName="idx_group_members_username"/>
        </preConditions>
        <dropIndex tableName="group_members" indexName="idx_group_members_username"/>
    </changeSet>

    <changeSet id="remove_username_from_group_members" author="bodeng" dbms="!h2">
        <comment>Because we can't identify the name of the username FK, we need to drop them all and re-add.</comment>
        <dropAllForeignKeyConstraints baseTableName="group_members"/>
        <dropColumn tableName="group_members" columnName="username"/>
        <addForeignKeyConstraint baseTableName="group_members" baseColumnNames="group_id"
                                 constraintName="fk_group_members_group_id" referencedTableName="groups" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="group_members" baseColumnNames="member_id" constraintName="fk_group_members_member_id"
                                 referencedTableName="users" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="remove_username_from_group_members" author="bodeng" dbms="h2">
        <comment>H2 doesn't support dropAllForeignKeyConstraints...</comment>
        <dropColumn tableName="group_members" columnName="username"/>
    </changeSet>

    <!-- a new group ('service admin') because:
           - ideally we give service admin to all managers, but we don't yet have service-type access to global areas (eg group support activities)
             and therefore we don't want all 'managers' or 'senior managers' to have this functionality
           - customers can change accordingly using existing ui (ie no sql)
    -->
    <!-- this is a completely new security group, so its OK to add it all here and we may well want it in tests -->
    <changeSet author="adamjhamer" id="ECCO-2011-create-service-admin">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="20"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="service admin"/>
        </insert>
      <!-- give its first role, ROLE_ADMINGROUPSUPPORT, for more fine grained control over group support admin -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="20"/>
            <column name="authority" value="ROLE_ADMINGROUPSUPPORT"/>
        </insert>
        <!-- we don't add a ROLE_USER as a site manager user is very likely to be an existing manager/senior manager -->
        <!-- its just that we don't yet want to give all managers this fn yet -->
    </changeSet>
    <!-- we retain ROLE_ADMINGROUPSUPPORT for 'site sysadmin' group so we can just test for this permission in the code -->
    <changeSet author="adamjhamer" id="ECCO-2011-admingroupsupport-for-siteadmin">
        <validCheckSum>8:2852fc831590abcb90d1bc9eedd0f93f</validCheckSum>
        <!-- Removed preCondition for migration of old sites where we may already have added this change manually -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="19"/> <!-- site sysadmin -->
            <column name="authority" value="ROLE_ADMINGROUPSUPPORT"/>
        </insert>
    </changeSet>
    <!-- we retain ROLE_ADMINGROUPSUPPORT for 'sysadmin' group so we can just test for this permission in the code -->
    <changeSet author="adamjhamer" id="ECCO-2011-admingroupsupport-for-sysadmin">
        <validCheckSum>8:a3c3289df54335ad055e19078a9dd548</validCheckSum>
        <!-- Removed preCondition for migration of old sites where we may already have added this change manually -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="8"/> <!-- sysadmin -->
            <column name="authority" value="ROLE_ADMINGROUPSUPPORT"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-2198-commissioner_ROLE_USER">
        <validCheckSum>8:6fe3836d6da013a65c608dd881c33af6</validCheckSum>
        <!-- Removed preCondition for migration of old sites where we may already have added this change manually -->
        <insert tableName="group_authorities">
            <column name="authority" value="ROLE_USER"/>
            <column name="group_id" valueNumeric="4"/>
        </insert>
    </changeSet>


    <!-- ROLE_OVERVIEWREFERRAL - for more fine grained control over seeing just the overview info on a referral -->
    <changeSet author="adamjhamer" id="DEV-291-overseerAccess"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="21"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="overseer"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="21"/>
            <column name="authority" value="ROLE_OVERVIEWREFERRAL"/>
        </insert>
    </changeSet>

    <!-- ROLE_OVERVIEWREFERRAL_HIST - for more fine grained control over seeing just the support history on a referral -->
    <changeSet author="adamjhamer" id="DEV-463-overseerAccess-history"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="22"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="overseer-history"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="22"/>
            <column name="authority" value="ROLE_OVERVIEWREFERRAL_HIST"/>
        </insert>
    </changeSet>
    <!-- ROLE_CALENDARADMIN - add to managers also -->
    <changeSet author="adamjhamer" id="DEV-464-calendaradmin-for-managers"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <validCheckSum>8:de84baf66303b4690edb8d1cff60f250</validCheckSum>
        <!-- NB manager is management at this point - see ECCO-270-2-v1 and 1366042568880-23 -->
        <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_CALENDARADMIN"/>
        </insert>
    </changeSet>


    <!-- ROLE_DELETEEVIDENCE - add to managers -->
    <changeSet author="adamjhamer" id="DEV-680-deleteevidence-for-managers"
            logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <validCheckSum>8:41b4dd0aebf7ea6be8859eedec8fca1a</validCheckSum>
        <!-- NB manager is management at this point - see ECCO-270-2-v1 and 1366042568880-23 -->
        <!-- Removed preCondition fQor migration of old sites where we may already have added this groupId manually -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_DELETEEVIDENCE"/>
        </insert>
    </changeSet>

    <!-- ROLE_DELETEEVIDENCE - for more fine grained control over seeing the deletion requests, and acting on them -->
    <changeSet author="adamjhamer" id="DEV-708-deleteevidence"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="23"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="delete evidence"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="23"/>
            <column name="authority" value="ROLE_DELETEEVIDENCE"/>
        </insert>
    </changeSet>

    <!-- ROLE_EDITEVIDENCE - add to staff as currently -->
    <changeSet author="adamjhamer" id="DEV-714-editevidence-for-staff"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <validCheckSum>8:76a35220ea52f1d13f1a6613631f1eba</validCheckSum>
        <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="2"/> <!-- staff -->
            <column name="authority" value="ROLE_EDITEVIDENCE"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-714-admin-evidence-group"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <validCheckSum>7:03631a971f42ab6a156d98a4080776e4</validCheckSum>
        <update tableName="groups">
            <column name="group_name" value="admin evidence"/>
            <where>
                id=23
            </where>
        </update>
    </changeSet>


    <changeSet author="adamjhamer" id="DEV-714-adminevidence-rename2"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <update tableName="group_authorities">
            <column name="authority" value="ROLE_ADMINEVIDENCE"/>
            <where>
                authority='ROLE_DELETEEVIDENCE'
            </where>
        </update>
    </changeSet>

    <!-- ROLE_EDITEVIDENCE - add to 'admin evidence' -->
    <changeSet author="adamjhamer" id="DEV-714-editevidence-for-adminevidence"
               logicalFilePath="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml">
        <validCheckSum>8:17f66187a712edce7e418093ed181e33</validCheckSum>
        <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="23"/> <!-- admin evidence -->
            <column name="authority" value="ROLE_EDITEVIDENCE"/>
        </insert>
    </changeSet>

    <!-- KEEP ADDING TO END OF FILE ABOVE HERE FOR SECURITY DOMAIN -->
    <!-- ONLY ADD NON-DEPENDENT CHANGESETS -->
    <!-- that is - don't include updates to stuff outside this domain -->
    <!-- as that will force us to add 'MARK_RAN' when preconditions fail on tests -->
    <!-- and will mask problems when preconditions fail on CREATE due to the running order -->
    <!-- about the only scenario which works is existing db's - which happens to be the one we develop to -->
    <!-- other scenarios silently fail -->

</databaseChangeLog>
