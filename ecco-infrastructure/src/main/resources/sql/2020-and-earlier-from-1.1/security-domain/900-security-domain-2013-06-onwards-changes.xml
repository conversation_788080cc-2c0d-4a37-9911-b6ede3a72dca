<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/0002-security-domain/900-security-domain-2013-06-onwards-changes.xml">

    <changeSet id="ECCO-762-userdevices-table-amend2" author="bodeng">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/0002-security-domain/900-security-domain-2013-06-onwards-changes.xml" author="bodeng" id="ECCO-762-userdevices-table"/>
        </preConditions>
        <modifyDataType tableName="userdevices" columnName="randomkey" newDataType="BLOB"/>
        <addNotNullConstraint tableName="userdevices" columnName="randomkey" columnDataType="BLOB"/>
    </changeSet>
    <changeSet id="ECCO-762-userdevices-audit-table-amend2" author="bodeng">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/0002-security-domain/900-security-domain-2013-06-onwards-changes.xml" author="bodeng" id="ECCO-762-userdevices-audit-table"/>
        </preConditions>
        <modifyDataType tableName="userdevices_AUD" columnName="randomkey" newDataType="BLOB"/>
        <addNotNullConstraint tableName="userdevices_AUD" columnName="randomkey" columnDataType="BLOB"/>
    </changeSet>

    <changeSet id="ECCO-762-userdevices-table" author="bodeng">
        <!-- modified checksum to indicate this is valid against the original changeSet -->
        <validCheckSum>3:e7e22b9cdd90cefd0298b8482cdeef5d</validCheckSum>
        <createTable tableName="userdevices">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="users_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="guid" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="randomkey" type="BLOB">
                <constraints nullable="false"/>
            </column>
            <column name="cipher" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="valid" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(50)"/>
            <column name="useragent" type="VARCHAR(500)"/>
            <column name="ipaddress" type="VARCHAR(39)"/>
        </createTable>
        <createIndex tableName="userdevices" indexName="idx_userdevices_guid" unique="true">
            <column name="guid"></column>
        </createIndex>
        <addForeignKeyConstraint baseTableName="userdevices" baseColumnNames="users_id"
                                 constraintName="fk_userdevices_users_id"
                                 referencedTableName="users"
                                 referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="ECCO-762-userdevices-audit-table" author="bodeng">
        <validCheckSum>3:6a416ab6353cc0ea230f5d034155b61a</validCheckSum>
        <createTable tableName="userdevices_AUD">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="rev" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="TINYINT"/>
            <column name="users_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="guid" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="randomkey" type="BLOB">
                <constraints nullable="false"/>
            </column>
            <column name="cipher" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="valid" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(50)"/>
            <column name="useragent" type="VARCHAR(500)"/>
            <column name="ipaddress" type="VARCHAR(39)"/>
        </createTable>
        <addPrimaryKey tableName="userdevices_AUD" columnNames="id, rev"/>
        <addForeignKeyConstraint baseTableName="userdevices_AUD" baseColumnNames="rev"
                                 constraintName="fk_userdevices_aud_rev_id"
                                 referencedTableName="revision"
                                 referencedColumnNames="id"/>
    </changeSet>
    <changeSet id="ECCO-763-add-autodismiss-property" author="bodeng">
        <addColumn tableName="userdevices">
            <column name="autodismiss" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="userdevices_AUD">
            <column name="autodismiss" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-763-command-queue-domain" author="bodeng">
        <createTable tableName="commandqueue">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="userdevices_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="valid" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="dismissed" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="queuetime" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="executed" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="result" type="SMALLINT"/>
            <column name="executetime" type="DATETIME"/>
            <column name="method" type="VARCHAR(8)">
                <constraints nullable="false"/>
            </column>
            <column name="url" type="VARCHAR(2000)">
                <constraints nullable="false"/>
            </column>
            <column name="contenttype" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="accepttype" type="VARCHAR(255)"/>
            <column name="body" type="CLOB"/>
            <column name="encryptedcommand" type="LONGBLOB"/>
        </createTable>
    </changeSet>
    <changeSet id="ECCO-763-add-archived-property" author="bodeng">
        <addColumn tableName="commandqueue">
            <column name="archived" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-951-add-role-offline">
        <insert tableName="groups">
            <column name="id" valueNumeric="16"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="offline"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="16"/>
            <column name="authority" value="ROLE_OFFLINE"/>
        </insert>
    </changeSet>

    <!-- create 'reports' group so we can easily allow access to reports and nothing else -->
    <!-- this is a completely new security group, so its OK to add it all here and we may well want it in tests -->
    <changeSet author="adamjhamer" id="ECCO-1314-reportsRole" logicalFilePath="classpath:sql/1.1-changes/071-Feb-2014-until-next.xml">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="17"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="reports"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="17"/>
            <column name="authority" value="ROLE_REPORTS"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="17"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-1318-users-syncGroupId">
        <addColumn tableName="users">
            <column name="syncGroupId" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="nealeu" id="ECCO-1318-syncGroupId-fk"  >
        <addForeignKeyConstraint baseTableName="users" baseColumnNames="syncGroupId"
            referencedTableName="groups" referencedColumnNames="id"
            constraintName="FK_USERS_SYNCGROUPID" deferrable="false"
            initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet author="nealeu" id="ECCO-1318-users_AUD-syncGroupId">
        <addColumn tableName="users_AUD">
            <column name="syncGroupId" type="BIGINT"/>
        </addColumn>
    </changeSet>

    <!-- create 'adminrota' group so we can easily allow access to rota -->
    <!-- this is a completely new security group, so its OK to add it all here and we may well want it in tests -->
    <!-- currently this group can't operate independently (the rota still expects ROLE_STAFF for api calls) -->
    <changeSet author="adamjhamer" id="z330-rotaAdmin">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="18"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="rota"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="18"/>
            <column name="authority" value="ROLE_ADMINROTA"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="18"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
    </changeSet>

    <!-- create 'site sysadmin' group for non-staff sysadmin access -->
    <!-- this is a completely new security group, so its OK to add it all here and we may well want it in tests -->
    <changeSet author="adamjhamer" id="ECCO_1450-create-customer-site-sysadmin">
        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="19"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="site sysadmin"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="19"/>
            <column name="authority" value="ROLE_ADMIN"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="19"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
    </changeSet>

    <!-- DON'T ADD ANY MORE - USE 901-security.... -->

</databaseChangeLog>
