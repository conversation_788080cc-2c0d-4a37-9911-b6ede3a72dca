package com.ecco.workflow.activiti;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;

import org.activiti.engine.form.FormProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Provide ability to create implementation {@link URI}
 */
abstract class ImplementationURI {

    static protected Logger log = LoggerFactory.getLogger(ImplementationURI.class);

    public static URI from(String taskFormKey, List<FormProperty> formProperties) {
        if (taskFormKey != null) {
            if (taskFormKey.contains("?")) {
                log.warn("Legacy data?? taskFormKey contains invalid character [?]: " + taskFormKey);
            }
            try {
                char prefix = taskFormKey.indexOf('?') > 0 ? '&' : '?';
                final StringBuilder uriBuilder = new StringBuilder(taskFormKey);
                if (formProperties != null) {
                    for (FormProperty formProperty : formProperties) {
                        if (!formProperty.isWritable()) {
                            uriBuilder.append(prefix).append(URLEncoder.encode(formProperty.getId(), "UTF-8"))
                                    .append('=').append(URLEncoder.encode(formProperty.getValue(), "UTF-8"));
                            prefix = '&';
                        }
                    }
                }
                return new URI(uriBuilder.toString());
            } catch (URISyntaxException e) {
                throw new RuntimeException("Task form key is not valid URI : " + taskFormKey);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException("UTF-8 not understood. Something's badly wrong.");
            }
        }
        return null;
    }

}
