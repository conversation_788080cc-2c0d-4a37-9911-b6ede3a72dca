package com.ecco.workflow.activiti;

import com.ecco.workflow.WorkflowTarget;
import com.ecco.workflow.WorkflowTaskSummary;
import org.activiti.engine.task.Task;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class ActivitiWorkflowTaskSummary implements WorkflowTaskSummary {
    private final ActivitiWorkflowTarget target;

    private transient Set<String> processIds = new HashSet<>();

    private int allProcesses;
    private int allTasks;
    private int overdueTasks;
    private int dueTasks;
    private int adhocTasks;

    ActivitiWorkflowTaskSummary(ActivitiWorkflowTarget target) {
        this.target = target;
    }

    @Override
    public WorkflowTarget getTarget() {
        return target;
    }

    @Override
    public int getAllProcesses() {
        return allProcesses;
    }

    @Override
    public int getAllTasks() {
        return allTasks;
    }

    @Override
    public int getOverdueTasks() {
        return overdueTasks;
    }

    @Override
    public int getDueTasks() {
        return dueTasks;
    }

    @Override
    public int getAdhocTasks() {
        return adhocTasks;
    }

    void addTask(Task task) {
        allTasks++;
        if (task.getDueDate() != null && task.getDueDate().compareTo(new DateTime().withMillisOfDay(DateTimeConstants.MILLIS_PER_DAY - 1).toDate()) < 0) {
            dueTasks++;
            if (task.getDueDate().compareTo(new Date()) < 0) {
                overdueTasks++;
            }
        }
        if (task.getProcessInstanceId() != null) {
            processIds.add(task.getProcessInstanceId());
        } else {
            adhocTasks++;
        }
    }

    void calculateProcessCount() {
        this.allProcesses = processIds.size();
        processIds = null;
    }
}
