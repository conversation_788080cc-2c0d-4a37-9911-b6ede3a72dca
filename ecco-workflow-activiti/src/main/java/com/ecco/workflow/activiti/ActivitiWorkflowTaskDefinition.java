package com.ecco.workflow.activiti;

import java.util.HashMap;
import java.util.Map;

import org.activiti.engine.form.FormProperty;
import org.activiti.engine.form.TaskFormData;
import org.apache.commons.lang3.StringUtils;

import com.ecco.workflow.WorkflowTaskDefinition;

public class ActivitiWorkflowTaskDefinition implements WorkflowTaskDefinition {

    private final TaskDefinitionHandle handle;
    private final String taskType;
    private final Map<String, String> formSettings = new HashMap<>();


    public ActivitiWorkflowTaskDefinition(TaskDefinitionHandle handle, TaskFormData formData) {

        this.handle = handle;

        String trimmed = StringUtils.substringAfter(formData.getFormKey(), ":");
        this.taskType = trimmed.startsWith("/") ? trimmed.substring(1) : trimmed;

        for (FormProperty prop : formData.getFormProperties()) {
            formSettings.put(prop.getId(), prop.getValue());
        }
    }

    @Override
    public String getHandle() {
        return handle.toExternal();
    }

    @Override
    public String getTaskType() {
        return taskType;
    }

    @Override
    public Map<String, String> getFormSettings() {
        return formSettings;
    }
}
