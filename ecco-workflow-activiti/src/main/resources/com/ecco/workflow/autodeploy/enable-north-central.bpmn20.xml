<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="enable-north" isExecutable="true">
        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>

        <!-- ** IMPORTANT: Update this expression with the ordered list when you add tasks to the workflow ** -->
        <serviceTask id="setTasksToShowClientView" name="set tasksToShowClientView"
            activiti:resultVariable="tasksToShowClientView"
            activiti:expression="emergencyDetails,needsReduction"/>

        <serviceTask id="setTasksToShowRestricted" name="set tasksToShowRestricted"
            activiti:resultVariable="tasksToShowRestricted"
            activiti:expression=
           "from,referralDetails,referralComments,referralAccepted,housingQ,dataProtection,pendingStatus,assessmentDate,needsAssessment,allocationQ,pre-tenancy visit,transition visits,assessmentAccepted,tenancy agreement,settling in,annual housing visit,moving out,close"/>

        <userTask id="emergencyDetails" name="emergencyDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/emergencyDetails"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin"
                activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="referralComments" name="referralComments" activiti:candidateGroups="admin"
                activiti:formKey="generic:/engagementComments" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="referral comments" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- sourcePageGroup is an integer id that evidence is saved against, such that the history of this work
                     can be shown, thus allowing multiple work streams (e.g. multiple needs assessments/threat
                     assessment etc to have their own evidence trail -->
                <activiti:formProperty id="sourcePageGroup" expression="97" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="97" writable="false"/>
                <activiti:formProperty id="outcomes" expression="missing info" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="referralAccepted" name="referralAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralAccepted">
            <!--
            <extensionElements>
                <activiti:formProperty id="show" expression="supportWorker" writable="false"/>
            </extensionElements>
            -->
        </userTask>

        <userTask id="housingQ" name="housingQ" activiti:candidateGroups="admin"
                activiti:formKey="generic:/iaptAttendance" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="housing options" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="69" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="69" writable="false"/>
                <activiti:formProperty id="questions" expression="housing options" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="needsQ" name="needsQ" activiti:candidateGroups="admin"
            activiti:formKey="generic:/iaptInitialAssessment">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="needs questionnaire" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="68" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="68" writable="false"/>
                <activiti:formProperty id="questions" expression="support needs" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="dataProtection" name="dataProtection" activiti:candidateGroups="admin" activiti:formKey="flow:/dataProtection">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="pendingStatus" name="pendingStatus" activiti:candidateGroups="admin"
                activiti:formKey="flow:/pendingStatus"/>

        <userTask id="allocationQ" name="allocationQ" activiti:candidateGroups="admin"
            activiti:formKey="generic:/iaptCurrentView">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="allocation summary" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="70" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="70" writable="false"/>
                <activiti:formProperty id="questions" expression="allocation summary" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="preTenancyVisit" name="pre-tenancy visit" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessmentReduction" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="pre-tenancy visits" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <!--<activiti:formProperty id="showActionGroups" expression="y" writable="false"/>-->
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent,attachments" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview,attachments" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="155" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="155" writable="false"/>
                <activiti:formProperty id="outcomes" expression="pre-tenancy visit" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="allowActionStraightToAchieve" expression="y" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="admin"
                activiti:formKey="flow:/assessmentDate"/>

        <userTask id="needsAssessment" name="needsAssessment" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessment" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessment" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment" writable="false"/>
                <!--<activiti:formProperty id="showActionGroups" expression="y" writable="false"/>-->
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="27" writable="false"/>
                <activiti:formProperty id="outcomes" expression="health safety,daily routines,social / econ.,living situation,CQC" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="transitionVisits" name="transition visits" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessmentReduction" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="transition visits and planning meetings" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <!--<activiti:formProperty id="showActionGroups" expression="y" writable="false"/>-->
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="83" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="83" writable="false"/>
                <activiti:formProperty id="outcomes" expression="bedroom,other" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/decideFinal"/>

        <userTask id="tenancyAgreement" name="tenancy agreement" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessmentReduction" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="tenancy agreement" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <!--<activiti:formProperty id="showActionGroups" expression="y" writable="false"/>-->
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent,attachments" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview,attachments" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="156" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="156" writable="false"/>
                <activiti:formProperty id="outcomes" expression="tenancy agreement" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="allowActionStraightToAchieve" expression="y" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="settlingInQ" name="settling in" activiti:candidateGroups="admin"
            activiti:formKey="generic:/iaptSessions">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="settling in" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="71" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="71" writable="false"/>
                <activiti:formProperty id="questions" expression="settling in" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="housingVisitQ" name="annual housing visit" activiti:candidateGroups="admin"
            activiti:formKey="generic:/iaptGoals">
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="annual housing visit" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="n" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <!-- choose arbitrarily high number as this doesn't exist in current referral aspects -->
                <activiti:formProperty id="sourcePageGroup" expression="87" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="87" writable="false"/>
                <activiti:formProperty id="questions" expression="general qn, income, diversity, tenancy management" writable="false"/>
             </extensionElements>
        </userTask>

        <userTask id="movingOut" name="moving out" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessmentReduction" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="moving out" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <!--<activiti:formProperty id="showActionGroups" expression="y" writable="false"/>-->
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="157" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="157" writable="false"/>
                <activiti:formProperty id="outcomes" expression="moving out" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="admin" activiti:formKey="flow:/close"/>

        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="to_setTasksToShowRestricted" sourceRef="theStart"                 targetRef="setTasksToShowRestricted"/>
        <sequenceFlow id="to_loadReferral"             sourceRef="setTasksToShowRestricted" targetRef="loadReferral"/>
        <sequenceFlow id="to_fork1"                     sourceRef="loadReferral"             targetRef="fork1"/>

        <parallelGateway id="fork1"/>

            <sequenceFlow id="to_referralSource"   sourceRef="fork1" targetRef="referralSource"/>
            <sequenceFlow id="from_referralSource" sourceRef="referralSource" targetRef="join1"/>

            <sequenceFlow id="to_referralDetails"   sourceRef="fork1" targetRef="referralDetails"/>
            <sequenceFlow id="from_referralDetails" sourceRef="referralDetails" targetRef="join1"/>

            <sequenceFlow id="to_referralComments"   sourceRef="fork1"     targetRef="referralComments"/>
            <sequenceFlow id="from_referralComments" sourceRef="referralComments" targetRef="join1"/>

            <sequenceFlow id="to_referralAccepted"   sourceRef="fork1" targetRef="referralAccepted"/>
            <sequenceFlow id="from_referralAccepted" sourceRef="referralAccepted" targetRef="fork2"/>

            <parallelGateway id="fork2"/>

                <sequenceFlow id="to_housingQ" sourceRef="fork2" targetRef="housingQ"/>
                <sequenceFlow id="from_housingQ" sourceRef="housingQ" targetRef="join2"/>

                <sequenceFlow id="to_needsQ" sourceRef="fork2" targetRef="needsQ"/>
                <sequenceFlow id="from_needsQ" sourceRef="needsQ" targetRef="join2"/>

                <sequenceFlow id="to_dataProtection" sourceRef="fork2" targetRef="dataProtection"/>
                <sequenceFlow id="from_dataProtection" sourceRef="dataProtection" targetRef="join2"/>

                <sequenceFlow id="to_pendingStatus"   sourceRef="fork2" targetRef="pendingStatus"/>
                <sequenceFlow id="from_pendingStatus" sourceRef="pendingStatus" targetRef="join2"/>

                <sequenceFlow id="to_allocationQ"   sourceRef="fork2" targetRef="allocationQ"/>
                <sequenceFlow id="from_allocationQ" sourceRef="allocationQ" targetRef="join2"/>

                <sequenceFlow id="to_assessmentDate"       sourceRef="fork2" targetRef="assessmentDate"/>
                <sequenceFlow id="from_assessmentDate"     sourceRef="assessmentDate" targetRef="join2"/>

                <sequenceFlow id="to_needsAssessment"    sourceRef="fork2" targetRef="needsAssessment"/>
                <sequenceFlow id="from_needsAssessment"  sourceRef="needsAssessment" targetRef="join2"/>

                <sequenceFlow id="to_preTenancyVisit"       sourceRef="fork2" targetRef="preTenancyVisit"/>
                <sequenceFlow id="from_preTenancyVisit"     sourceRef="preTenancyVisit" targetRef="join2"/>

                <sequenceFlow id="to_transitionVisits"       sourceRef="fork2" targetRef="transitionVisits"/>
                <sequenceFlow id="from_transitionVisits"     sourceRef="transitionVisits" targetRef="join2"/>

                <sequenceFlow id="to_decideFinal"       sourceRef="fork2" targetRef="decideFinal"/>
                <sequenceFlow id="from_decideFinal"     sourceRef="decideFinal" targetRef="fork3"/>

                <parallelGateway id="fork3"/>

                    <sequenceFlow id="to_tenancyAgreement"    sourceRef="fork3" targetRef="tenancyAgreement"/>
                    <sequenceFlow id="from_tenancyAgreement"  sourceRef="tenancyAgreement" targetRef="join3"/>

                    <sequenceFlow id="to_settlingInQ"    sourceRef="fork3" targetRef="settlingInQ"/>
                    <sequenceFlow id="from_settlingInQ"  sourceRef="settlingInQ" targetRef="join3"/>

                    <sequenceFlow id="to_housingVisitQ"    sourceRef="fork3" targetRef="housingVisitQ"/>
                    <sequenceFlow id="from_housingVisitQ"  sourceRef="housingVisitQ" targetRef="fork4"/>

                    <parallelGateway id="fork4"/>

                        <sequenceFlow id="to_movingOut"     sourceRef="fork4" targetRef="movingOut"/>
                        <sequenceFlow id="from_movingOut"   sourceRef="movingOut" targetRef="join4"/>

                        <sequenceFlow id="to_closeReferral" sourceRef="fork4" targetRef="closeReferral"/>
                        <sequenceFlow id="from_closeReferral" sourceRef="closeReferral" targetRef="join4"/>

                    <parallelGateway id="join4"/>

                <parallelGateway id="join3"/>

            <parallelGateway id="join2"/>

        <parallelGateway id="join1"/>

        <sequenceFlow id="to_theEnd" sourceRef="closeReferral" targetRef="theEnd"/>

        <endEvent id="theEnd" name="End Event"/>
    </process>
</definitions>
