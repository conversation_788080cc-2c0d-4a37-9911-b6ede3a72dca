package com.ecco.config.dom;

import com.ecco.config.service.ExternalSystemService;
import lombok.Getter;
import org.jspecify.annotations.NonNull;

import java.io.Serializable;
import java.net.URI;
import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * Configuration of an external system which provides data to, or consumes data from, ecco.
 * Initial use for this is external CRM systems which provide client data and receive lifecycle events from referrals.
 * e.g. Northgate
 *
 * @since 08/07/2014
 */
@Entity
@Table(name = "cfg_externalsystem")
@Access(AccessType.FIELD)
@Getter
public class ExternalSystem implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @NotNull
    @NonNull
    @Column(nullable=false, length=20)
    private String name;

    @Column
    private String description;

    /**
     * Indication that this is a default for some purpose.
     * Currently, this is used to auto-populate an externalClientSource.
     */
    @Column
    private Boolean defaultEntry;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(length = 31, nullable = false)
    private ExternalSystemService.@NonNull ApiType apiType;

    @NonNull
    @NotNull
    @Column(length = 2000, nullable = false)
    private URI uri;

    /** True if we can search this source for buildings */
    @Column(nullable = false)
    private boolean buildingSource;

    /** True if we should sync new buildings to this external system - there should only be one of these */
    @Column(nullable = false)
    private boolean buildingSink;

    @Column(nullable = false)
    private boolean clientSource;

    /** True if we should sync new clients to this external system - there should only be one of these */
    @Column(nullable = false)
    private boolean clientSink;

    @Column(nullable = false)
    private boolean staffSource;

    /** True if we should sync new staff to this external system - there should only be one of these */
    @Column(nullable = false)
    private boolean staffSink;

    @Column(nullable = false)
    private boolean notifyWorkflow;

    protected ExternalSystem() { // for Hibernate
    }
}
