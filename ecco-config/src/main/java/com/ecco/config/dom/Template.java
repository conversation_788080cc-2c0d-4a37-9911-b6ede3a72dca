package com.ecco.config.dom;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.ecco.dom.upload.SimpleUploadedFile;
import lombok.Getter;
import lombok.Setter;

/**
 * A template for a report or single page
 */
@Entity
@Table(name = "templates")
@Access(AccessType.FIELD)
@Getter
@Setter
public class Template {

    /** What the template expects to have as its root */
    public enum RootEntity {
        REFERRAL, REFERRAL_LIST, CLIENT, CLIENT_LIST, WORKER, WORKER_LIST, WORKER_ASSIGNED_EVENT, REFERRAL_CREATED_EVENT,
        APPOINTMENT_NOTIFICATION_EVENT, TASK_EVENT, INCIDENT_CREATED_EVENT, REPAIR_CREATED_EVENT,
        REFERRAL_DECISION_EVENT
    }

    /** What format the template is in */
    public enum TemplateSourceType {
        MARKDOWN, HTML
    }

    @Id
    @Column(nullable=false, length=127)
    private String name;

    @Column
    private Boolean enabled;

    @Enumerated(EnumType.STRING)
    private TemplateSourceType sourceType;

    @Enumerated(EnumType.STRING)
    private RootEntity rootEntity;

    @OneToOne(fetch=FetchType.LAZY, cascade=CascadeType.ALL)
    @JoinColumn(name="fileId")
    private SimpleUploadedFile file;

    @Basic(fetch = FetchType.EAGER)
    @Lob
    private String body;


    protected Template() {
        // For persistence frameworks
    }

    public Template(String name, TemplateSourceType sourceType, RootEntity rootEntity) {
        this.enabled = true;
        this.name = name;
        this.sourceType = sourceType;
        this.rootEntity = rootEntity;
    }
}
