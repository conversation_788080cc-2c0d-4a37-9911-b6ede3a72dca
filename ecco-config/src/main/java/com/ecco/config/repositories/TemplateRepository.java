package com.ecco.config.repositories;

import org.jspecify.annotations.NonNull;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.ecco.config.dom.Template;

import java.util.Optional;

public interface TemplateRepository extends CrudRepository<Template, String> {

    @NonNull
    @Query("from Template t where t.name = ?1")
    Optional<Template> findById(@NonNull String templateName);
}
