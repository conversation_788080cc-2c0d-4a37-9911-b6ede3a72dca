package com.ecco.config.repositories;

import com.ecco.config.dom.SoftwareFeature;
import com.ecco.config.service.FeatureEnablementVoter.Vote;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import org.jspecify.annotations.NonNull;
import java.util.Optional;

public interface SoftwareFeatureRepository extends CrudRepository<SoftwareFeature, String> {

    /** Update just the defaultVote on the given feature */
    @Modifying // doesn't have version field otherwise would need version=version + 1 too
    @Query("update SoftwareFeature sf set sf.defaultVote = ?2 where sf.name = ?1")
    void setDefaultVote(String featureName, Vote vote);

    @NonNull
    @Query("from SoftwareFeature sf where sf.name = ?1")
    Optional<SoftwareFeature> findById(@NonNull String featureName);
}
