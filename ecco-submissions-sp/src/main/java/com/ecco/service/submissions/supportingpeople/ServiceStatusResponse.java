
package com.ecco.service.submissions.supportingpeople;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="serviceStatusResult" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = "serviceStatusResult")
@XmlRootElement(name = "serviceStatusResponse")
public class ServiceStatusResponse {

    protected String serviceStatusResult;

    /**
     * Gets the value of the serviceStatusResult property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getServiceStatusResult() {
        return serviceStatusResult;
    }

    /**
     * Sets the value of the serviceStatusResult property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setServiceStatusResult(String value) {
        this.serviceStatusResult = value;
    }

}
