package com.ecco.oxm;

import java.io.IOException;
import java.io.OutputStream;

import org.jspecify.annotations.NonNull;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.transform.Result;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.oxm.MarshallingFailureException;
import org.springframework.oxm.XmlMappingException;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;


// see knowledge.txt
@SuppressWarnings("deprecation")
public class CustomJaxb2Marshaller extends Jaxb2Marshaller {

    private String[] cdataElements;

    public String[] getCdataElements() {
        return cdataElements;
    }

    public void setCdataElements(String[] cdataElements) {
        this.cdataElements = cdataElements;
    }

    @Override
    public void marshal(@NonNull Object graph, @NonNull Result result) throws XmlMappingException {

        // no solution - just a way to keep the class and play with other solutions
        /*
        Marshaller marshaller = createMarshaller();
        try {
            marshaller.marshal(graph, (StreamResult) result);
        } catch (JAXBException e) {
            throw convertJaxbException(e);
        }
        */

        /*
        // SOLUTION 2
        // the best from http://stackoverflow.com/questions/3136375/how-to-generate-cdata-block-using-jaxb
        // is from the entry after "solution review" provides us what we need - confirmation we need a straight xsl transformation
        // but again, this seems to be specific 'characters' from the source of CDATA_SECTION_ELEMENTS
        // useful breakpoints com.sun.org.apache.xalan.internal.xsltc.trax.TransformerImpl and com.sun.org.apache.xalan.internal.xsltc.trax.DOM2TO .parse
        // and the SerializerBase below
        Marshaller marshaller = createMarshaller();
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc;
        Transformer nullTransformer;
        try {
            doc = dbf.newDocumentBuilder().newDocument();
            marshaller.marshal(graph, doc);
            nullTransformer = TransformerFactory.newInstance().newTransformer();
            nullTransformer.setOutputProperty(OutputKeys.INDENT, "yes");
            // test this array at com.sun.org.apache.xml.internal.serializer.SerializerBase isCdataSection hover m_cdataSectionElements
            // XMLData does not work, but then ns2:Forename doesn't either?
            // incidentally I believe we can set tranformer properties in the resouce file through spring
            nullTransformer.setOutputProperty(OutputKeys.CDATA_SECTION_ELEMENTS, "APIKEY XMLData ns2:Forename"); // myElement {myNamespace}myOtherElement
            nullTransformer.transform(new DOMSource(doc), ((StreamResult) result));
        } catch (ParserConfigurationException e) {
             throw new RuntimeException(e);
        } catch (JAXBException e) {
             throw convertJaxbException(e);
        } catch (TransformerConfigurationException e) {
             throw new RuntimeException(e);
        } catch (TransformerFactoryConfigurationError e) {
             throw new RuntimeException(e);
        } catch (TransformerException e) {
             throw new RuntimeException(e);
        }
        */

        // SOLUTION 1
        // common solution without introducing moxy - but CDATA only on value
        // see http://davidwinterfeldt.blogspot.co.uk/2012/01/spring-jaxb-with-cdata-elements.html
        if (!ArrayUtils.isEmpty(cdataElements)) {
            try {
                Marshaller marshaller = createMarshaller();
                // NOTE: This comes from activeSoap:xercesImpl so should not resolve the one internal to java.xml module of the JDK
                // IDEA proposes a fix which is safe to accept for its non Maven build
                com.sun.org.apache.xml.internal.serialize.XMLSerializer serializer = createXMLSerializer(cdataElements, ((StreamResult)result).getOutputStream());
                marshaller.marshal(graph, serializer.asContentHandler());
            } catch (IOException e) {
                throw new MarshallingFailureException(e.getMessage(), e);
            } catch (JAXBException ex) {
                throw convertJaxbException(ex);
            }
        } else {
            super.marshal(graph, result);
        }

    }

    @SuppressWarnings("restriction")
    private com.sun.org.apache.xml.internal.serialize.XMLSerializer createXMLSerializer(String[] cDataElements, OutputStream cOut) {
        // This code is from a sample online: http://jaxb.java.net/faq/JaxbCDATASample.java
        // configure an OutputFormat to handle CDATA
        com.sun.org.apache.xml.internal.serialize.OutputFormat of = new com.sun.org.apache.xml.internal.serialize.OutputFormat();

        // specify which of your elements you want to be handled as CDATA.
        // The use of the '^' between the namespaceURI and the localname
        // seems to be an implementation detail of the xerces code.
        // When processing xml that doesn't use namespaces, simply omit the
        // namespace prefix as shown in the third CDataElement
        // see http://jaxb.java.net/faq/JaxbCDATASample.java
        of.setCDataElements(cDataElements); //

        // set any other options you'd like
        //of.setPreserveSpace(true);
        //of.setIndenting(true);
        //of.setPreserveSpace(false);

        // create the serializer
        com.sun.org.apache.xml.internal.serialize.XMLSerializer serializer = new com.sun.org.apache.xml.internal.serialize.XMLSerializer(of);
        serializer.setOutputByteStream(cOut);

        return serializer;
    }

}
