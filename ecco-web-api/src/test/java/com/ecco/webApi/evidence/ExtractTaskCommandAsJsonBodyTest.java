package com.ecco.webApi.evidence;

import com.ecco.dom.Individual;
import com.ecco.dom.commands.ServiceRecipientTaskCommand;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExtractTaskCommandAsJsonBodyTest {

    @Mock
    private ServiceRecipientTaskCommand cmd;

    @Mock
    private Individual contact;

    @Mock
    private User user;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private ExtractTaskCommandViewModelJson extractJsonBody;

    @Before
    public void init() {
        when(cmd.getUserId()).thenReturn(13L);
        when(cmd.getTaskName()).thenReturn("acceptOnService");
        when(userRepository.findById(eq(13L))).thenReturn(Optional.of(user));
        when(user.getUsername()).thenReturn("freddie");
        when(user.getContact()).thenReturn(contact);
        when(contact.getDisplayName()).thenReturn("Freddie Mercury");
    }

    @Test
    public void shouldHandleEmptyBody() {
        when(cmd.getBody()).thenReturn("{}");
        CharSequence apply = extractJsonBody.apply(cmd);
        assertThat(apply.toString(), equalTo("{\"userName\":\"freddie\",\"userDisplayName\":\"Freddie Mercury\",\"taskName\":\"acceptOnService\"}"));
    }

    @Test
    public void shouldHandleObjectWithProperties() {
        when(cmd.getBody()).thenReturn("{\"thing\":\"value\"}");
        CharSequence apply = extractJsonBody.apply(cmd);
        assertThat(apply.toString(), equalTo("{\"userName\":\"freddie\",\"userDisplayName\":\"Freddie Mercury\",\"taskName\":\"acceptOnService\",\"thing\":\"value\"}"));
    }

    @Test(expected = IllegalStateException.class)
    public void shouldThrowIfNotValidJson() {
        when(cmd.getBody()).thenReturn("");
        CharSequence apply = extractJsonBody.apply(cmd);
    }
}