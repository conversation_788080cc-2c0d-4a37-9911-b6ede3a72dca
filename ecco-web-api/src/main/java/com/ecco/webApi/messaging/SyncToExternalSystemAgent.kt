package com.ecco.webApi.messaging

import com.ecco.buildings.repositories.AddressRepository
import com.ecco.config.repositories.ListDefinitionRepository
import com.ecco.contacts.dao.AddressHistoryRepository
import com.ecco.dao.ClientRepository
import com.ecco.infrastructure.bus.MessageBus
import com.ecco.service.event.ExternalSystemSyncAgent
import com.ecco.webApi.contacts.address.ServiceRecipientAddressLocationChangeCommandViewModel
import com.ecco.webApi.evidence.CommandCreatedEvent
import com.ecco.webApi.evidence.CommentCommandViewModel
import com.ecco.webApi.taskFlow.ReferralTaskClientDetailCommandViewModel
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEvent
import org.springframework.stereotype.Component
import javax.annotation.PostConstruct

/**
 * A CommandCreatedEvent handler which can determine, from the view model and user preferences, what to communicate.
 * We want the view model because it can include before and after values (eg risk flag on, off)
 */
@Component
class SyncToExternalSystemAgent(
    private val listDefinitionRepository: ListDefinitionRepository,
    private val clientRepository: ClientRepository,
    private val addressHistoryRepository: AddressHistoryRepository,
    private val addressRepository: AddressRepository,
    private val messageBus: MessageBus<ApplicationEvent>,
    private val externalSystemSyncAgent: ExternalSystemSyncAgent,
) {
    private val log = LoggerFactory.getLogger(javaClass)

    @PostConstruct
    fun init() {
        messageBus.subscribe(CommandCreatedEvent::class.java, this::syncEvent)
    }

    // NB also see DefaultClientSyncStrategy
    private fun syncEvent(event: CommandCreatedEvent) {
        // sync an updated client detail change
        if (event.viewModel is ReferralTaskClientDetailCommandViewModel) {
            val vm = event.viewModel
            if (true == vm.externalSource) {
                log.info(
                    "Stopping sync event for serviceRecipientId {} - its 'externalSource' (we don't want to loop)",
                    vm.serviceRecipientId,
                )
            } else {
                // NOTE: This will be null for non-Referral evidence
                val client = clientRepository.findOneByServiceRecipientId(vm.serviceRecipientId!!)
                if (client != null && client.externalClientRef != null) {
                    val cmd =
                        ReferralTaskClientDetailCommandViewModel.toClientDefinitionCommand(
                            client.externalClientRef,
                            vm,
                            listDefinitionRepository,
                        )
                    externalSystemSyncAgent.syncUpdatedClientToExternalSystems(cmd)
                }
            }
        }

        // sync an updated client detail change
        if (event.viewModel is ServiceRecipientAddressLocationChangeCommandViewModel) {
            val vm = event.viewModel
            if (true == vm.externalSource) {
                log.info(
                    "Stopping sync event for serviceRecipientId {} - its 'externalSource' (we don't want to loop)",
                    vm.serviceRecipientId,
                )
            } else {
                // NOTE: This will be null for non-Referral evidence
                val client = clientRepository.findOneByServiceRecipientId(vm.serviceRecipientId!!)
                if (client != null && client.externalClientRef != null) {
                    val cmd =
                        ServiceRecipientAddressLocationChangeCommandViewModel.toClientDefinitionCommand(
                            client.externalClientRef,
                            vm,
                            addressHistoryRepository,
                            addressRepository,
                        )
                    if (cmd != null) {
                        externalSystemSyncAgent.syncUpdatedClientToExternalSystems(cmd)
                    } else {
                        // log.info("Stopping sync client (update) for externalClientRef {} - not a client", cmd.externalClientRef);
                    }
                }
            }
        }

        // sync an updated flag change
        // NB This is also possible via ExternalSystemSyncAgent
        // but GenericTypeComment doesn't have access to the work, and its just as easy to use the command
        if (event.viewModel is CommentCommandViewModel) {
            val vm = event.viewModel
            if (true == vm.externalSource) {
                log.info(
                    "Stopping sync event for serviceRecipientId {} - its 'externalSource' (we don't want to loop)",
                    vm.serviceRecipientId,
                )
            } else {
                // NOTE: This will be null for non-Referral evidence
                val client = clientRepository.findOneByServiceRecipientId(vm.serviceRecipientId!!)
                if (client != null && client.externalClientRef != null) {
                    val cmd =
                        CommentCommandViewModel.toFlagsDefinitionCommand(
                            client.externalClientRef,
                            vm.flagIds,
                            listDefinitionRepository,
                        )
                    if (cmd != null) {
                        externalSystemSyncAgent.syncFlagsToExternalSystems(cmd)
                    }
                }
            }
        }
    }
}