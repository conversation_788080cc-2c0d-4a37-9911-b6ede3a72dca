package com.ecco.webApi.report;

import com.ecco.dom.ReportDefinition;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

@Slf4j
public class ReportDefinitionToViewModel implements Function<ReportDefinition, ReportDefinitionViewModel> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Nullable
    @Override
    public ReportDefinitionViewModel apply(@Nullable ReportDefinition input) {

        final ReportDefinitionViewModel result = new ReportDefinitionViewModel();
        result.uuid = input.getId();
        result.name = input.getName();
        result.showOnDashboardManager = input.isShowOnDashboardManager();
        result.showOnDashboardFile = input.isShowOnDashboardFile();
        result.friendlyName = input.getFriendlyName();
        result.deleted = input.isHidden();
        result.userId = input.getUser().getId();
        try {
            objectMapper.readTree(input.getBody());
        } catch (JsonProcessingException e) {
            log.error("Invalid JSON in report: {}", input.getId());
            return null;
        }
        result.definition = input.getBody(); // FIXME: Validate JSON and throw + omit if there's an error
        result.orderby = input.getOrderby();

        return result;
    }

}
