package com.ecco.webApi.taskFlow;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.SignatureRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.dom.Individual;
import com.ecco.dom.Referral;
import com.ecco.evidence.dom.Signature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.UUID;

@Component
public abstract class ReferralTaskEditAgreementCommandHandler<T extends ReferralTaskEditAgreementCommandViewModel> extends ServiceRecipientTaskCommandHandler<T> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ReferralRepository referralRepository;

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final SignatureRepository signatureRepository;

    @Autowired
    public ReferralTaskEditAgreementCommandHandler(@NonNull ObjectMapper objectMapper,
                                                 @NonNull WorkflowTaskController workflowTaskController,
                                                 @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                 @NonNull ClientRepository clientRepository,
                                                 @NonNull SignatureRepository signatureRepository,
                                                 @NonNull ReferralRepository referralRepository,
                                                 @NonNull Class<T> clazz) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, clazz);

        this.clientRepository = clientRepository;
        this.signatureRepository = signatureRepository;
        this.referralRepository = referralRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               T vm) {
        Referral r = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);

        if (Boolean.TRUE.equals(vm.reset)) {
            this.setAgreementDate(r, null);
            this.setAgreementStatus(r, null);
            this.setAgreementSignature(r, null);

        } else {
            if (vm.agreementDateChange != null) {
                this.setAgreementDate(r, vm.agreementDateChange.to);
            }
            if (vm.agreementStatusChange != null) {
                this.setAgreementStatus(r, vm.agreementStatusChange.to);
            }

            if (vm.signatureSvgXml != null) {
                ClientDetail client = clientRepository.findOneByServiceRecipientId(params.serviceRecipientId);
                Individual clientContact = client.getContact();

                Signature signature = signatureRepository.save(
                        new Signature(clientContact, vm.signatureSvgXml,
                                vm.signedDateChange.to.toDateTime(DateTimeZone.UTC)));
                entityManager.flush(); // because Hibernate thinks we mean to attach sigs before creating the sig!
                this.setAgreementSignature(r, signature.getId());
            }
        }
        return null;
    }

    protected abstract void setAgreementDate(Referral r, @Nullable DateTime utcDate);

    protected abstract void setAgreementStatus(Referral r, @Nullable Boolean status);

    protected abstract void setAgreementSignature(Referral r, @Nullable UUID signatureId);

}
