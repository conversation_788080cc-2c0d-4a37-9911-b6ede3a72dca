package com.ecco.webApi.taskFlow;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Component
public class ReferralTaskClientResidenceCommandHandler
        extends ServiceRecipientTaskCommandHandler<ReferralTaskClientResidenceCommandDto> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ClientRepository clientRepository;


    @Autowired
    public ReferralTaskClientResidenceCommandHandler(ObjectMapper objectMapper,
                                                     @NonNull WorkflowTaskController workflowTaskController,
                                                     @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                     @NonNull ClientRepository clientRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskClientResidenceCommandDto.class);

        this.clientRepository = clientRepository;
    }


    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskClientResidenceCommandDto vm) {

        ClientDetail c = clientRepository.findOneByServiceRecipientId(params.serviceRecipientId);

        if (vm.startDate != null) {
            // ignore for now
        }

        if (vm.endDate != null) {
            // ignore for now
        }

        if (vm.residence != null) {
            Integer to = vm.residence.to;
            c.setResidenceId(to);
        }

        return null;
    }
}
