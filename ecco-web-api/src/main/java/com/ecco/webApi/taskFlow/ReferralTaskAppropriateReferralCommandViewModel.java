package com.ecco.webApi.taskFlow;


public class ReferralTaskAppropriateReferralCommandViewModel extends ReferralTaskBaseAcceptCommandViewModel  {

    static String TASK_ACCEPTREFERRAL = "referralAccepted";


    /** For Jackson etc */
    @Deprecated
    ReferralTaskAppropriateReferralCommandViewModel() {
        super();
    }

    public ReferralTaskAppropriateReferralCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_ACCEPTREFERRAL, taskHandle);
    }

    @Override
    public String toString() {
        return "ReferralTaskAppropriateReferralCommandViewModel [acceptedDateChange=" + acceptedDate
                + ", taskName=" + taskName + ", serviceRecipientId=" + serviceRecipientId
                + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp=" + timestamp + "]";
    }

}
