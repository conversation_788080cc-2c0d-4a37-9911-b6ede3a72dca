package com.ecco.webApi.taskFlow;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.ClientDetailAbstract;
import com.ecco.dom.PreferredContactMethod;
import com.ecco.calendar.dom.MedDate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Component
public class TaskClientDetailAbstractCommandHandler {

    @PersistenceContext
    private EntityManager entityManager;


    public ListDefinitionEntry getListDefinitionEntryRef(Integer to) {
        return to == null ? null
                : entityManager.getReference(ListDefinitionEntry.class, to);
    }

    protected void handleAbstractDetails(ClientDetailAbstract c, TaskClientDetailAbstractCommandViewModel vm) {

        if (vm.codeChange != null) {
            c.setCode(vm.codeChange.to);
        }

        if (vm.completeAtChange != null) {
            c.setCompleteAt(vm.completeAtChange.to);
        }

        if (vm.birthDateChange != null) {
            c.setBirthDate(MedDate.from(vm.birthDateChange.to));
        }

        if (vm.genderChange != null) {
            c.setGender(getListDefinitionEntryRef(vm.genderChange.to));
        }

        if (vm.genderAtBirthChange != null) {
            c.setGenderAtBirth(getListDefinitionEntryRef(vm.genderAtBirthChange.to));
        }

        if (vm.firstNameChange != null) {
            c.getContact().setFirstName(StringUtils.trimWhitespace(vm.firstNameChange.to));
        }

        if (vm.lastNameChange != null) {
            c.getContact().setLastName(StringUtils.trimWhitespace(vm.lastNameChange.to));
        }

        if (vm.knownAsChange != null) {
            c.getContact().setKnownAs(StringUtils.trimWhitespace(vm.knownAsChange.to));
        }

        if (vm.pronounsChange != null) {
            c.getContact().setPronouns(getListDefinitionEntryRef(vm.pronounsChange.to));
        }

        if (vm.firstLanguageChange != null) {
            c.setFirstLanguage(getListDefinitionEntryRef(vm.firstLanguageChange.to));
        }

        if (vm.ethnicOriginChange != null) {
            c.setEthnicOrigin(getListDefinitionEntryRef(vm.ethnicOriginChange.to));
        }

        if (vm.nationalityChange != null) {
            c.setNationality(getListDefinitionEntryRef(vm.nationalityChange.to));
        }

        if (vm.maritalStatusChange != null) {
            c.setMaritalStatus(getListDefinitionEntryRef(vm.maritalStatusChange.to));
        }

        if (vm.religionChange != null) {
            c.setReligion(getListDefinitionEntryRef(vm.religionChange.to));
        }

        if (vm.disabilityChange != null) {
            c.setDisability(getListDefinitionEntryRef(vm.disabilityChange.to));
        }

        if (vm.sexualOrientationChange != null) {
            c.setSexuality(getListDefinitionEntryRef(vm.sexualOrientationChange.to));
        }

        if (vm.niChange != null) {
            c.setNi(vm.niChange.to);
        }

        if (vm.nhsChange != null) {
            c.setNhs(vm.nhsChange.to);
        }

        if (vm.emailChange != null) {
            c.getContact().setEmail(vm.emailChange.to);
        }

        if (vm.phoneNumberChange != null) {
            c.getContact().setPhoneNumber(vm.phoneNumberChange.to);
        }

        if (vm.mobileNumberChange != null) {
            c.getContact().setMobileNumber(vm.mobileNumberChange.to);
        }

        if (vm.preferredContactMethodChange != null) {
            c.getContact().setPreferredContactMethod(PreferredContactMethod.fromString(vm.preferredContactMethodChange.to));
        }
    }
}
