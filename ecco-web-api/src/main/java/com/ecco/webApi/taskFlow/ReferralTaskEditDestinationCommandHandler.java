package com.ecco.webApi.taskFlow;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Project;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.repositories.repairs.RepairRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Component
public class ReferralTaskEditDestinationCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskEditDestinationCommandViewModel> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull private final ReferralRepository referralRepository;
    @NonNull private final ServiceCategorisationRepository serviceCategorisationRepository;
    @NonNull private final ServiceRecipientRepository serviceRecipientRepository;
    @NonNull private final RepairRepository repairRepository;

    @Autowired
    public ReferralTaskEditDestinationCommandHandler(ObjectMapper objectMapper,
                                                     @NonNull WorkflowTaskController workflowTaskController,
                                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                     ServiceRecipientRepository serviceRecipientRepository,
                                                     ReferralRepository referralRepository,
                                                     RepairRepository repairRepository,
                                                     ServiceCategorisationRepository serviceCategorisationRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskEditDestinationCommandViewModel.class);
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.referralRepository = referralRepository;
        this.repairRepository = repairRepository;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskEditDestinationCommandViewModel vm) {

        var sr = this.serviceRecipientRepository.findById(params.serviceRecipientId).get();

        if (vm.projectChange != null) {
            Project project = vm.projectChange.to == null ? null
                    : entityManager.getReference(Project.class, vm.projectChange.to.longValue());
            var svcCat = serviceCategorisationRepository.findOneByService_IdAndProject_Id(sr.getServiceAllocation().getServiceId(), project.getId());
            // NB this assumes there is an existing service allocation - else search for 'setServiceAllocation'
            sr.setServiceAllocation(svcCat);
        }

        return null;
    }
}
