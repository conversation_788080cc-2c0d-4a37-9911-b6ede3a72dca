package com.ecco.webApi.managedvoids;

import com.ecco.dom.managedvoids.ManagedVoidServiceRecipient;
import com.ecco.webApi.controllers.CreateServiceRecipientCommandViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;

public class CreateManagedVoidCommandViewModel extends CreateServiceRecipientCommandViewModel {

    private ManagedVoidViewModel managedVoidViewModel;

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected CreateManagedVoidCommandViewModel() {
        super();
    }

    public CreateManagedVoidCommandViewModel(@Nonnull ManagedVoidViewModel managedVoidViewModel) {
        super(UriComponentsBuilder
                .fromUriString("service-recipient/command/create/"+ ManagedVoidServiceRecipient.PREFIX+"/")
                .toUriString(), ManagedVoidServiceRecipient.PREFIX);
        this.managedVoidViewModel = managedVoidViewModel;
    }

    public ManagedVoidViewModel getManagedVoidViewModel() {
        return managedVoidViewModel;
    }

}
