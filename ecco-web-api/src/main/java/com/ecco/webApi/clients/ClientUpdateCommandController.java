package com.ecco.webApi.clients;

import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.webApi.controllers.AttributeChangeUtility;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.ChoiceUpdateCommand;
import com.ecco.webApi.viewModels.DateUpdateCommand;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.TextUpdateCommand;
import org.joda.time.LocalDate;
import org.springframework.beans.PropertyAccessor;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import org.jspecify.annotations.NonNull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class ClientUpdateCommandController extends BaseWebApiController {

    @PersistenceContext
    protected EntityManager entityManager;

    private final ClientRepository clientRepository;
    private final ClientAttributeChangeCommandHandler clientAttributeChangeCommandHandler;
    private final ExternalSystemRepository externalSystemRepository;


    ClientUpdateCommandController() {
        // Required for CGLIB - we only need services wired on the proxy's target
        this(null, null, null);
    }

    public ClientUpdateCommandController(ClientRepository clientRepository,
                                         ClientAttributeChangeCommandHandler clientAttributeChangeCommandHandler,
                                         ExternalSystemRepository externalSystemRepository) {
        this.clientRepository = clientRepository;
        this.clientAttributeChangeCommandHandler = clientAttributeChangeCommandHandler;
        this.externalSystemRepository = externalSystemRepository;
    }


    @PostJson("/clients/{id}/textUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result processClientTextUpdateCommand(
            @RequestBody TextUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        // allow us to set an externalClientRef without specifying a source directly
        if (path.contains("externalClientRef")) {
            var defaultEntry = externalSystemRepository.findByDefaultEntryTrue().stream().findFirst().orElse(null);
            if (defaultEntry != null) {
                clientRepository.updateExternalSystemRef(id, defaultEntry.getName(), updateCommand.newValue);
            }
            return new Result("updated");
        }

        ClientDetail client = clientRepository.findById(id).orElseThrow();
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(client);

        String currentValue = (String) wrapper.getPropertyValue(path);
        AttributeChangeUtility.checkAndHandleNonMatchingTextValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue);
        clientRepository.save(client);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

    @PostJson("/clients/{id}/choiceUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result processClientChoiceUpdateCommand(
            @RequestBody ChoiceUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        ClientDetail client = clientRepository.findById(id).orElseThrow();
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(client);

        path = path.split(":")[0];
        Integer currentValue = (Integer) wrapper.getPropertyValue(path);
        AttributeChangeUtility.checkAndHandleNonMatchingIntegerValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue);
        clientRepository.save(client);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

    @PostJson("/clients/{id}/dateUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result processClientDateUpdateCommand(
            @RequestBody DateUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        ClientDetail client = clientRepository.findById(id).orElseThrow();
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(client);

        LocalDate currentValue = (LocalDate) wrapper.getPropertyValue(path);
        AttributeChangeUtility.checkAndHandleNonMatchingDateValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue);
        clientRepository.save(client);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

    @PostJson("/clients/{clientId}/commands/")
    public Result updateContact(
            @PathVariable long clientId,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        // TODO: when adding more, select different handlers based on incoming
        // command DTO based on a discriminator within the command
        return clientAttributeChangeCommandHandler.handleCommand(authentication, clientId, requestBody);
    }

}
