package com.ecco.webApi.clients;

import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.CreateClientCommandHandler;
import com.ecco.webApi.viewModels.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import org.jspecify.annotations.NonNull;
import java.io.IOException;

// authorise the controller path, not the handler - we may need to be available to all
//@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class CreateClientCommandController extends BaseWebApiController {

    private final CreateClientCommandHandler createClientCommandHandler;

    @Autowired
    public CreateClientCommandController(CreateClientCommandHandler createClientCommandHandler) {
        this.createClientCommandHandler = createClientCommandHandler;
    }

    @PostJson("/client/command/create/")
    public Result createClient(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return createClientCommandHandler.handleCommand(authentication, null, requestBody);
    }

}
