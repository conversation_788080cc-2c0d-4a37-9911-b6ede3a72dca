package com.ecco.webApi.finance;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;

@NoArgsConstructor(access = AccessLevel.PACKAGE)
@Slf4j
public class FinanceReceiptCommandViewModel extends BaseServiceRecipientCommandViewModel {

    @NonNull
    public String operation;

    @Nullable
    public Integer receiptId;

    @Nullable
    public ChangeViewModel<String> description;

    @Nullable
    public ChangeViewModel<LocalDate> receivedDate;

    @Nullable
    public ChangeViewModel<BigDecimal> amount;

    /**
     * For tests only (since <PERSON> uses default constructor).
     */
    public FinanceReceiptCommandViewModel(@NonNull String operation, int serviceRecipientId) {
        super(UriComponentsBuilder
                .fromUriString("finance/receipts/serviceRecipient/{serviceRecipientId}/command")
                .buildAndExpand(serviceRecipientId)
                .toUriString(),
                serviceRecipientId);
        this.operation = operation;
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        switch (this.operation) {
            case OPERATION_ADD -> {
                if (receiptId != null) {
                    log.error("Supplied field incorrectly: receiptId");
                    return false;
                }
                if (serviceRecipientId == null) {
                    log.error("Required field: serviceRecipientId");
                    return false;
                }
                if (amount == null || amount.to == null) {
                    log.error("Required field: amount");
                    return false;
                }
                if (receivedDate == null || receivedDate.to == null) {
                    log.error("Required field: received");
                    return false;
                }
            }
            case OPERATION_UPDATE, OPERATION_REMOVE -> {
                if (receiptId == null) {
                    log.error("Required field: receiptId");
                    return false;
                }
            }
        }

        return valid;
    }

}
