package com.ecco.webApi.finance;
import com.ecco.dom.finance.FinanceReceipt;
import java.util.function.Function;

public class FinanceReceiptToViewModel implements Function<FinanceReceipt, FinanceReceiptViewModel> {

    @Override
    public FinanceReceiptViewModel apply(FinanceReceipt input) {

        FinanceReceiptViewModel result = new FinanceReceiptViewModel();
        result.receiptId = input.getId();
        result.serviceRecipientId = input.getServiceRecipientId();
        result.description = input.getDescription();
        result.amount = input.getAmount();
        result.receivedDate = input.getReceivedDate();
        return result;
    }

}