package com.ecco.webApi.finance

import com.ecco.infrastructure.annotations.WriteableTransaction
import com.ecco.infrastructure.web.WebSlice
import com.ecco.repositories.finance.HousingBenefitIncomeStatementRepository
import com.ecco.webApi.controllers.NotFoundException
import org.springframework.composed.web.rest.json.GetJson
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import kotlin.jvm.optionals.getOrNull

@RestController
@RequestMapping("/finance/housingBenefit")
@WebSlice("api")
@WriteableTransaction
class HousingBenefitIncomeStatementController(private val statementRepository: HousingBenefitIncomeStatementRepository) {
    @GetJson("/statements/{statementId}/")
    fun getIncomeStatement(@PathVariable statementId: Int): HousingBenefitIncomeStatementViewModel =
        statementRepository.findById(statementId).getOrNull()?.toViewModel()
            ?: throw NotFoundException(statementId)
}