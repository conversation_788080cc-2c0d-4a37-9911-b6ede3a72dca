package com.ecco.webApi.users;

import com.ecco.dto.AddedRemovedDto;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.RequiredForOperations;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Changes to typical 'core' account details.
 */
@Slf4j
@NoArgsConstructor
public class UserCoreCommandDto extends BaseUserCommandDto {


    public ChangeViewModel<Boolean> enabled;

    @RequiredForOperations(value=OPERATION_ADD)
    public ChangeViewModel<String> username;

    @RequiredForOperations(value=OPERATION_ADD)
    public ChangeViewModel<String> firstName;

    @RequiredForOperations(value=OPERATION_ADD)
    public ChangeViewModel<String> lastName;

    @Nullable
    public ChangeViewModel<String> email;

    @Nullable
    public AddedRemovedDto<String> groups;

    @Nullable
    public ChangeViewModel<Boolean> activeDirectoryUser;

    @Nullable
    public ChangeViewModel<Boolean> mfaRequired;

    /**
     * For tests only (since Jackson uses default constructor).
     */
    public UserCoreCommandDto(@NonNull String operation, @Nullable Long userIdSubject) {
        super(operation, UserCoreCommandDto.getUri(userIdSubject), userIdSubject);
        this.operation = operation;
    }

    private static String getUri(Long userIdSubject) {
        return UriComponentsBuilder
                .fromUriString("users/{userIdSubject}/commands/")
                .buildAndExpand(userIdSubject == null ? "-" : userIdSubject)
                .toUriString();
    }

    public boolean valid() {
        return BaseCommandViewModel.valid(getClass(), this);
    }

}
