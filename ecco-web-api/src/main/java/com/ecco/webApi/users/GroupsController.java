package com.ecco.webApi.users;

import com.ecco.infrastructure.rest.hateoas.schema.ResourceSchemaCreator;
import com.ecco.security.dom.GroupAuthority;
import com.ecco.security.repositories.GroupRepository;
import com.ecco.security.service.UserManagementService;
import com.ecco.webApi.annotations.GetJsonSchema;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.factories.JsonSchemaFactory;
import com.fasterxml.jackson.module.jsonSchema.types.IntegerSchema;
import com.fasterxml.jackson.module.jsonSchema.types.ObjectSchema;
import lombok.AllArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static java.util.Collections.singleton;
import static java.util.concurrent.TimeUnit.MINUTES;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@PreAuthorize("hasRole('ROLE_ADMINLOGIN')")
@AllArgsConstructor
@RestController
public class GroupsController extends BaseWebApiController {

    private final GroupRepository groupRepository;
    private final ResourceSchemaCreator resourceSchemaCreator;
    private final JsonSchemaFactory schemaFactory;
    @Resource(name="userManagementService")
    private final UserManagementService userManagement;

    @GetJson("/groups/")
    public GroupDto[] getGroups() {
        var groupNamesAllowed = userManagement.findAllGroups();

        return groupRepository.findAll().stream()
            .filter(grp -> groupNamesAllowed.contains(grp.getName()))
            .map(group -> new GroupDto(
                    group.getName(),
                    group.getAuthorities().stream()
                            .map(GroupAuthority::getAuthority)
                            .toArray(String[]::new)))
            .toArray(GroupDto[]::new);
    }

    @GetJsonSchema("/groups$enumSchema/")
    public ResponseEntity<JsonSchema> enumerateAllGroups() {
        JsonSchema schema = resourceSchemaCreator.createNestedEnum(groupRepository.findAll().stream(), methodOn(getClass()).enumerateAllGroups(),
                vm -> {
                    ObjectSchema objectSchema = schemaFactory.objectSchema();
                    IntegerSchema serviceSchema = schemaFactory.integerSchema();
                    serviceSchema.setEnums(singleton(String.valueOf(vm.getId())));
                    serviceSchema.setTitle(vm.getName());
                    objectSchema.putProperty("group", serviceSchema);
                    return objectSchema;
                }, null, null);
        return ResponseEntity.ok().cacheControl((CacheControl.maxAge(3, MINUTES))).body(schema);
    }
}
