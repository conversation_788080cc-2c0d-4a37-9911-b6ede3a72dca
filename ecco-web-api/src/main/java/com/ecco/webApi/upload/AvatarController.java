package com.ecco.webApi.upload;

import com.ecco.infrastructure.web.WebSlice;
import com.ecco.service.ContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@Controller
@WebSlice("api")
public class AvatarController {

    @Autowired
    private ContactService contactsService;

    @GetMapping("/secure/avatarHiddenJs.html")
    public void avatarJs(@RequestParam("contactId") long contactId, @RequestParam("bytesId") long bytesId,
                         HttpServletResponse response) {
        contactsService.setAvatar(contactId, bytesId);
        response.setStatus(HttpStatus.OK.value());
        //return UploadErrorResult.success("deleted").toJson();
    }

    @GetMapping("/contact/{contactId}/avatar")
    @ResponseBody
    public byte[] getClientAvatar(@PathVariable long contactId) {
        return contactsService.getAvatar(contactId);
    }

    @PostMapping("/contact/{contactId}/avatar")
    @ResponseStatus(HttpStatus.OK)
    public void setClientAvatar(@PathVariable long contactId, @RequestParam("bytesId") long bytesId) {
        contactsService.setAvatar(contactId, bytesId);
    }

    @DeleteMapping("/contact/{contactId}/avatar")
    @ResponseStatus(HttpStatus.OK)
    public void removeContactAvatar(@PathVariable long contactId) {
        contactsService.removeAvatar(contactId);
    }

}
