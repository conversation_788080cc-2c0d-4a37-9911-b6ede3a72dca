package com.ecco.webApi.tasks;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaProperty;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import java.time.LocalDateTime;
import java.util.UUID;

import static lombok.AccessLevel.PROTECTED;

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
public class TaskStatusListRowResource extends RepresentationModel<TaskStatusListRowResource> {
    /** Not a visible column */
    private UUID taskStatusId;

    @JsonSchemaMetadata(title = "due date", order = 15)
    @JsonSchemaProperty(format = JsonValueFormat.DATE_TIME)
    private LocalDateTime dueDate;

    /** Not a visible column */
    private Long assignedUserId;

    @JsonSchemaMetadata(title = "assigned user", order = 20)
    private String assignedUser;


    @JsonSchemaMetadata(title = "description", order = 25)
    private String description;
}
