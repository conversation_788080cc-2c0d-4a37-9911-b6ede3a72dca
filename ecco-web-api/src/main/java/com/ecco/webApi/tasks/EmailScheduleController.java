package com.ecco.webApi.tasks;

import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.acls.CachedAclVisibilityService;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.messaging.EmailService;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.TaskStatusToViewModel;
import com.ecco.webApi.viewModels.TaskStatusViewModel;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/email-schedule")
public class EmailScheduleController extends EmailBaseController {

    private final TaskStatusRepository taskStatusRepository;
    private final UserRepository userRepository;
    private final SoftwareFeatureService featureService;
    private final TaskStatusToViewModel taskStatusToViewModel;

    @Value("${enableEmailSchedule:false}")
    private boolean enableEmailSchedule;

    @Autowired
    public EmailScheduleController(@NonNull TaskStatusRepository taskStatusRepository,
                                   @NonNull UserRepository userRepository,
                                   @NonNull CachedAclVisibilityService aclService,
                                   @NonNull EmailService emailService,
                                   @NonNull ServiceTypeService serviceTypeService,
                                   @NonNull ServiceCategorisationRepository serviceCategorisationRepository,
                                   @NonNull MessageSource messageSource,
                                   @NonNull TaskDefinitionRepository taskDefinitionRepository,
                                   @NonNull ServiceRecipientRepository serviceRecipientRepository,
                                   @NonNull SoftwareFeatureService featureService,
                                   @NonNull ApplicationProperties applicationProperties) {
        super(emailService, serviceTypeService, taskDefinitionRepository, serviceCategorisationRepository, serviceRecipientRepository, userRepository, messageSource, applicationProperties);
        this.taskStatusRepository = taskStatusRepository;
        this.userRepository = userRepository;
        this.featureService = featureService;
        this.taskStatusToViewModel = new TaskStatusToViewModel(serviceRecipientRepository);
    }

    // runtime config changes may be possible in ScheduledTaskRegistrar
    // runs sometime between 4am and 5am on Sunday - if no provided -DemailSchedule=""
    @Scheduled(cron = "0 #{new java.util.Random().nextInt(59)} 4 * * SUN", zone = "UTC")
    public void trigger() {

        if (!featureService.featureEnabled("ecco.email")) {
            // we don't log this elsewhere, it's the first thing that will be checked, and so avoids log entries
            //log.error("EMAIL NOT SENT - not enabled");
            return;
        }

        if (enableEmailSchedule) {
            emailTasks();
        }
    }

    @PostMapping("/tasks/")
    public Result emailTasks() {
        findTasksAndSendEmails(null);
        return new Result("Processed all entries");
    }

    @PostMapping("/tasks/{userId}/")
    public Result emailTasksOfUserId(@PathVariable Long userId) {
        var user = userRepository.findById(userId).orElseThrow();
        findTasksAndSendEmails(user);
        return new Result("Processed all entries for user " + user.getUsername());
    }

    private void findTasksAndSendEmails(@Nullable User user) {
        findTasks(user).stream()
                .collect(Collectors.groupingBy(t -> t.assignee)) // redundant for supplied user
                .values()
                .forEach(tasks -> this.sendEmail(tasks, tasks.get(0).assigneeEmail, this.getSubject(tasks)));
    }

    private List<TaskStatusViewModel> findTasks(@Nullable User user) {
        var now = LocalDate.now(ZoneId.of("UTC")).atStartOfDay();
        var tasks = user == null
            ? taskStatusRepository.findAllByCompletedNullAndAssignedUserIsNotNullAndDueDateAfterAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(now, now.plusDays(7))
            : taskStatusRepository.findAllByAssignedUserAndCompletedNullAndDueDateAfterAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(user, now, now.plusDays(7));
        return tasks.map(taskStatusToViewModel).toList();
    }

    @NonNull
    @Override
    protected String getSubject(List<TaskStatusViewModel> tasks) {
        return "Due tasks: " + tasks.size();
    }

}
