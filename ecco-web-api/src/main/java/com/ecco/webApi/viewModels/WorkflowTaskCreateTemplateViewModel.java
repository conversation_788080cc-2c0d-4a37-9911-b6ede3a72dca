package com.ecco.webApi.viewModels;

import org.springframework.hateoas.RepresentationModel;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.jspecify.annotations.Nullable;

/** Data-transfer object representing a task template. */
public class WorkflowTaskCreateTemplateViewModel extends RepresentationModel<WorkflowTaskCreateTemplateViewModel> {
    /** The name used for display */
    public String taskName;

    @Nullable
    /** Due date for the task, if it has one. */
    public LocalDateTime dueDate;

    /**
     * Entities related to this workflow task.
     * This representation is only required on the task create template, not the task since the task will be managed through
     * its links instead (HATEOAS again).
     */
    public List<EntityReferenceViewModel> entities = new ArrayList<EntityReferenceViewModel>();

}
