package com.ecco.webApi.viewModels;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.Link;


public class Result {

    public static String COMMAND_APPLIED = "command applied";

    /** id provided as the result
     * NB for commands this is the command id NOT the entity id */
    private String id;

    /** Display for the client as to the result of the request */
    private String message;

    /** Optional Link for the entity */
    private Link[] links;

    Result() {
    }

    public Result(long id) {
        super();
        this.message = null;
        this.id = String.valueOf(id);
    }

    public Result(String message) {
        super();
        this.message = message;
        this.id = null;
    }

    public Result(String message, Number id) {
        this(message, String.valueOf(id), null);
    }
    public Result(String message, Number id, Link[] links) {
        this(message, String.valueOf(id), links);
    }
    public Result(String message, String id) {
        this(message, id, null);
    }

    public Result(String message, String id, Link[] links) {
        super();
        this.message = message;
        this.id = id;
        this.links = links;
    }

    public Result(Exception e, String id) {
        this(e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName(), id);
    }

    public Result(Exception e) {
        this(e, null);
    }

    public String getId() {
        return id;
    }

    public String getMessage() {
        return message;
    }

    public Link[] getLinks() {
        return links;
    }

    public Link getLink(@NonNull String rel) {
        if (links == null) {
            return null;
        }
        for (Link link : links) {
            if (rel.equalsIgnoreCase(link.getRel().value())) {
                return link;
            }
        }
        return null;
    }

    @JsonIgnore
    public boolean isCommandSuccessful() {
        return Result.COMMAND_APPLIED.equals(message);
    }

}
