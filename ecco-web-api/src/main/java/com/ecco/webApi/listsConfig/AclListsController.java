package com.ecco.webApi.listsConfig;

import static java.util.stream.Collectors.toList;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.Comparator;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.ecco.infrastructure.web.RequestUtils;
import com.ecco.webApi.contacts.IndividualUserSummaryToViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dom.IndividualUserSummary;
import com.ecco.service.acls.CachedAclVisibilityService;
import com.ecco.webApi.controllers.BaseWebApiController;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class AclListsController extends BaseWebApiController {

    private final CachedAclVisibilityService aclService;
    private final IndividualUserSummaryToViewModel toViewModel;

    @Autowired
    public AclListsController(CachedAclVisibilityService aclVisibilityService, IndividualUserSummaryToViewModel toViewModel) {
        this.aclService = aclVisibilityService;
        this.toViewModel = toViewModel;
    }

    @RequestMapping(value = "/usersWithAccessTo/", method = GET, produces = APPLICATION_JSON_VALUE)
    public Iterable<IndividualUserSummaryViewModel> findUsersWithAccessTo( HttpServletResponse response,
            @RequestParam Long serviceId,
            @RequestParam(required=false) Long projectId,
            @RequestParam(defaultValue="ROLE_STAFF") String role) {

        // this method is cached by aclVisibilityService
        // too much sql for the cache though means the sql is run each time simply to produce a 304 not changed
        // see reportUsers which uses query dsl
        List<IndividualUserSummary> usersWithAccessTo = this.aclService.getUsersWithAccessTo(serviceId, projectId, role);
        // the response (which will be quick if the method is already cached above) is http-cached for 12 hours (per request)
        // so the client just gets from disk cache, and renews the http cache when expired
        // different users will be hitting different expiry times on different url's
        // so it should remain healthy, but we may want to evict specific service/project caches
        // else the whole cache needs to be cleared to add new staff, and usersWithAccessTo could get hit hard multiple times
        RequestUtils.cacheForXHours(12, response);
        return usersWithAccessTo.stream().map(toViewModel)
                .sorted(Comparator.comparing(IndividualUserSummaryViewModel::getLastName, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(toList());
    }

}
