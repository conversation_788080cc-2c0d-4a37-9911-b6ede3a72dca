package com.ecco.webApi.contacts

import com.ecco.config.repositories.ListDefinitionRepository
import com.ecco.dom.ClientDetailAbstract
import com.ecco.webApi.contacts.address.AddressToViewModel
import com.ecco.webApi.contacts.address.AddressedLocationToViewModel
import com.ecco.webApi.evidence.ReferralToViewModel.LocalDateToString
import com.google.common.collect.Maps
import org.joda.time.LocalDate

class PersonToViewModel internal constructor(private val listDefinitionRepository: ListDefinitionRepository) {
    private val addressToViewModel = AddressToViewModel()
    private val addressedLocationToViewModel = AddressedLocationToViewModel()

    private val localDateToString = LocalDateToString()

    fun apply(input: ClientDetailAbstract, output: PersonViewModel) {
        output.calendarId = input.contact.calendarId
        output.title = input.contact.title
        output.firstName = input.contact.firstName
        output.lastName = input.contact.lastName
        output.avatarId = input.contact.avatarId
        if (input.birthDate == null) {
            output.birthDate = null
        } else {
            output.birthDate = input.birthDate.toLocalDate()
        }
        output.dateOfDeath = input.dateOfDeath
        if (input.dateMap != null) {
            val view = Maps.transformValues(input.dateMap) { source: LocalDate? -> localDateToString.apply(source) }
            output.dateMap = HashMap(view)
        }
        output.age = input.age
        output.genderId = if (input.gender == null) null else input.gender.id
        output.genderAtBirthId = if (input.genderAtBirth == null) null else input.genderAtBirth.id
        output.disabilityId = if (input.disability == null) null else input.disability.id
        output.ethnicOriginId = if (input.ethnicOrigin == null) null else input.ethnicOrigin.id
        output.nationalityId = if (input.nationality == null) null else input.nationality.id
        output.maritalStatusId = if (input.maritalStatus == null) null else input.maritalStatus.id
        output.religionId = if (input.religion == null) null else input.religion.id
        output.firstLanguageId = if (input.firstLanguage == null) null else input.firstLanguage.id
        output.sexualOrientationId = if (input.sexuality == null) null else input.sexuality.id
        if (input.contact.addressedLocation != null) {
            output.addressedLocationId = input.contact.addressedLocation.id
        }
        if (input.contact.addressedLocation != null) {
            output.addressedLocationId = input.contact.addressedLocation.id
            output.address = addressedLocationToViewModel.apply(input.contact.addressedLocation)
        } else {
            if (input.contact.address != null) {
                output.address = addressToViewModel.apply(input.contact.address)
            }
        }
        output.phoneNumber = input.contact.phoneNumber
        output.mobileNumber = input.contact.mobileNumber
        output.email = input.contact.email
        if (input.contact.preferredContactMethod != null) {
            output.preferredContactMethod = input.contact.preferredContactMethod.toString()
        }
        output.knownAs = input.contact.knownAs
        output.pronounsId = input.contact.pronouns?.id
    }
}