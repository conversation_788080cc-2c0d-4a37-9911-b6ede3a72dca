package com.ecco.webApi.contacts;

import com.ecco.dom.ClientDetailAbstract;
import com.ecco.webApi.evidence.ReferralToViewModel;
import com.google.common.collect.Maps;
import org.jspecify.annotations.NonNull;

import java.util.HashMap;
import java.util.Map;

public final class ClientDetailAbstractToViewModel {

    private final ReferralToViewModel.LocalDateToString localDateToString = new ReferralToViewModel.LocalDateToString();

    ClientDetailAbstractToViewModel() {
    }

    public void apply(@NonNull ClientDetailAbstract input, @NonNull ClientDetailAbstractViewModel result) {

        result.contactId = input.getContact().getId();

        result.ni = input.getNi();
        result.nhs = input.getNhs();
        result.textMap = input.getTextMap();
        if (input.getDateMap() != null) {
            Map<String, String> view = Maps.transformValues(input.getDateMap(), localDateToString::apply);
            result.dateMap = new HashMap<>(view);
        }

        if (input.getContact().getAddressedLocation() != null) {
            result.addressedLocationId =  input.getContact().getAddressedLocation().getId();
        }

        result.communicationNeeds = input.getCommunicationNeeds();
        result.description = input.getDescriptionDetails();
        result.doctorDetails = input.getDoctorDetails();
        result.dentistDetails = input.getDentistDetails();
        result.emergencyDetails = input.getEmergencyDetails();
        result.emergencyKeyCode = input.getKeyCode();
        result.emergencyKeyWord = input.getEmergencyKeyword();
        result.medicationDetails = input.getMedicationDetails();
        result.risksAndConcerns = input.getRisksAndConcerns();
        result.completeAt = input.getCompleteAt();
        if (input.getContact().getPreferredContactMethod() != null) {
            result.preferredContactInfo = input.getContact().getPreferredContactInfo();
        }
    }
}
