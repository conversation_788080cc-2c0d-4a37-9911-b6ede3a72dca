package com.ecco.webApi.contacts.address;

import java.util.function.Function;

import com.ecco.dom.AddressedLocation;

import org.jspecify.annotations.Nullable;

public final class AddressedLocationFromViewModel implements Function<AddressViewModel, AddressedLocation> {

    @Nullable
    @Override
    public AddressedLocation apply(@Nullable AddressViewModel input) {
        if (input == null) {
            throw new NullPointerException("input AddressViewModel must not be null");
        }

        AddressedLocation address = new AddressedLocation();
        if (input.address != null && input.address.length >= 1) {
            address.setLine1(input.address[0]);
            if (input.address.length >= 2) {
                address.setLine2(input.address[1]);
            }
            if (input.address.length >= 3) {
                address.setLine3(input.address[2]);
            }
        }
        address.setTown(input.town);
        address.setCounty(input.county);
        address.setPostCode(input.postcode);
        return address;
    }
}
