package com.ecco.webApi.contacts;


import com.ecco.webApi.evidence.EvidenceAssociatedContactCommandViewModel;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

/**
 * Links a contact to a service recipient.
 * This was introduced to add audits for the 'contacts' tab for a referral file.
 * The idea of it (in DEV-1169) was that it could include the 'relationships' tab
 * to create a concept of networks of supporting contacts.
 * {@link com.ecco.dom.Referral#primaryReferral}
 * DEV-1169 also describes ways of recording notes - perhaps using {@link EvidenceAssociatedContactCommandViewModel}.
 */
public class ServiceRecipientAssociatedContactViewModel {

    /**
     * The service recipient
     */
    public Integer serviceRecipientId;

    /**
     * contact and organisation are optional (one or the other)
     * so this avoid us looking in individual/agency for the contactId
     */
    public Integer contactId;

    /**
     * The contact, if an individual.
     */
    public IndividualViewModel contact;

    /**
     * The contact, if an agency, or an individual's organisation if one if there
     */
    public AgencyViewModel organisation;

    /**
     * The list definition representing the associations this contact has with the srId.
     * So this is typically the 'GP', but could also become the Relation.
     * An array allows us to handle more than one relation type (once the domain allows it).
     * See DEV-1169.
     */
    public Integer[] associatedTypeIds;

    /**
     * The UTC date and time when the contact was linked (for the purposes of visual ordering)
     */
    public DateTime created;

    /**
     * The date and time when the contact was archived
     */
    public LocalDate archived;

}
