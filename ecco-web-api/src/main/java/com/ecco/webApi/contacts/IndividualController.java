package com.ecco.webApi.contacts;

import static java.util.stream.Collectors.toList;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.config.service.SettingsService.AgencySearchScope;
import com.ecco.dao.ContactAllocationRepository;
import com.ecco.service.AgencyScope;
import com.ecco.service.ContactService;
import com.ecco.serviceConfig.dom.ContactAllocation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dom.Individual;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.service.UserManagementService;
import com.ecco.webApi.controllers.BaseWebApiController;
import lombok.RequiredArgsConstructor;

import org.jspecify.annotations.NonNull;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
@RequiredArgsConstructor
public class IndividualController extends BaseWebApiController {

    private final IndividualRepository individualRepository;
    private final AddressRepository addressRepository;
    private final UserManagementService userManagementService;
    private final ContactAllocationRepository contactAllocationRepository;
    private final ContactService contactService;
    private final IndividualFromViewModel individualFromViewModel;
    private final IndividualUserSummaryToViewModel individualUserSummaryToViewModel;
    private final IndividualToViewModel individualToViewModel = new IndividualToViewModel();

    @GetJson("/individuals/{id}/")
    public IndividualViewModel findOne(@PathVariable long id) {
        return individualToViewModel.apply(individualRepository.findById(id).orElse(null));
    }

    @GetJson("/individuals/")
    public Iterable<IndividualViewModel> findAll(
            @RequestParam(value = "id", required = false) long[] individualIds,
            @RequestParam(value="companyId", required=false) Long companyId
    ) {
        List<Individual> individuals =
            companyId != null ? individualRepository.findByCompanyId(companyId)
                    : individualIds != null ? individualRepository.findAllById(individualIds)
                    : individualRepository.findAll();

        return individuals.stream().map(individualToViewModel::apply).collect(toList());
    }

    @GetJson("/individuals/professionals/")
    public Iterable<IndividualViewModel> findAllProfessionals(
            @RequestParam(required=false, defaultValue="false") Boolean includeArchived,
            @RequestParam(required=false) Integer contextId) {

        // NB the query params don't act in combination currently
        if (Boolean.TRUE.equals(includeArchived)) {
            return individualRepository.findAllByCompanyIsNotNull().stream().map(individualToViewModel).collect(toList());
        }

        return getIndividualsByScope(contextId);
    }

    @NonNull
    private List<IndividualViewModel> getIndividualsByScope(Integer contextId) {

        if (contextId == null) {
            return individualRepository.findAllByCompanyIsNotNullAndArchivedIsNull().stream().map(individualToViewModel).collect(toList());
        }

        var setting = settingsService.settingFor(AgencySearchScope.NAMESPACE, AgencySearchScope.AGENCY_SCOPE);
        AgencyScope value = setting.getAsEnum();
        if (AgencyScope.GLOBAL.equals(value)) {
            return individualRepository.findAllByCompanyIsNotNullAndArchivedIsNull().stream().map(individualToViewModel).collect(toList());
        }

        List<Integer> allocationIds = contactService.getContactScopeServiceAllocationIdsFromFile(contextId, value);
        return contactAllocationRepository.findAllIndividualsByArchivedIsNullOrderByLastNameForAllocationIds(allocationIds).stream().map(individualToViewModel).collect(toList());
    }


    /** TODO: This needs sorting out for when encryption is enabled! */
    @GetJson("/individuals/byLastName/{lastNamePrefix}/")
    public Iterable<IndividualViewModel> findMatchesByLastName(@PathVariable String lastNamePrefix) {
        return individualRepository.findByLastNameStartsWith(lastNamePrefix).stream().map(individualToViewModel::apply).collect(toList());
    }
    /** TODO: This needs sorting out for when encryption is enabled! */
    @GetJson("/individuals/filter/byName/{firstName}/{lastName}/")
    public Iterable<IndividualViewModel> findMatchesByFirstNameAndLastName(@PathVariable String firstName,
            @PathVariable String lastName) {
        return individualRepository.findByFirstNameAndLastName(firstName, lastName).stream().map(individualToViewModel::apply).collect(toList());
    }

    @GetJson("/individuals/filter/username/{username}/")
    public IndividualUserSummaryViewModel findIndividualUserByUsername(@PathVariable String username) {
        return individualUserSummaryToViewModel.apply(userManagementService.findIndividualsFromUsername(username).get(0));
    }

    @GetJson("/individuals/filter/user/contactId/{contactId}/")
    public IndividualUserSummaryViewModel findIndividualUserByContactId(@PathVariable Integer contactId) {
        return individualUserSummaryToViewModel.apply(userManagementService.findIndividualsFromContactId(contactId).get(0));
    }

    @GetJson("/individuals/filter/authority/{authority}/")
    public Iterable<IndividualUserSummaryViewModel> findIndividualsWithAuthority(@PathVariable String authority) {
        return userManagementService.findIndividualsWithAuthority(authority).stream().map(individualUserSummaryToViewModel).collect(toList());
    }


    @PostJson("/individuals/")
    public Map<String, Long> create(@RequestBody IndividualViewModel individualViewModel) {

        boolean newIndividual = individualViewModel.contactId == null;

        Individual individual = null;
        if (!newIndividual) {
            individual = individualRepository.findOne(individualViewModel.contactId);
            IndividualFromViewModel.apply(addressRepository, individual, individualViewModel);
        } else {
            if (StringUtils.isNotBlank(individualViewModel.code)) {
                List<Individual> matchingIndividuals = individualRepository.findAllByCode(individualViewModel.code);
                throwForExistingOrTooManyMatches(matchingIndividuals);
            }
            individual = individualFromViewModel.apply(individualViewModel);
        }

        individualRepository.save(individual);

        // only professionals should update contacts with allocated services
        if (newIndividual && individualViewModel.organisationId != null) {
            var allocations = contactAllocationRepository.findAllByContactId(individualViewModel.organisationId);
            for (ContactAllocation allocation : allocations) {
                var ca = new ContactAllocation(allocation.getServiceAllocationId(), individual.getId());
                contactAllocationRepository.save(ca);
            }
        }

        return Collections.singletonMap("id", individual.getId());
    }
}
