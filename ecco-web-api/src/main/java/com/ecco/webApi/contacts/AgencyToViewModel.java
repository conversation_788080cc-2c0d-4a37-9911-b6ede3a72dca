package com.ecco.webApi.contacts;

import java.util.function.Function;

import com.ecco.dom.Agency;
import com.ecco.webApi.contacts.address.AddressToViewModel;
import com.ecco.webApi.contacts.address.AddressedLocationToViewModel;
import org.jspecify.annotations.Nullable;

public class AgencyToViewModel implements Function<Agency, AgencyViewModel> {

    private final AddressToViewModel addressToViewModel = new AddressToViewModel();
    private final AddressedLocationToViewModel addressedLocationToViewModel = new AddressedLocationToViewModel();

    @Nullable
    @Override
    public AgencyViewModel apply(@Nullable Agency input) {
        if (input == null) {
            throw new NullPointerException("input Agency must not be null");
        }

        AgencyViewModel agencyViewModel = new AgencyViewModel();
        agencyViewModel.contactId = input.getId();
        agencyViewModel.avatarId = input.getAvatarId();

        agencyViewModel.code = input.getCode();
        agencyViewModel.discriminator = ContactViewModel.AGENCY;
        agencyViewModel.agencyCategoryId = input.getAgencyCategoryId();
        agencyViewModel.companyName = input.getCompanyName();
        agencyViewModel.email = input.getEmail();
        agencyViewModel.phoneNumber = input.getPhoneNumber();
        agencyViewModel.archived = input.getArchived();

        if (input.getAddressedLocation() != null) {
            agencyViewModel.addressedLocationId = input.getAddressedLocation().getId();
            agencyViewModel.address = addressedLocationToViewModel.apply(input.getAddressedLocation());
        } else {
            var address = input.getAddress();
            if (address != null) {
                agencyViewModel.address = addressToViewModel.apply(address);
            }
        }
        return agencyViewModel;
    }
}
