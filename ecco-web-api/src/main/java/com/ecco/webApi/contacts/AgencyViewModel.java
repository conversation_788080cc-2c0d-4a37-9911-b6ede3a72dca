package com.ecco.webApi.contacts;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AgencyViewModel extends ContactViewModel {
    /**
     * A context of a new agency, used here only for create.
     * Currently, this is the serviceRecipientId which is used to record the agency's relevant service allocation.
     */
    public Integer contextId;

    public String companyName;

    public Boolean outOfArea;

    public Integer agencyCategoryId;
    //public String agencyCategoryNew; // used in importing

    // Property accessors are unfortunately required for com.ecco.data.client.csv.CSVBeanReader.
    // See ECCO-703

}
