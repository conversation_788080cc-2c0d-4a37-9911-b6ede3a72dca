package com.ecco.webApi.serviceConfig;

import org.jspecify.annotations.NonNull;

import com.ecco.serviceConfig.dom.QuestionAnswerFree;
import com.ecco.serviceConfig.dom.QuestionGroupQuestion;
import com.ecco.serviceConfig.repositories.QuestionGroupRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.dto.ChangeViewModel;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.Question;
import com.ecco.serviceConfig.dom.QuestionnaireCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.QuestionRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.Serializable;
import java.util.Collections;

@Component
public class QuestionCommandHandler extends BaseCommandHandler<QuestionCommandViewModel, Long,
            ConfigCommand, @Nullable Void> {

    @NonNull
    private final QuestionRepository questionRepository;
    @NonNull
    private final QuestionGroupRepository questionGroupRepository;

    @Autowired
    public QuestionCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
            QuestionRepository questionRepository, QuestionGroupRepository questionGroupRepository) {
        super(objectMapper, configCommandRepository, QuestionCommandViewModel.class);
        this.questionRepository = questionRepository;
        this.questionGroupRepository = questionGroupRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull QuestionCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case QuestionnaireBaseCommandViewModel.OPERATION_ADD:
                this.addQuestion(auth, viewModel);
                break;

            case QuestionnaireBaseCommandViewModel.OPERATION_UPDATE:
                this.updateQuestion(auth, viewModel);
                break;

            case QuestionnaireBaseCommandViewModel.OPERATION_REMOVE:
                // can't remove
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addQuestion(Authentication auth, QuestionCommandViewModel cmdVM) {
        Question question = new Question();
        this.applyChanges(question, cmdVM);

        // only a type change on adding - complex otherwise, and just disable qn and add another
        if (cmdVM.typeChange != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.typeChange, question.getType(), "name");
            switch (cmdVM.typeChange.to) {
                case "list":
                    if (cmdVM.listNameChange != null) {
                        question.getParameters().put("listName", cmdVM.listNameChange.to);
                    }
                case "choices":
                    // do nothing - individual commands add the choices
                    break;
                default:
                    QuestionAnswerFree f = new QuestionAnswerFree();
                    f.setValueType(cmdVM.typeChange.to);
                    question.setFreeTypes(Collections.singleton(f));
            }
        }

        question = this.questionRepository.save(question);
        var qg = this.questionGroupRepository.getOne(cmdVM.questionGroupId.longValue());
        var qgq = new QuestionGroupQuestion(qg, question, cmdVM.orderByChange != null ? cmdVM.orderByChange.to : 0);
        // requires a not null questions
        qg.getQuestions().add(qgq);
    }

    private void updateQuestion(Authentication auth,  QuestionCommandViewModel cmdVM) {
        Question question = this.questionRepository.findById(cmdVM.id.longValue()).orElse(null);
        this.applyChanges(question, cmdVM);
        this.questionRepository.save(question);

        this.applyOrderBy(cmdVM.questionGroupId, question.getId(), cmdVM.orderByChange);
    }

    private void applyChanges(Question question, QuestionCommandViewModel cmdVM) {
        if (cmdVM.hasChanges()) {
            if (cmdVM.nameChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.nameChange, question.getName(), "name");
                question.setName(cmdVM.nameChange.to);
            }
            if (cmdVM.listNameChange != null) {
                question.getParameters().put("listName", cmdVM.listNameChange.to);
            }
            if (cmdVM.disableChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.disableChange, question.isDisabled(), "disabled");
                question.setDisabled(cmdVM.disableChange.to);
            }
        }
    }

    private void applyOrderBy(Integer questionGroupId, Long questionId, ChangeViewModel<Integer> orderByChange) {
        if (orderByChange != null) {
            //warnIfPrevValueDoesntMatch(cmdVM, cmdVM.orderByChange, question.get(), "orderBy");
            questionGroupRepository.updateOrderBy(orderByChange.to, questionGroupId, questionId);
        }
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull QuestionCommandViewModel viewModel, long userId) {
        return new QuestionnaireCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
