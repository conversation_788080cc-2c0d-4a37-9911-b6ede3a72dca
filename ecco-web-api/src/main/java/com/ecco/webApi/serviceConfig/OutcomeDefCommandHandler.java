package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class OutcomeDefCommandHandler extends BaseCommandHandler<OutcomeDefCommandViewModel, Long,
            ConfigCommand, @Nullable Void> {

    @NonNull
    private final OutcomeRepository outcomeRepository;

    @Autowired
    public OutcomeDefCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                    OutcomeRepository outcomeRepository) {
        super(objectMapper, configCommandRepository, OutcomeDefCommandViewModel.class);
        this.outcomeRepository = outcomeRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull OutcomeDefCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                this.addOutcome(auth, viewModel);
                break;

            case BaseCommandViewModel.OPERATION_UPDATE:
                this.updateOutcome(auth, viewModel);
                break;

            /* TOOD hide the outcome
            case BaseCommandViewModel.OPERATION_REMOVE:
                this.removeFlag(auth, viewModel);
                break;
            */

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addOutcome(Authentication auth, OutcomeDefCommandViewModel cmdVM) {
        Outcome outcome = createOutcome(cmdVM);
        outcome.setUuid(cmdVM.outcomeDefUuid);
        this.applyChanges(outcome, cmdVM);
        this.outcomeRepository.save(outcome);
    }

    private Outcome createOutcome(OutcomeDefCommandViewModel cmdVM) {
        if (OutcomeType.NEEDS.equals(cmdVM.outcomeType)) {
            return new OutcomeSupport();
        }
        if (OutcomeType.THREAT.equals(cmdVM.outcomeType)) {
            OutcomeThreat ot = new OutcomeThreat();
            ot.setWeighting(0);
            return ot;
        }
        throw new IllegalArgumentException("OutcomeType not known: " + cmdVM.outcomeType.toString());
    }

    private void updateOutcome(Authentication auth,  OutcomeDefCommandViewModel cmdVM) {
        if (!cmdVM.hasChanges()) {
            return;
        }
        Outcome outcome = this.outcomeRepository.findOneByUuid(cmdVM.outcomeDefUuid);
        this.applyChanges(outcome, cmdVM);
        this.outcomeRepository.save(outcome);
    }

    private void applyChanges(Outcome outcome, OutcomeDefCommandViewModel cmdVM) {
        if (!cmdVM.hasChanges()) {
            return;
        }
        if (cmdVM.nameChange != null) {
            warnIfPrevValueDoesntMatch(cmdVM, cmdVM.nameChange, outcome.getName(), "name");
            outcome.setName(cmdVM.nameChange.to);
        }
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull OutcomeDefCommandViewModel viewModel, long userId) {
        return new OutcomeDefCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
