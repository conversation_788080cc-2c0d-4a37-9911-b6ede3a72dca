package com.ecco.webApi.serviceConfig;

import org.jspecify.annotations.NonNull;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@Secured("ROLE_ADMIN")
public class ConfigCommandController extends BaseWebApiController {

    @NonNull
    private final ActivityTypeCommandHandler activityTypeCommandHandler;
    @NonNull
    private final ProjectCommandHandler projectCommandHandler;
    @NonNull
    private final ServiceCommandHandler serviceCommandHandler;
    @NonNull
    private final QuestionCommandHandler questionCommandHandler;
    @NonNull
    private final QuestionChoiceCommandHandler questionChoiceCommandHandler;
    @NonNull
    private final QuestionGroupCommandHandler questionGroupCommandHandler;
    @NonNull
    private final OutcomeCloneCommandHandler outcomeCloneCommandHandler;
    @NonNull
    private final RiskAreaCloneCommandHandler riskAreaCloneCommandHandler;
    @NonNull
    private final OutcomeDefCommandHandler outcomeDefCommandHandler;
    @NonNull
    private final ActionDefCommandHandler actionDefCommandHandler;
    @NonNull
    private final ActionGroupDefCommandHandler actionGroupDefCommandHandler;

    @Autowired
    public ConfigCommandController(
            @NonNull ActivityTypeCommandHandler activityTypeCommandHandler,
            @NonNull ProjectCommandHandler projectCommandHandler,
            @NonNull QuestionCommandHandler questionCommandHandler,
            @NonNull QuestionGroupCommandHandler questionGroupCommandHandler,
            @NonNull QuestionChoiceCommandHandler questionChoiceCommandHandler,
            @NonNull ServiceCommandHandler serviceCommandHandler,
            @NonNull OutcomeCloneCommandHandler outcomeCloneCommandHandler,
            @NonNull RiskAreaCloneCommandHandler riskAreaCloneCommandHandler,
            @NonNull OutcomeDefCommandHandler outcomeDefCommandHandler,
            @NonNull ActionDefCommandHandler actionDefCommandHandler,
            @NonNull ActionGroupDefCommandHandler actionGroupDefCommandHandler) {
        this.activityTypeCommandHandler = activityTypeCommandHandler;
        this.projectCommandHandler = projectCommandHandler;
        this.serviceCommandHandler = serviceCommandHandler;
        this.questionCommandHandler = questionCommandHandler;
        this.questionGroupCommandHandler = questionGroupCommandHandler;
        this.questionChoiceCommandHandler = questionChoiceCommandHandler;
        this.outcomeCloneCommandHandler = outcomeCloneCommandHandler;
        this.riskAreaCloneCommandHandler = riskAreaCloneCommandHandler;
        this.outcomeDefCommandHandler = outcomeDefCommandHandler;
        this.actionDefCommandHandler = actionDefCommandHandler;
        this.actionGroupDefCommandHandler = actionGroupDefCommandHandler;
    }

    @RequestMapping(value = "/config/activityType/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @Secured({"ROLE_ADMIN", "ROLE_ADMINGROUPSUPPORT"})
    public Result processActivityTypeCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return activityTypeCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/project/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processProjectCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return projectCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/service/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processServiceCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return serviceCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/questionnaire/question/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processQuestionCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return questionCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/questionnaire/questionChoice/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processQuestionChoiceCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return questionChoiceCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/questionnaire/questionGroup/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processQuestionGroupCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return questionGroupCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/outcome/clone/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processOutcomeCloneCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return outcomeCloneCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/riskArea/clone/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processRiskAreaCloneCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return riskAreaCloneCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/outcomeDef/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processOutcomeCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return outcomeDefCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/actionDef/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processActionDefCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return actionDefCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/config/actionGroupDef/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processActionGroupDefCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return actionGroupDefCommandHandler.handleCommand(authentication, null, requestBody);
    }

}
