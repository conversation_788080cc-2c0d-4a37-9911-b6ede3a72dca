package com.ecco.webApi.acls;

import java.util.function.Function;

import com.ecco.security.dto.AclEntryDto;
import org.jspecify.annotations.NonNull;

public class AclEntryDtoToViewModel implements Function<AclEntryDto, AclEntryViewModel> {

    @Override
    @NonNull
    public AclEntryViewModel apply(@NonNull AclEntryDto input) {
        return new AclEntryViewModel(input.username, input.secureObjectId, input.clazz.getCanonicalName(), input.permission.getMask());
    }

}
