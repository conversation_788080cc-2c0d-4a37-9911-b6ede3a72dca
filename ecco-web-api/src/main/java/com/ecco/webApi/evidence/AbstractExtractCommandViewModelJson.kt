package com.ecco.webApi.evidence

import com.ecco.infrastructure.dom.BaseCommand
import com.ecco.security.repositories.UserRepository
import com.fasterxml.jackson.core.io.JsonStringEncoder
import org.springframework.util.Assert
import java.util.function.Function
import java.util.stream.Collectors
import java.util.stream.Stream

/** Extracts the raw JSON body and enhances it by prefixing aspects that are useful (often from the persisted command)  */
abstract class AbstractExtractCommandViewModelJson<T : BaseCommand<*>> internal constructor(private val userRepository: UserRepository) :
    Function<T, CharSequence> {
    override fun apply(input: T): CharSequence {
        val body = input.body
        Assert.state(body.startsWith("{\"") || body == "{}", "Body must be valid JSON starting {\" or empty {}")

        // If we get an NPE then we've got some problems in the database as we should never delete user record
        val user = userRepository.findById(input.userId).get()
        val encoder = JsonStringEncoder.getInstance()
        val username = encoder.quoteAsString(user.username)
        val userDisplayName = encoder.quoteAsString(user.contact.displayName)
        val result =
            StringBuilder()
                .append("{\"userName\":\"")
                .append(username)
                .append("\",\"userDisplayName\":\"")
                .append(userDisplayName)
                .append("\"")
        if (input.commandName != null) {
            result
                .append(",\"commandName\":\"")
                .append(input.commandName)
                .append("\"")
        }
        result.append(applyAdditionalProperties(input))
        return if (body == "{}") {
            result.append('}') // {} -> }
        } else {
            result
                .append(',')
                .append(body.substring(1)) // {"p":1}  -> ,"p":1}
        }
    }

    protected open fun applyAdditionalProperties(input: T): String = ""

    open fun asJsonArray(commands: List<T>): String = asJsonArray(commands.stream())

    open fun asJsonArrayx(commands: List<T>): String = asJsonArray(commands.stream())

    fun asJsonArray(commands: Stream<out T>): String = commands.map(this).collect(Collectors.joining(",", "[", "]"))
}