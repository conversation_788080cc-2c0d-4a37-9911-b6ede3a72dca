package com.ecco.webApi.evidence;

import com.ecco.dao.AgencyRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.Individual;
import com.ecco.dto.ChangeViewModel;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.clients.InboundReferralParams;
import com.ecco.webApi.clients.InboundReferralResource;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.contacts.ClientWebService;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.ecco.calendar.core.util.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Instant;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.net.URI;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

import static com.ecco.service.security.RunAsTemplate.runAsExternalUserAccountIfNecessary;
import static java.lang.Long.parseLong;


/**
 * Controller for allowing public
 */
@RestController
//@PreAuthorize("permitAll()") // - if we do this things blow up on getEntityTypeName expecting an authentication object
@RequestMapping("/inbound/referrals")
@RequiredArgsConstructor
@Slf4j
public class InboundReferralController extends SchemaProvidingController<InboundReferralController> {

    @PersistenceContext
    private EntityManager entityManager;

    private final ClientWebService clientWebService;
    private final ReferralController referralController;
    private final EvidenceFormWorkController formController;
    private final AgencyRepository agencyRepository;
    private final IndividualRepository individualRepository;
    private final ReferralRepository referralRepository;
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private final ObjectMapper objectMapper;

    @PostJson
    public ResponseEntity<Result> createReferral(@RequestBody InboundReferralParams params) {

        return runAsExternalUserAccountIfNecessary(() -> {
            try {
                return createClientAndReferral(params);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @NonNull
    ResponseEntity<Result> createClientAndReferral(@RequestBody InboundReferralParams params) throws IOException {
        InboundReferralResource referral = params.getDto();

        Result referralResult = createClientAndReferral(referral);

        // NB we need to clear - else we don't get a full serviceRecipient at FormUpdateCommandHandler:85 (addEntry)
        // leaving the referral property empty, causing an error on the handler for the withContact() during newSnapshotBuilder
        entityManager.clear();

        try {
            this.saveReferralDetails(Long.parseLong(referralResult.getId()), params.getFormDefinitionUuid(), params.getFormData());
        } catch (IOException e) {
            return ResponseEntity.badRequest().body(new Result("failed to save referral details for: " + referralResult));
        }

        // TODO errors??
        return ResponseEntity.created(URI.create("/api/inbound/referrals/")).body(referralResult);
    }

    private Result createClientAndReferral(InboundReferralResource referral) throws IOException {
        ClientViewModel client = new ClientViewModel();
        client.firstName = referral.getFirstName();
        client.lastName = referral.getLastName();
        client.birthDate = referral.getBirthDate();
        client.phoneNumber = referral.getPhoneNumber();
        client.mobileNumber = referral.getMobileNumber();
        client.email = referral.getEmail();
        client.genderId = referral.getGenderId() != null ? referral.getGenderId().intValue() : null;
        client.firstLanguageId = referral.getFirstLanguageId();
        client.ethnicOriginId = referral.getEthnicOriginId();
        client.religionId = referral.getReligionId();
        client.nationalityId = referral.getNationalityId();
        client.disabilityId = referral.getDisabilityId();
        client.sexualOrientationId = referral.getSexualOrientationId();
        client.maritalStatusId = referral.getMaritalStatusId();
        client.ni = referral.getNi();
        client.nhs = referral.getNhs();
        client.housingBenefit = referral.getHousingBenefit();
        client.address = new AddressViewModel();
        client.address.setLine1(referral.getLine1());
        client.address.town = referral.getTown();
        client.address.postcode = referral.getPostCode();

        // find the client first (if found, don't update details - its not their data)
        // if not, then create the client
        Result result = clientWebService.checkExists(client);
        if (result.getId() == null) {
            // NB client created without audit, but the referral below has an audit
            result = clientWebService.create(client, false);
        }
        long clientId = parseLong(result.getId());

        ReferralViewModel rvm = new ReferralViewModel();
        rvm.setClientId(clientId);
        rvm.setReceivedDate(LocalDate.now());
        rvm.setReferralReason(referral.getReferralReason());
        var svcCat = serviceCategorisationRepository.findOneByService_IdAndProject_Id(referral.getServiceId(), referral.getProjectId());
        rvm.serviceAllocationId = svcCat.getId();

        rvm.selfReferral = Boolean.TRUE.equals(referral.getSelfReferral());

        if (!rvm.selfReferral) {

            // match against a professional email only
            // if we are a family member etc, its not matched
            var referrerEmail = StringUtils.trimAllWhitespace(referral.getReferrerEmail());
            var professionalMatches = StringUtils.hasText(referrerEmail)
                ? individualRepository.findAllByProfessionalEmail(referrerEmail)
                : Collections.<Individual>emptyList();

            // if match, use that
            // TODO should we update the referrer details from the form?
            if (professionalMatches.size() == 1) {
                var professional = professionalMatches.get(0);
                if (professional.getCompany() != null) {
                    rvm.setReferrerAgencyId(professional.getCompany().getId());
                }
                rvm.setReferrerIndividualId(professional.getId());

            // or create what we can, instantly archived - if we are a professional
            } else {
                Long agencyId = InboundBaseController.createAgencyMaybe(agencyRepository, referral.getAgencyName());
                rvm.setReferrerAgencyId(agencyId);
                Long contactId = InboundBaseController.createContactMaybe(agencyRepository, individualRepository, referral, agencyId);
                rvm.setReferrerIndividualId(contactId);
            }
        }

        // creates the audit for the referral, and triggers CommandCreated event
        return referralController.createImport(rvm);
    }

    private void saveReferralDetails(long referralId, @Nullable UUID formDefinitionUuid, JsonNode jsonPatch) throws IOException {

        if (formDefinitionUuid == null) {
            log.info("InboundReferralController: skipping formdefinition empty");
            return;
        }
        // we do a check on the value type only - and assume that Array/Object etc mean data exists
        if (jsonPatch == null || (jsonPatch.isValueNode() && jsonPatch.asText().isEmpty()) || jsonPatch.isNull() || jsonPatch.asText().equals("null")) {
            log.info("InboundReferralController: skipping jsonPatch as empty: " + objectMapper.writeValueAsString(jsonPatch));
            return;
        }

        int serviceRecipientId = referralRepository.getServiceRecipientId(referralId);

        UUID workUuid = UUID.randomUUID();
        EvidenceFormSnapshotCommandViewModel vm = new EvidenceFormSnapshotCommandViewModel(BaseCommandViewModel.OPERATION_ADD,
                serviceRecipientId, "referralDetails", "referralDetails", formDefinitionUuid);
        vm.workUuid = workUuid;
        vm.timestamp = new Instant();
        vm.workDate = ChangeViewModel.changeNullTo(DateTimeUtils.convertFromUtcToUsersLocalDateTime(new DateTime()));
        vm.jsonPatch = jsonPatch;

        String jsonViewModel = objectMapper.writeValueAsString(vm);
        //{"commandName":"formUpdate","commandUri":"service-recipients/200641/evidence/json/referralDetails/referralDetails/","uuid":"f39630cc-af3f-4d64-7a61-ddaa345ea062","operation":"add","timestamp":"2018-02-08T15:36:29.076Z","serviceRecipientId":200641,"evidenceGroup":"referralDetails","taskName":"referralDetails","workUuid":"993c0f9e-4127-4312-769c-d5bd76a123a8","workDate":{"from":null,"to":"2018-02-08T15:36:29.073"},"jsonPatch":[{"op":"replace","path":"/medication/0/name","value":"save in Task.referralDetails4"}]}

        EvidenceParams params = new EvidenceParams();
        params.serviceRecipientId = serviceRecipientId;
        params.taskName = "referralDetails";
        params.evidenceGroupKey = "referralDetails";

        formController.patchSnapshot(SecurityContextHolder.getContext().getAuthentication(), params, jsonViewModel);
    }

    @Override
    public String getEntityTypeName() {
        return "inbound-referrals";
    }

    @Override
    @GetJson("/$schema/")
    public ResponseEntity<JsonSchema> describe(WebRequest request) {
        JsonSchema schema = getSchemaCreator().create(InboundReferralResource.class,
                self().describe(request),
                Optional.empty(),
                Optional.of(self().createReferral(null)));

        return ResponseEntity.ok(schema);
    }
}
