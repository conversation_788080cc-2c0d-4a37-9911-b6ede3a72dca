package com.ecco.webApi.evidence;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.webApi.CommandResult;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.Authentication;


/**
 * An interface to indicate to implementing evidence Handlers that they need to be aware of parent and child recipients
 * which comes about from the use of central processing - saving evidence against the parent only but including the
 * childServiceRecipientId so that we could separate which recipient the work was actually done against.
 */
public interface ParentChildHandler<VM extends BaseServiceRecipientCommandViewModel,
        ENTITY extends ServiceRecipientCommand, PARAMS extends EvidenceParams> {

    @Nullable
    CommandResult handleInternal(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth,
                                 PARAMS params, VM viewModel);

}
