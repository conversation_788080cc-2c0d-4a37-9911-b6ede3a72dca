package com.ecco.webApi.evidence;

import com.ecco.dao.EvidenceFlagSummary;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

public class EvidenceFlagSummaryToViewModel implements Function<EvidenceFlagSummary, FlagViewModel> {

        @Override
        @Nullable
        public FlagViewModel apply(@Nullable EvidenceFlagSummary input) {
        if (input == null) {
            throw new NullPointerException("input Referral must not be null");
        }

        FlagViewModel viewModel = new FlagViewModel();
        viewModel.id = input.getId();
        viewModel.flagId = input.getFlagId();
        viewModel.value = input.isValue();
        viewModel.workId = input.getWorkId();

        return viewModel;
    }

}
