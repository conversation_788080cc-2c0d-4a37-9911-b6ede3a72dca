package com.ecco.webApi.evidence;

import java.util.UUID;

import org.joda.time.LocalDateTime;

/**
 * A signature.
 * <p/>
 * This is a signature in the sense of a hand-written name, indicating the
 * named party's agreement to or acknowledgement of some particulars.
 */
public final class SignatureViewModel {
    /**
     * The Signature ID.
     */
    public UUID id;

    /**
     * TODO: Rename this. It is now by default svg, unless it starts data: and is therefore a data URL of any type
     * SVG data representing the signature graphically.
     * <p/>
     * This is in the form of a complete SVG XML document.
     */
    public String svgXml;

    /**
     * Date and time when this signature was created
     */
    public LocalDateTime signedDate;

    /**
     * Display name of who this signature is on behalf of (usually the service recipient)
     */
    public String signedFor;
}
