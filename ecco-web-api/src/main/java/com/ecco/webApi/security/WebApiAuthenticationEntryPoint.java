package com.ecco.webApi.security;

import com.ecco.infrastructure.web.RequestUtils;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 * Because we support HTTP Basic Authentication on the web API, we need to ensure tha the browser will not
 * pop up a dialog (as it normally would) if we are not authenticated.
 * Therefore we do not set the status code to HTTP 401 or set a WWW-Authenticate header.
 */
@Slf4j
public class WebApiAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {

        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        String jsonReturn = objMapper.writeValueAsString(new Result("unauthenticated: " + authException.getMessage()));

        // show more info like encryption errors over the standard authentication error mechanism 'access denied'
        log.info("WebApi authentication failure: {}", authException.getMessage());
        if (RequestUtils.isRequestFromLocalhostForDevOnly(request.getRemoteHost())) {
            log.error("WebApi authentication failure: LOCALHOST may need setting with -Dcookie.insecure=true");
        }

        response.getWriter().write(jsonReturn);
    }
}
