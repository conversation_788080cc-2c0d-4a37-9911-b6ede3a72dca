package com.ecco.serviceConfig.service;

import static java.util.stream.Collectors.groupingBy;

import com.ecco.serviceConfig.dom.QuestionAnswerChoiceView;
import com.ecco.serviceConfig.dom.QuestionAnswerFreeView;
import com.ecco.serviceConfig.dom.QuestionGroupQuestionView;
import com.ecco.serviceConfig.dom.QuestionGroupView;
import com.ecco.serviceConfig.repositories.QuestionAnswerChoiceRepository;
import com.ecco.serviceConfig.repositories.QuestionAnswerFreeRepository;
import com.ecco.serviceConfig.repositories.QuestionRepository;
import com.ecco.serviceConfig.viewModel.QuestionAnswerFreeViewModel;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;
import java.util.List;
import java.util.Map;
import java.util.stream.StreamSupport;

import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.serviceConfig.dom.OutcomeSupport;
import com.ecco.serviceConfig.dom.OutcomeThreat;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.serviceConfig.repositories.QuestionGroupRepository;
import com.ecco.serviceConfig.viewModel.OutcomeToViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaToViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;

@Service("sessionDataService")
@ReadOnlyTransaction
@RequiredArgsConstructor
public class SessionDataServiceImpl implements SessionDataService {

    private final OutcomeToViewModel outcomeToViewModel = new OutcomeToViewModel();
    private final RiskAreaToViewModel riskAreaToViewModel = new RiskAreaToViewModel();

    private final OutcomeRepository outcomeRepository;
    private final QuestionAnswerChoiceRepository questionAnswerChoiceRepository;
    private final QuestionAnswerFreeRepository questionAnswerFreeRepository;
    private final QuestionGroupRepository questionGroupRepository;
    private final QuestionRepository questionRepository;

    @Cacheable(value="outcomeViewModels", key="'*'")
    @Override
    public List<OutcomeViewModel> findOutcomes() {
        Iterable<OutcomeSupport> outcomes = outcomeRepository.findAllSupport();
        return StreamSupport.stream(outcomes.spliterator(), false).map(outcomeToViewModel).toList();
    }

    @Cacheable(value="riskAreaViewModels", key="'*'")
    @Override
    public List<RiskAreaViewModel> findRiskAreas() {
        Iterable<OutcomeThreat> riskAreas = outcomeRepository.findAllThreat();
        return StreamSupport.stream(riskAreas.spliterator(), false).map(riskAreaToViewModel).toList();
    }

    @Cacheable(value="questionGroupViewModels", key="'*'")
    @Override
    public List<QuestionGroupViewModel> findQuestionGroups() {
        var questionsByQuestionGroupId = questionRepository.findAllAsViews().stream()
                .collect(groupingBy(QuestionGroupQuestionView::questionGroupId));

        var choices = questionAnswerChoiceRepository.findAllAsViews().stream()
                .collect(groupingBy(QuestionAnswerChoiceView::questionId));
        var frees = questionAnswerFreeRepository.findAllAsViews().stream()
                .collect(groupingBy(QuestionAnswerFreeView::questionId));

        var questionGroupsViews = questionGroupRepository.findAllAsViews();
        return StreamSupport.stream(questionGroupsViews.spliterator(), false).map(
                new QuestionGroupViewToDto(questionsByQuestionGroupId, choices, frees)::apply).toList();
    }

    private record QuestionGroupViewToDto(Map<Long, List<QuestionGroupQuestionView>> questionsByQuestionGroupId,
                                          Map<Long, List<QuestionAnswerChoiceView>> choicesByQuestionId,
                                          Map<Long, List<QuestionAnswerFreeView>> freesByQuestionId) {

        public QuestionGroupViewModel apply(QuestionGroupView questionGroupView) {
            var result = new QuestionGroupViewModel();
            result.id = questionGroupView.id();
            result.name = questionGroupView.name();
            result.disabled = questionGroupView.disabled();
            result.headerText = questionGroupView.headerText();
            result.parameters = questionGroupView.getParameters();

            result.questions = questionsByQuestionGroupId.get(questionGroupView.id()).stream()
                    .map(qgq -> {
                        var qn = new QuestionViewModel();
                        qn.id = qgq.id().intValue();
                        qn.name = qgq.name();
                        qn.disabled = qgq.disabled();
                        qn.answerType = qgq.answerType();
                        qn.answerRequired = qgq.answerRequired();
                        qn.parameters = qgq.getParameters();
                        qn.orderby = qgq.orderby();
                        var choices = choicesByQuestionId.get(qgq.id());
                        qn.choices = choices;
                        var frees = freesByQuestionId.get(qgq.id());
                        qn.freeTypes = frees == null ? null : frees.stream()
                                .map(f -> { // Note: f contains extra field, questionId which would bulk up the JSON
                                    var qf = new QuestionAnswerFreeViewModel();
                                    qf.id = f.id().intValue();
                                    qf.minimum = f.minimum();
                                    qf.maximum = f.maximum();
                                    qf.valueType = f.valueType();
                                    return qf;
                                }).toList();
                        return qn;
                    }).toList();
            return result;
        }
    }
}
