package com.ecco.serviceConfig.viewModel;

import static java.util.stream.Collectors.toList;

import java.util.function.Function;

import com.ecco.serviceConfig.dom.Question;
import com.ecco.serviceConfig.dom.QuestionGroupSupport;
import org.jspecify.annotations.Nullable;

public class QuestionGroupToViewModel implements Function<QuestionGroupSupport, QuestionGroupViewModel> {

    private static final Function<Question, QuestionViewModel> questionToViewModel = new QuestionToViewModel();

    @Nullable
    @Override
    public QuestionGroupViewModel apply(@Nullable QuestionGroupSupport input) {
        if (input == null) {
            throw new NullPointerException("input QuestionGroupSupport must not be null");
        }

        QuestionGroupViewModel questionGroupViewModel = new QuestionGroupViewModel();
        questionGroupViewModel.id = input.getId();
        questionGroupViewModel.name = input.getName();
        questionGroupViewModel.disabled = input.isDisabled();
        questionGroupViewModel.headerText = input.getHeaderText();
        questionGroupViewModel.parameters = input.getParameters();

        questionGroupViewModel.questions = input.getQuestions().stream().map(questionGroupQuestion -> {
            var qn = questionToViewModel.apply(questionGroupQuestion.getQuestion());
            qn.orderby = questionGroupQuestion.getOrderby();
            return qn;
        }).collect(toList());

        return questionGroupViewModel;
    }
}