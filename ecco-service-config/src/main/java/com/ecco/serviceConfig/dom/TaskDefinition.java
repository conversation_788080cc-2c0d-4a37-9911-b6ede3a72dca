package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.jspecify.annotations.Nullable;

import javax.persistence.*;

/**
 * An aspect of the referral flow. This can be for example an accommodation, probation, interview, etc.
 * <p/>
 * The intention here is that this aspect can be a whole sub-flow. It's not entirely sure how that will be done yet, however, this class
 * exists here to capture the concept.
 */
@SuppressWarnings("unused")
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "taskdefinitions")
public class TaskDefinition extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;
    public static enum Type { DEPRECATED, DEDICATED_TASK, AGREEMENT, AUDITONLY, EVIDENCE_CHECKLIST,
                        EVIDENCE_CUSTOMFORM, EVIDENCE_RISK, EVIDENCE_ROTA, EVIDENCE_QUESTIONNAIRE,
                        EVIDENCE_SUPPORT }

    /** unique name of the task */
    private String name;

    @Enumerated(EnumType.STRING)
    private Type type;

    /** A description of what the task does */
    private String description;

    /**
     * friendly name of the task
     * Non-unique label for a referral aspect which defines the purpose (but not impl of the task) e.g. we can have
     * "from" which can be implemented as name="source" or name="sourceWithIndividual"
     */
    @Column(name = "name", insertable = false, updatable = false)
    private String friendlyName;

    /**
     * Whether this task gets displayed on screen - it might be a processing task only
     * including if this should be shown as a breadcrumb referral aspect
     */
    private boolean display;

    /** Whether this task shows on the referralOverview screen - it might just be a wizard item */
    private boolean displayOverview;

    /** This task is for internal use */
    private boolean internal;

    /**
     * An idea that this task is for external use (ie non-logged in user referral process)
     * NB this isn't used
     */
    @Column(name="isexternal") // external is a keyword in SQL Server
    boolean external;

    @Nullable
    @Lob
    @Column(nullable = false)
    private String metadata;

    // see ListDefinitionEntry
    public void setMetadata(String metadata) {

        // Enforce not empty. If we are empty we get SessionData issues since the
        // JSON produced cannot be parsed.
        // Alternatives like hibernate @NotEmpty or use of @Pattern annotations are possible
        // see https://stackoverflow.com/a/43716689
        if (metadata != null && !metadata.startsWith("{\"")) {
            throw new RuntimeException("TaskDefinition.metadata incorrect format");
        }

        this.metadata = metadata;
    }

    /** Referral aspect id */
    public static final long NEWMULTIPLE_REFERRAL = 78;


    public TaskDefinition(Long id) {
        super(id);
    }

    public TaskDefinition(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TaskDefinition)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        TaskDefinition that = (TaskDefinition) o;

        return name != null ? name.equals(that.name) : that.name == null;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (name != null ? name.hashCode() : 0);
        return result;
    }
}
