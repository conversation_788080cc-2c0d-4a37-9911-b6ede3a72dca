package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.dom.ConfigCommand;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Configuration command for questionnaire
 */
@Entity
@DiscriminatorValue("commenttype")
public class CommentTypeCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public CommentTypeCommand() {
        super();
    }

    public CommentTypeCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                              long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
