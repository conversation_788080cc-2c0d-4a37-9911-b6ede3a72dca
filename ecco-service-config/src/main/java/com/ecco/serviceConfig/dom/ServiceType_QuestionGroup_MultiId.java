package com.ecco.serviceConfig.dom;

import javax.persistence.*;
import java.io.Serializable;

@Embeddable
public class ServiceType_QuestionGroup_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    @OneToOne(fetch=FetchType.LAZY, optional=false)
    @JoinColumn(name="serviceTypeId")
    ServiceType serviceType;

    @OneToOne(fetch= FetchType.LAZY, optional=false)
    @JoinColumn(name="questiongroupId")
    QuestionGroup questionGroup;

    protected ServiceType_QuestionGroup_MultiId() {
    }

    public ServiceType_QuestionGroup_MultiId(ServiceType serviceType, QuestionGroup questionGroup) {
        this.serviceType = serviceType;
        this.questionGroup = questionGroup;
    }

    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    public QuestionGroup getQuestionGroup() {
        return questionGroup;
    }

    public void setQuestionGroup(QuestionGroup questionGroup) {
        this.questionGroup = questionGroup;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ServiceType_QuestionGroup_MultiId that = (ServiceType_QuestionGroup_MultiId) o;

        if (serviceType != null ? !serviceType.equals(that.serviceType) : that.serviceType != null) return false;
        return questionGroup != null ? questionGroup.equals(that.questionGroup) : that.questionGroup == null;

    }

    @Override
    public int hashCode() {
        int result = serviceType != null ? serviceType.hashCode() : 0;
        result = 31 * result + (questionGroup != null ? questionGroup.hashCode() : 0);
        return result;
    }
}
