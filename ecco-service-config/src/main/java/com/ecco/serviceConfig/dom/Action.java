package com.ecco.serviceConfig.dom;

import java.util.Set;
import java.util.UUID;

import org.jspecify.annotations.NonNull;
import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;

/**
 * Definition of a SMART step
 */
@Entity
@Table(name = "actions")
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Getter
@Setter
public class Action extends ActionAbstract {

    /** uuid key to allow remote commands to reference what they created */
    @NonNull
    @Column(name = "uuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID uuid;

    @ManyToMany(cascade={}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    @JoinTable(
            name = "gsat_linkedactions",
            joinColumns = @JoinColumn(name = "actionId"),
            inverseJoinColumns = @JoinColumn(name = "groupsupportactivitytypeId"))
    Set<GroupSupportActivityType> linkedActivities;

    private int orderby;

    private String statusChangeReasonListName;

    public Action() {}

    public Action(Long id) {
        setId(id);
    }

    public Action(String name, UUID uuid) {
        setName(name);
        this.uuid = uuid;
    }

}
