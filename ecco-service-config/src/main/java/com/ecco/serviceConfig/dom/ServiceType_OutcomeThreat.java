package com.ecco.serviceConfig.dom;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Access(AccessType.FIELD)
@Table(name="servicetypes_outcomethreats")
@AssociationOverrides({
        @AssociationOverride(name="multiId.serviceType", joinColumns = @JoinColumn(name="servicetypeId")),
        @AssociationOverride(name="multiId.outcome", joinColumns = @JoinColumn(name="threatoutcomeId"))
})
public class ServiceType_OutcomeThreat implements Serializable {

    private static final long serialVersionUID = 1L;

    protected ServiceType_OutcomeThreat() {
    }

    public ServiceType_OutcomeThreat(ServiceType_OutcomeThreat_MultiId multiId) {
        this.multiId = multiId;
    }

    @EmbeddedId
    private ServiceType_OutcomeThreat_MultiId multiId = new ServiceType_OutcomeThreat_MultiId();

    public ServiceType_OutcomeThreat_MultiId getMultiId() {
        return multiId;
    }

    public void setMultiId(ServiceType_OutcomeThreat_MultiId multiId) {
        this.multiId = multiId;
    }

    @Override
    public String toString() {
        return  "servicetypes_outcomethreats " + multiId.getServiceType() + ", " + multiId.getOutcome();
    }

}
