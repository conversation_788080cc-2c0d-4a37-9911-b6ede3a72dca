package com.ecco.serviceConfig.hact.viewModel;

public class HactSocialValueBankViewModel {

    public Integer id;

    /**
     * The HACT outcome code
     */
    public String hactOutcomeDefCode;

    /**
     * The HACT outcome category (eg employment, environment)
     */
    public String outcomeCategory;

    /**
     * The name/description of the outcome
     */
    public String outcomeName;

    /** The value with area: unknown, age: unknown */
    public int valueAnywhereUnknown;

    /** The value with area: unknown, age: <25 */
    public int valueAnywhere24;

    /** The value with area: unknown, age: <50 */
    public int valueAnywhere49;

    /** The value with area: unknown, age: 50+ */
    public int valueAnywhereHigher;

    /** The value with area: London, age: unknown */
    public int valueLondonUnknown;

    /** The value with area: London, age: <25 */
    public int valueLondon24;

    /** The value with area: London, age: <50 */
    public int valueLondon49;

    /** The value with area: London, age: 50+ */
    public int valueLondonHigher;

    /** The value with area: outside London, age: unknown */
    public int valueOutsideLondonUnknown;

    /** The value with area: outside London, age: <25 */
    public int valueOutsideLondon24;

    /** The value with area: outside London, age: <50 */
    public int valueOutsideLondon49;

    /** The value with area: outside London, age: 50+ */
    public int valueOutsideLondonHigher;

    /** The deadweight (would have happened anyway) */
    public int percentDropYear1;
    public int percentDropYear2;
    public int percentDropYear3;
    public int percentDropYear4;
    public int percentDropYear5;
    public int percentDropYear6;
    public int percentDropYear7;
}
