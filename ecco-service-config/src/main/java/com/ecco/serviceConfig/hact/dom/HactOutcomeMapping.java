package com.ecco.serviceConfig.hact.dom;

import org.jspecify.annotations.NonNull;
import javax.persistence.*;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import com.ecco.serviceConfig.dom.Action;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A mapping from smart steps to HACT outcomes
 */
@Entity
@Table(name = "hactoutcomemappings")
@Data
@NoArgsConstructor
public class HactOutcomeMapping extends AbstractIntKeyedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * The HACT outcome code
     */
    @NonNull
    @Column(nullable = false)
    private String hactOutcomeDefCode;

    @ManyToOne(fetch= FetchType.LAZY)
    @JoinColumn(name="actionDefId", insertable = false, updatable = false)
    private Action action;

    /**
     * The id of the Action which maps to the HACT outcome (can be more than one)
     */
    private Long actionDefId;

    /**
     * The id of the Action which maps to the HACT outcome (can be more than one)
     */
    private Long questionDefId;

}
