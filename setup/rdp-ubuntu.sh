
git clone https://github.com/FreeRDP/FreeRDP
cd FreeRDP
git tag -l
git checkout tags/1.1.0-beta1

apt-get install build-essential git-core cmake libssl-dev libx11-dev libxext-dev libxinerama-dev \
  libxcursor-dev libxdamage-dev libxv-dev libxkbfile-dev libasound2-dev libcups2-dev libxml2 libxml2-dev \
  libxrandr-dev libgstreamer0.10-dev libgstreamer-plugins-base0.10-dev libxi-dev libgstreamer-plugins-base1.0-dev

apt-get install libavutil-dev libavcodec-dev

cmake -DCMAKE_BUILD_TYPE=Debug -DWITH_SSE2=ON .

make

make install

# must run the script as sudo for this
echo "/usr/local/lib/freerdp" > /etc/ld.so.conf.d/freerdp.conf

ldconfig


which xfreerdp
