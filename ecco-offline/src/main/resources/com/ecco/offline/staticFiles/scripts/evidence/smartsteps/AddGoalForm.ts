import $ = require("jquery");
import BaseCommandEmittingForm = require("../../controls/BaseCommandEmittingForm");

import CheckboxList = require("../../controls/CheckboxList");
import InputGroup = require("../../controls/InputGroup");
import TextAreaInput = require("../../controls/TextAreaInput");
import TextInput = require("../../controls/TextInput");
import EditActionDefForm = require("../../service-config/EditActionDefForm");
import SmartStepsListControl = require("./SmartStepsListControl");
import Action = domain.Action;
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import * as commands from "ecco-commands";
import {CommentCommand} from "ecco-commands";
import {apiClient, getGlobalEccoAPI} from "ecco-components";
import * as domain from "ecco-dto";
import {EvidenceGroup, ServiceRecipientWithEntities, SmartStepStatus} from "ecco-dto";
import * as dto from "ecco-dto/service-config-dto";
import {EvidenceDef} from "ecco-evidence";
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";

var activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(apiClient);


class AddGoalForm extends BaseCommandEmittingForm {

    private goalNameInput = new TextInput("goalName");
    private goalPlanInput = new TextAreaInput("goalPlan");
    private workCommentInput = new TextAreaInput("workCommentInput");
    private actionsList: SmartStepsListControl;
    private selectedAction: Action;
    private showActivityInterest: boolean;
    private $activitySubForm = $("<div>");
    private workUuid = Uuid.randomV4();
    private messages = getGlobalEccoAPI().sessionData.getMessages()

    /** We provide the form with the referralId and and ServiceType, and we get back a CommandQueue
     *  containing the changes which have happened.
     */
    constructor(private serviceRecipient: ServiceRecipientWithEntities, private evidenceDef: EvidenceDef) {
        super("Add " + (getGlobalEccoAPI().sessionData.getMessages())["terminology.smartstep"]);
        this.disableSubmit();
        this.actionsList = new SmartStepsListControl(serviceRecipient, evidenceDef,
            (action: Action) => { this.onActionChosen(action); },
            () => {
                this.selectedAction = null;
                this.disableSubmit();
             }
        );
        this.actionsList.load();

        this.form.append( this.actionsList);
        this.form.append(new InputGroup("goal", this.goalNameInput));
        this.form.append(new InputGroup("plan", this.goalPlanInput));

        this.showActivityInterest
            = serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(evidenceDef.getTaskName(),
                "showActionComponents", "activityInterest");

        if (this.showActivityInterest) {
            this.form.append(this.$activitySubForm);
        }
        this.form.append(new InputGroup("say something about this change (optional)", this.workCommentInput));
    }

    private onActionChosen(action: Action) {
        this.selectedAction = action;
        // Show (hopefully) only the one we clicked
        this.actionsList.select(action);

        if (this.showActivityInterest) {

            // Create control and kick off request to populate it with associatedActivities
            const activitiesControl = new CheckboxList((id, checked) => this.activityChange(id, checked));
            activitiesControl.element().addClass("input-group");

            activityTypeRepository.findActivityInterestsByServiceRecipientId(this.serviceRecipient.serviceRecipientId)
                .then(actTypeInterests => {
                    let actionDef = this.serviceRecipient.configResolver.getServiceType().getActionById(action.getId());
                    if (actionDef) {
                        let availableActivities = action.activityTypes;
                        activitiesControl.populateFromList(availableActivities.sort((a, b) => a.name.localeCompare(b.name)),
                            (activity: dto.ActivityType) => ({key: activity.id.toString(), value: activity.name}),
                            actTypeInterests.map(activity => activity.id.toString()));
                    }
                });

            const svcId = this.serviceRecipient.features.getServiceCategorisation(this.serviceRecipient.serviceAllocationId).serviceId;
            this.$activitySubForm
                .empty()
                .addClass("alert alert-info")
                .append(
                    $("<h4>")
                        .text("Activities for this " + this.messages["terminology.smartstep"])
                        .click(() => EditActionDefForm.showInModal(action, svcId)))
                .append(
                    $("<p>")
                        .text("The following activities are relevant to this goal. Please select "
                        + "any that you are interested in attending (some may already be selected in "
                        + "relation to other goals)\n"))
                .append(activitiesControl.element());
        }
        super.enableSubmit();
    }

    /** Add an activity change against the appropriate/original referral - parent possibly */
    private activityChange(activityTypeId: string, checked: boolean) {
        var cmd = new commands.ActivityInterestChangeCommand(checked ? "add" : "remove",
            this.serviceRecipient.serviceRecipientId,
            parseInt(activityTypeId));
        this.queueCommand(cmd);
    }


    protected override submitForm() {
        const actionInstanceUuid = Uuid.randomV4();
        const uuid = Uuid.randomV4();

        const addGoalCmd = new commands.GoalUpdateCommand("add", uuid, this.workUuid, this.serviceRecipient.serviceRecipientId,
                    this.evidenceDef.getTaskName(), this.selectedAction.getId(), this.evidenceDef.getEvidenceGroup(),
                    actionInstanceUuid, null)
                    .changeStatus(null, SmartStepStatus.WantToAchieve)
                    .changeGoalName(null, this.goalNameInput.val())
                    .changeGoalPlan(null, this.goalPlanInput.val());
        this.queueCommand(addGoalCmd);

        if (this.workCommentInput.val()) {
            const cmd = CommentCommand.create(false, this.workUuid, this.serviceRecipient.serviceRecipientId,
                        EvidenceGroup.needs, this.evidenceDef.getTaskName())
                    .changeComment(null, this.workCommentInput.val())
                    .changeWorkDate(null, EccoDateTime.nowLocalTime().formatIso8601())
                    .build();
            this.queueCommand(cmd);
        }
        return super.submitForm();
    }
}

export = AddGoalForm;
