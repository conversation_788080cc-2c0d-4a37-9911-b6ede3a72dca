import $ = require("jquery");
import {EventBus, ResizeEvent} from "@eccosolutions/ecco-common";
import * as React from "react";
import {useEffect, useRef} from "react";
import {MenuUpdateEvent} from "../common/events";

// for React migration
// registers MenuUpdateEvent handler which replaces the menu via MenuUpdateEvent.bus.fire
// which replaces text at the top of older forms
export const EventBusDisplayContainer = (props: { bus: EventBus<MenuUpdateEvent> }) => {
    const menuBar = useRef<HTMLDivElement>();

    useEffect(() => {
            const handler = event => {
                $(menuBar.current).empty().append(event.content); // FIXME: Remove $ I think we can do this with innerHTML=""
                setTimeout(ResizeEvent.bus.fire, 30);
                // ResizeEvent.bus.fire(new ResizeEvent());
            };

            props.bus.addHandler(handler);
            return () => {
                props.bus.removeHandler(handler);
            };
        },
        [] // Empty deps as we don't need to add/remove on updates
    );

    return <div ref={menuBar}/>;
};