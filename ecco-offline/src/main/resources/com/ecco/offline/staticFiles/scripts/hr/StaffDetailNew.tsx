import _ = require("lodash");
import moment = require("moment");
import FormEvent = React.FormEvent;
import * as React from "react"
import {ReactNode} from "react"
import {
    AddressLocation,
    apiClient,
    update,
    UpdateSpec
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {IdNameDisabled, PartialTo} from "@eccosolutions/ecco-common";
import {
    DISABILITY_LISTNAME,
    ETHNICORIGIN_LISTNAME,
    GENDER_LISTNAME,
    LANGUAGE_LISTNAME,
    listDefToIdName,
    MARITALSTATUS_LISTNAME,
    NATIONALITY_LISTNAME,
    RELIGION_LISTNAME,
    SessionData,
    SEXUALORIENTATION_LISTNAME,
    StaffDto,
    WorkersAjaxRepository
} from "ecco-dto";
import {Alert, Col, ControlLabel, FormControl, FormGroup, Row} from "react-bootstrap";
import {dropdownList, FieldGroup} from "../components/ComponentUtils";
import {default as DateInput} from "../components/DateInput";

type StringChangeEvent = FormEvent<any>;

/**
 * NON-command-based CREATING staff
 */

interface Props extends React.ClassAttributes<StaffDetailNew> {
    sessionData: SessionData;
    staff?: StaffDto | undefined;
    show?: boolean | undefined;
    showAsModal?: boolean | undefined;
    onSave?: ((staff: StaffDto) => void) | undefined;
    onCancel?: (() => void) | undefined;
}

type AlertState = { [key:string]: string};

type StaffField = keyof StaffDto;

interface State {
    staff: StaffDto;
    languageIndex: Record<string, IdNameDisabled>;
    religionIndex: Record<string, IdNameDisabled>;
    ethnicOriginIndex: Record<string, IdNameDisabled>;
    genderIndex: Record<string, IdNameDisabled>;
    disabilityIndex: Record<string, IdNameDisabled>;
    sexualOrientationIndex: Record<string, IdNameDisabled>;
    nationalityIndex: Record<string, IdNameDisabled>;
    maritalStatusIndex: Record<string, IdNameDisabled>;
    errors: {[P in keyof StaffDto]?: string};
    requiredFields: StaffField[];
    optionalFields: StaffField[];
    alerts: AlertState;
    editingAddress: boolean // if there is some address provided, then we must be editing
}

class StaffDetailNew extends React.Component<Props, State> {
    private saveTimer: number; // setTimeout/clearTimeout handle
    private repository = new WorkersAjaxRepository(apiClient);

    private addressLocation: AddressLocation | null = null;

    private optionalFieldCounter;
    private rowObjStart = {
        labelClassName: 'col-xs-2 hidden-md hidden-lg',
        wrapperClassName: "col-xs-10 col-md-offset-2 col-md-5",
        clearClassName: "clearfix visible-sm visible-xs"
    };

    private rowObjEnd = {
        labelClassName: 'col-xs-2 hidden-md hidden-lg',
        wrapperClassName: "col-xs-10 col-md-5",
        clearClassName: "clearfix"
    };

    constructor(props) {
        super(props);

        const staff = this.props.staff || StaffDetailNew.getNewStaff();
        this.state = {
            staff,
            languageIndex: {},
            religionIndex: {},
            ethnicOriginIndex: {},
            nationalityIndex: {},
            maritalStatusIndex: {},
            genderIndex: {},
            disabilityIndex: {},
            sexualOrientationIndex: {},
            errors: {},
            requiredFields: [],
            optionalFields: [],
            alerts: {},
            editingAddress: staff.addressedLocationId != null || staff.address != null // if there is some address provided, then we must be editing
        };
        // Validate the staff we got provided.
        // Won't be correct on basis of required fields until after session data loaded
        (this.state as State).errors = this.validate(staff); // cannot and should not use setState here
    }

    public override componentDidMount() {
        const {sessionData} = this.props;
        const requiredFields = sessionData.getSettingAsArray('com.ecco.forms:CLIENT_DETAIL_REQUIRED_FIELDS') as StaffField[];
        const optionalFields = sessionData.getSettingAsArray('com.ecco.forms:CLIENT_DETAIL_OPTIONAL_FIELDS') as StaffField[];
        this.setState({
            languageIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(LANGUAGE_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            religionIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(RELIGION_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            nationalityIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(NATIONALITY_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            ethnicOriginIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(ETHNICORIGIN_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            maritalStatusIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(MARITALSTATUS_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            genderIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(GENDER_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            disabilityIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(DISABILITY_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            sexualOrientationIndex: _.keyBy(sessionData.getListDefinitionEntriesByListName(SEXUALORIENTATION_LISTNAME)
                .map(ld => listDefToIdName(ld)), (item) => item.id),
            requiredFields,
            optionalFields,
            errors: this.validate(this.state.staff)
        });
    }

    public override componentWillUnmount() {
        window.clearTimeout(this.saveTimer);
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        if (this.props.staff != nextProps.staff) {
            const staff = nextProps.staff || StaffDetailNew.getNewStaff();
            const errors = this.validate(staff);

            this.setState({
                staff: staff,
                errors: errors,
                alerts: {},
                editingAddress: staff.addressedLocationId != null || staff.address != null // if there is some address provided, then we must be editing
            });
        }
    }

    private static getNewStaff(): StaffDto {
        return {} as StaffDto;
    }

    // FIXME: This function cannot be made typesafe.
    private handleChange(updater: (value: any) => UpdateSpec<StaffDto>, event) {
        const staff = update(this.state.staff, updater(event.target.value));
        const errors = this.validate(staff);

        this.setState({
            staff: staff,
            errors: errors
        });
    }

    private handleFirstNameChange: (event: StringChangeEvent) => void
        = this.handleChange.bind(this, v => ({firstName: {$set: v}}));

    private handleLastNameChange: (event: StringChangeEvent) => void
        = this.handleChange.bind(this, v => ({lastName: {$set: v}}));


    private handleAddressChange = (addressId: number) => {
        const staff = update(this.state.staff, {addressedLocationId: {$set: addressId}});
        const errors = this.validate(staff);

        this.setState({
            staff: staff,
            errors: errors
        });
    };

    private handleAddressValidChange = (valid: boolean) => {
        const errors = this.validate(this.state.staff); // create errors object
        if (!valid) {
            errors.address = 'required';
        }
        this.setState({
            errors: errors
        });
    };

    private handleBirthDayChange = (date: Date) => {
        const staff = update(this.state.staff, {birthDate: {$set: date && moment.utc(date).format('YYYY-MM-DD')}});
        const errors = this.validate(staff);

        this.setState({
            staff: staff,
            errors: errors
        });
    };

    private handlePropertyChange = (event: StringChangeEvent, update: (v: string) => StaffDto) => {
        const v: string = (event.target as HTMLInputElement).value;
        const staff = update(v == "" ? undefined : v); // don't want null -> "" changes or attempt to set something as empty string
        const errors = this.validate(staff);
        this.setState({
            staff: staff,
            errors: errors
        });
    };

    private handleToggleClick = () => {
        // copy postcode between fields when toggling
        // FIXME: should call a setter that sets state or actually do via props...
        // if (this.state.editingAddress) {
        //     this.addressList.state.postCode = this.addressDetail.state.address.postcode;
        // } else {
        //     this.addressDetail.state.address.postcode = this.addressList.state.postCode;
        // }

        const staff = update(this.state.staff, {addressedLocationId: {$set: null}});
        const errors = this.validate(staff);

        this.setState({
            staff: staff,
            errors: errors,
            editingAddress: !this.state.editingAddress
        });
    };

    private handleSaveClick = () => {
        this.repository.saveStaff(this.getStaffWithUpdatedAddress())
            .then((response) => {
            this.saveTimer = window.setTimeout(() => this.handleSaveTimeout(), 1500);

            this.setState({
                staff: update(this.state.staff, {workerId: {$set: parseInt(response.id)}}),
                alerts: {info: 'individual saved'}
            });
        }).catch(reason => {
            this.setState({alerts: {danger: 'failed to save staff: '
                + (reason.reason && reason.reason.message ? reason.reason.message : reason.toString()) }});
        });
    };

    private handleSaveTimeout() {
        this.props.onSave && this.props.onSave(this.state.staff);
        const staff = StaffDetailNew.getNewStaff();

        this.setState({
            staff: staff,
            errors: this.validate(staff),
            alerts: {},
            editingAddress: false
        });
    }

    private handleCancelClick = () => {
        this.props.onCancel && this.props.onCancel();

        const staff = StaffDetailNew.getNewStaff();
        this.setState({
            staff: staff,
            errors: this.validate(staff),
            alerts: {},
            editingAddress: false
        });
    };

    private getStaffWithUpdatedAddress(): StaffDto {
        if (this.addressLocation.hasAddressChange()) {
            // TODO: update via a callback to setState
            this.state.staff.addressedLocationId = this.addressLocation.getAddressLocationId();
        }
        return this.state.staff;
    }

    private validate(staff: StaffDto) {
        const errors: PartialTo<StaffDto, string> = {};
        if (!staff.firstName) errors.firstName = 'required';
        if (!staff.lastName) errors.lastName = 'required';
        if (this.isRequired('birthDate') && !staff.birthDate) errors.birthDate = 'required';

        if (this.isRequired('address') && !staff.addressedLocationId) errors.addressedLocationId = 'required';

        this.state.requiredFields.map(opt => {
            if (this.isRequired(opt) && !staff[opt]) {
                errors[opt] = 'required';
            }
        });

        return errors;
    }

    private validationState(field: keyof StaffDto) {
        return this.isRequired(field)
            ? this.state.errors[field] ? 'success' : 'error'
            : undefined;
    }

    private isRequired(fieldName: keyof StaffDto) {
        return this.state.requiredFields.indexOf(fieldName) >= 0;
    }

    public isValid(): boolean {
        return Object.keys(this.state.errors).length == 0;
    }

    private staffStateSetter = (staff: StaffDto) => {
        const errors = this.validate(staff);
        this.setState({errors, staff});
    };

    override render() {
        // This gets modified ongoingly during render so need to reset each render.
        this.optionalFieldCounter = 0;

        /* NOTE: the order of this in the JSX is important.
         * See https://facebook.github.io/react/docs/transferring-props.html */
        const AlertElement = Object.entries(this.state.alerts)
            .map(([style, message]) => (
            <Alert bsStyle={style.toString()}>{message}</Alert>
        ));

        const alerts = document.querySelectorAll("div.alert");
        alerts.length > 0 && alerts[0].scrollIntoView();

        const {sessionData} = this.props;
        const {staff, errors} = this.state;

        // TODO: see worker.jsp and clientDetail.jspf optionalFields
        // // text: date: textbox: mothersFirstName, paris, militaryNumber, keyCode, externalSystemRef
        const CreateStaffs: ReactNode = (
                <div>
                {AlertElement}
                <form className='form-responsive'>
                    <FormGroup
                        validationState={(staff.firstName != undefined && errors.firstName)
                    || (staff.lastName != undefined && errors.lastName) ? 'error': 'success'}>
                        <ControlLabel bsClass='col-xs-2'>name</ControlLabel>
                        <Col xs={10}>
                            <Row>
                                <Col xs={6}>
                                    <FormControl
                                        type="text"
                                        className="form-control"
                                        value={staff.firstName || ''}
                                        placeholder='first name'
                                        autoComplete='off'
                                        onChange={this.handleFirstNameChange} />
                                </Col>
                                <Col xs={6}>
                                    <FormControl
                                        type="text"
                                        className="form-control"
                                        placeholder='last name'
                                        autoComplete='off'
                                        value={staff.lastName || ''}
                                        onChange={this.handleLastNameChange} />
                                </Col>
                            </Row>
                        </Col>
                    </FormGroup>
                    <div className="clearfix"/>
                    <DateInput
                        name='birthDate'
                        label='birth date'
                        value={staff.birthDate != null ? new Date(staff.birthDate) : undefined}
                        onChange={this.handleBirthDayChange}
                        required={this.state.requiredFields.indexOf('birthDate') >= 0}
                    />
                    <div className="clearfix visible-sm visible-xs"/>

                    {dropdownList("gender", this.staffStateSetter, this.state.staff, "genderId",
                        sessionData && sessionData.getGenderList(),
                        {labelClassName: 'col-xs-2 hidden-md hidden-lg', wrapperClassName: 'col-xs-10 col-md-3'},
                        "gender", false, this.isRequired('genderId'))}
                    <div className="clearfix"/>

                    <div>
                        <FieldGroup
                            type='text'
                            label='phone number'
                            labelClassName={this.rowObjStart.labelClassName}
                            wrapperClassName={this.rowObjStart.wrapperClassName}
                            placeholder='phone number'
                            value={staff.phoneNumber}
                            onChange={(event: StringChangeEvent) => this.handlePropertyChange(event, (v) => update(this.state.staff, {phoneNumber: {$set: v}}))}
                            validationState={this.validationState('phoneNumber')}/>
                        <div className="clearfix visible-sm visible-xs"/>
                    </div>

                    <div>
                        <FieldGroup
                            type='text'
                            label='mobile number'
                            labelClassName={this.rowObjEnd.labelClassName}
                            wrapperClassName={this.rowObjEnd.wrapperClassName}
                            placeholder='mobile number'
                            value={staff.mobileNumber}
                            onChange={(event: StringChangeEvent) => this.handlePropertyChange(event, (v) => update(this.state.staff, {mobileNumber: {$set: v}}))}
                            validationState={this.validationState('mobileNumber')}/>
                        <div className="clearfix"/>
                    </div>

                    <div>
                        <FieldGroup
                            type='text'
                            label='email'
                            labelClassName={this.rowObjStart.labelClassName}
                            wrapperClassName={this.rowObjStart.wrapperClassName}
                            placeholder='email'
                            value={staff.email}
                            onChange={(event: StringChangeEvent) => this.handlePropertyChange(event, (v) => update(this.state.staff, {email: {$set: v}}))}
                            validationState={this.validationState('email')}/>
                        <div className="clearfix visible-sm visible-xs"/>
                    </div>

                    <div className="clearfix"/>
                    {this.getOptionalField("firstLanguageId")}
                    {this.getOptionalField("ethnicOriginId")}
                    {this.getOptionalField("nationalityId")}
                    {this.getOptionalField("genderAtBirthId")}
                    {this.getOptionalField("maritalStatusId")}
                    {this.getOptionalField("religionId")}
                    {this.getOptionalField("disabilityId")}
                    {this.getOptionalField("sexualOrientationId")}
                    {this.getOptionalField("ni")}
                    {this.getOptionalField("housingBenefit")}
                    {this.getOptionalField("nhs")}
                </form>

                <div className='row'>
                    <div className='col-xs-10 col-xs-offset-2'>
                    <div className="page-header">
                    <AddressLocation
                        displayAddress={staff.address}
                        showBuildings={false}
                        ref={c => this.addressLocation = c}
                        handleAddressValidChange={this.handleAddressValidChange}
                    />

                    </div>
                    </div>
                </div>
                </div>
            );

        return possiblyModalForm("staff details", this.props.showAsModal || false, this.props.show || false,
            this.handleCancelClick, this.handleSaveClick, !this.isValid(), false, CreateStaffs);
    }

    private getOptionalField(fieldName: string) {
        const {sessionData} = this.props;
        const {staff, optionalFields} = this.state;

        let rowObj = this.rowObjEnd;
        switch (fieldName) {
            case "firstLanguageId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("first language", this.staffStateSetter, staff, "firstLanguageId",
                            sessionData && sessionData.getLanguageList(),
                            rowObj, "first language", false, this.isRequired('firstLanguageId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "ethnicOriginId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("ethnic origin", this.staffStateSetter, staff, "ethnicOriginId",
                            sessionData && sessionData.getEthnicOriginList(),
                            rowObj, "ethnic origin", false, this.isRequired('ethnicOriginId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "nationalityId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("nationality", this.staffStateSetter, staff, "nationalityId",
                            sessionData && sessionData.getNationalityList(),
                            rowObj, "nationality", false, this.isRequired('nationalityId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "maritalStatusId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("marital status", this.staffStateSetter, staff, "maritalStatusId",
                            sessionData && sessionData.getMaritalStatusList(),
                            rowObj, "marital status", false, this.isRequired('maritalStatusId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "religionId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("religion", this.staffStateSetter, staff, "religionId",
                            sessionData && sessionData.getReligionList(),
                            rowObj, "religion", false, this.isRequired('religionId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "disabilityId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("disability", this.staffStateSetter, staff, "disabilityId",
                            sessionData && sessionData.getDisabilityList(),
                            rowObj, "disability", false, this.isRequired('disabilityId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "sexualOrientationId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("sexual orientation", this.staffStateSetter, staff, "sexualOrientationId",
                            sessionData && sessionData.getSexualOrientationList(),
                            rowObj, "sexual orientation", false, this.isRequired('sexualOrientationId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "genderAtBirthId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("gender at birth", this.staffStateSetter, staff, "genderAtBirthId",
                            sessionData && sessionData.getGenderAtBirthList(),
                            rowObj, "gender at birth", false, this.isRequired('genderAtBirthId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;
            default:
                return null;
        }

    }

}

export default StaffDetailNew;
