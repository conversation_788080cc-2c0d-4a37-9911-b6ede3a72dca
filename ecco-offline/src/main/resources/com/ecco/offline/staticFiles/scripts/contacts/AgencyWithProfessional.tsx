import IndividualDetailModal = require("./components/IndividualDetailModal");
import * as React from "react"
import {
    useServicesContext,
    AgencyWithProfessionalsSelector,
    convertContactToEntities,
    useCounter,
    useAgenciesWithProfessionals, LoadingSpinner
} from "ecco-components";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";
import {FC, useState} from "react";
import AgencyDetailModal from "./components/AgencyDetailModal";
import {Button} from "react-bootstrap";
import {Agency, Individual} from "ecco-dto";
import {Grid} from "@eccosolutions/ecco-mui";

interface Props {
    serviceRecipientId: number; // triggers associatedTypeId functionality, or provides the context for agency lookup (the service allocation from the servicerecipientid)
    agencyId?: number | undefined;
    professionalId?: number | undefined;
    onChange: (agency: Agency | null, professional: Individual | null) => void;
}

export const AgencyWithProfessional: FC<Props> = props => {
    const {sessionData} = useServicesContext();
    const [agencyId, setAgencyId] = useState<number | null>(props.agencyId);
    const [professionalId, setProfessionalId] = useState<number | null>(props.professionalId);
    const [openEdit, setOpenEdit] = useState<'agency' | 'professional' | null>(null);
    const [edit, setEdit] = useState<number | null>(null);
    const [a, incA] = useCounter();
    const [p, incP] = useCounter();

    const {agenciesWithProfessionals, loading} = useAgenciesWithProfessionals(a, p, props.serviceRecipientId, professionalId || agencyId);

    if (loading) {
        return <LoadingSpinner/>
    }

    const {agency, professional} = convertContactToEntities(professionalId || agencyId, agenciesWithProfessionals);

    return (<>
        <Grid container>
            <Grid item xs={10}>
                <AgencyWithProfessionalsSelector
                    labelName={"search for a contact"}
                    onChange={(agency, professional) => {
                        setAgencyId(agency?.contactId);
                        setProfessionalId(professional?.contactId);
                        props.onChange(agency, professional);
                    }}
                    selectedId={professionalId || agencyId}
                    agenciesWithProfessionals={agenciesWithProfessionals}
                />
            </Grid>
            <Grid item xs={12}>
                {!agency &&
                    <span>or create a
                        <a
                            className="btn btn-link"
                            onClick={() => {
                                setEdit(null);
                                setOpenEdit('agency');
                            }}>
                            new agency
                        </a>
                    </span>
                }
            </Grid>
            <Grid item xs={10} container justify={"flex-end"}>
                {agency &&
                        <>
                            <Button onClick={() => {
                                setEdit(agency.contactId);
                                setOpenEdit('agency');
                            }}>
                                edit agency
                            </Button>
                        </>
                }
                {professional &&
                        <>
                            <Button onClick={() => {
                                setEdit(professional.contactId);
                                setOpenEdit('professional');
                            }}>
                                edit professional
                            </Button>
                        </>
                }
            </Grid>
        </Grid>
        <Grid item xs={12} style={{marginTop: "25px", marginBottom: "50px"}}>
            {agency && <>
                <span>or create a
                    <a
                        className="btn btn-link"
                        onClick={() => {
                            setEdit(null);
                            setOpenEdit('professional');
                        }}>
                        new professional
                    </a> in {agency.companyName}
                </span>
            </>}
        </Grid>
        <ServicesContextProvider>
            {openEdit == 'agency'
                && <AgencyDetailModal
                        contextId={props.serviceRecipientId}
                        agencyId={edit}
                        show={true}
                        onSave={agency => {
                            setOpenEdit(null);
                            // if new (edit has no id) then set ids
                            if (edit == null) {
                                setAgencyId(agency.contactId);
                                setProfessionalId(null);
                                props.onChange(agency, null);
                            }
                            incA();
                        }}
                        onCancel={() => setOpenEdit(null)}/>
            }
            {openEdit == 'professional' && agency
                && <IndividualDetailModal
                        sessionData={sessionData}
                        organisationId={agency.contactId}
                        individualId={edit}
                        serviceRecipientId={props.serviceRecipientId}
                        show={true}
                        onSave={professional => {
                            setOpenEdit(null);
                            // if new (edit has no id) then set ids
                            if (edit == null) {
                                setProfessionalId(professional.contactId);
                                props.onChange(agency, professional);
                            }
                            incP();
                        }}
                        onCancel={() => setOpenEdit(null)}/>
            }
        </ServicesContextProvider>
    </>)
}
