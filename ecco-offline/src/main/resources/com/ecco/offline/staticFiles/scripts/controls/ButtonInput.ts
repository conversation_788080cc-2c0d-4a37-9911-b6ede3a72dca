import $ = require("jquery");
import BaseControl = require("./BaseControl");

/** A text input with an attached action button. */
class ButtonInput extends BaseControl {
    private $inputField: $.JQuery;

    /**
     * inputType = text | textarea
     */
    constructor(buttonText: string, inputType: string, onClick: (source: ButtonInput) => void,
        buttonCss?: string) {
        var $button = $("<button>").addClass(buttonCss).text(buttonText);

        var $container = $("<div>");

        var $inputField: $.JQuery;

        if (inputType == "text") {
            $inputField = $("<input>").attr("type", inputType);
            $container
                    .append($inputField)
                    .append($button);
        } else if (inputType == "textarea") {
            $inputField = $("<textarea>");
            $container
                    .append($button)
                    .append($inputField);
        } else {
            throw new Error(inputType + " is not a valid input type for ButtonInput");
        }

        $button.click(() => {
            onClick(this);
        });

        super($container);

        this.$inputField = $inputField;
    }

    public getInputValue(): string {
        return this.$inputField.val();
    }
}

export = ButtonInput
