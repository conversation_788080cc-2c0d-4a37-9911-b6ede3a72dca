
import * as React from "react";
import {MenuItem, TextField} from "@eccosolutions/ecco-mui";
import {extractEnumEntries, PropertySchemaDto, UnionTypeSchemaDto, ValueTypeSchemaDto} from "ecco-dto";

interface FormControlEventTarget extends EventTarget {
    value: string;
}

function renderMenuItems(propertySchema: UnionTypeSchemaDto | ValueTypeSchemaDto) {
    if (!propertySchema) {
        console.error("propertySchema cannot be null");
        return <MenuItem>missing property schema - see console log</MenuItem>;
    }
    return extractEnumEntries(propertySchema)
        .map(entry => <MenuItem key={entry.key} value={entry.key}>{entry.title}</MenuItem>);
}


interface State {
    dto: object;
    errors: object;
}

export class MUISchemaForm<P, S extends State> extends React.Component<P, S> {

    protected hasErrors(): boolean {
        for (const error in this.state.errors) {
            // noinspection JSUnfilteredForInLoop - we created a simple object
            if (this.state.errors[error]) {
                return true;
            }
        }
        return false;
    }

    protected datePicker(properties: { [key: string]: PropertySchemaDto }, key: string) {
        const schema = properties[key];
        const isoDate = this.state.dto[key];
        return schema && <TextField
            type="date"
            label={schema.title}
            value={isoDate}
            onChange={ev => {
                const date = ev.target.value;
                this.setState(state => {
                    state.dto[key] = date;
                    return state;
                });
            }}
        />;
    }

    private updateField(schema: PropertySchemaDto, key: string, value) {
        const error = schema.required && !value && "required";
        this.setState(state => {
            state.dto[key] = value;
            state.errors[key] = error;
            return state;
        });
    }

    private validateField(schema: PropertySchemaDto, key: string) {
        this.setState(state => {
            state.errors[key] = schema.required && !state.dto[key] && "required";
            return state;
        });
    }

    protected selectField(properties: { [key: string]: PropertySchemaDto }, key: string, disabled = false) {
        const schema = properties[key];
        if (!schema) {
            return null;
        }
        this.state.errors[key] === undefined && this.validateField(schema, key); // Validate on initial render - will be null afterwards
        return (
            <TextField
                disabled={disabled}
                key={key}
                select={true}
                fullWidth
                required={schema.required}
                label={schema.title}
                value={this.state.dto[key] || ''}
                helperText={this.state.errors[key]}
                onChange={ev => this.updateField(schema, key, ev.target.value)}
                onBlur={() => this.validateField(schema, key)}
            >
                {renderMenuItems(schema)}
            </TextField>);
    }

    protected textField(properties: { [key: string]: PropertySchemaDto }, key: string) {
        const schema = properties[key];
        return schema && <TextField
            label={schema.title}
            value={this.state.dto[key]}
            placeholder={schema.description}
            helperText={this.state.errors[key]}
            onChange={ev => this.updateField(schema, key, (ev.target.value))}
            onBlur={() => this.validateField(schema, key)}
        />;
    }

    protected textBox(properties: {[key: string] : PropertySchemaDto}, key: string) {
        const schema = properties[key];
        return schema && <TextField
            label={schema.title}
            multiline={true}
            rows={1}
            rowsMax={20}
            value={this.state.dto[key]}
            onChange={ev => this.updateField(schema, key, (ev.target.value))}
            onBlur={() => this.validateField(schema, key)}
        />;
    }
}
