import * as React from "react"
import {apiClient} from "ecco-components";
import {ProjectDto, ServiceDto, SessionData, SessionDataAjaxRepository, SessionDataRepository} from "ecco-dto";
import {EntityRestrictionsAjaxRepository} from "../../entity-restrictions/EntityRestrictionsAjaxRepository";
import {Grid, MenuItem, TextField} from '@eccosolutions/ecco-mui';

interface Props {
    serviceId: number;
    projectId: number;
    onServiceChange: (service: ServiceDto) => void;
    onProjectChange: (project: ProjectDto) => void;
}

interface State {
    restrictedServices: ServiceDto[];
}

// shown in ChartCriteriaForm
class ReferralServiceProject extends React.Component<Props, State> {

    private entityRestrictionsRepository: EntityRestrictionsAjaxRepository = new EntityRestrictionsAjaxRepository(apiClient);
    private sessionDataRepository: SessionDataRepository = new SessionDataAjaxRepository(apiClient);

    private sessionData: SessionData;

    constructor(props) {
        super(props);
        this.state = {
            restrictedServices: null
        };
    }

    public override componentDidMount() {
        this.sessionDataRepository.getSessionData().then(sessionData => {
            this.sessionData = sessionData;
            this.entityRestrictionsRepository.findRestrictedServicesProjects().then(services => {
                // for chart criteria, we never want the disabled ones
                // NB if a report is hardcoded with serviceId, it is not shown in the dropdown
                const svc = services.filter(s => !s.disabled);
                this.setState({
                    restrictedServices: svc
                })
            });
        })
    }

    private handleServiceInputChange = event => {
        this.props.onServiceChange(this.sessionData.getService(event.target.value));
    };

    private handleProjectInputChange = event => {
        this.props.onProjectChange(this.sessionData.getProject(event.target.value));
    };

    override render() {
        const inputProps = {};

        const ServiceOptions = this.state.restrictedServices?.map(service => (
            <MenuItem
                value={service.id.toString()}
                key={service.id}>
                {service.name}
            </MenuItem>
        ));

        const projectsWithinSelectedService = this.state.restrictedServices && this.props.serviceId
                ? this.state.restrictedServices.find(s => s.id == this.props.serviceId)?.projects || []
                : [];
        const ProjectOptions = projectsWithinSelectedService
                .filter(p => p.id > 0) // for some reason we're getting an entirely blank project, so avoid 'access all projects' etc
                .map(project => (
            <MenuItem
                value={project.id.toString()}
                key={project.id}>
                {project.name}
            </MenuItem>
        ));

        return (
            <Grid container direction="row">
                <Grid item xs={6}>
                    <TextField
                        select
                        label='service'
                        value={String(this.props.serviceId)}
                        onChange={this.handleServiceInputChange}
                        {...inputProps}>
                        <MenuItem key="null" value="">-</MenuItem>
                        {ServiceOptions}
                    </TextField>
                </Grid>
                <Grid item xs={6}>
                    <TextField
                        select
                        label='project'
                        value={String(this.props.projectId)}
                        onChange={this.handleProjectInputChange}
                        disabled={!this.props.serviceId}
                        {...inputProps}>
                        <MenuItem key="null" value="">-</MenuItem>
                        {ProjectOptions}
                    </TextField>
                </Grid>
            </Grid>
        );
    }
}

export = ReferralServiceProject;