import $ = require("jquery");

import ActionButton = require("../controls/ActionButton");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import {SessionDataService} from "../feature-config/SessionDataService";
import commands = require("./commands");
import dto = require("ecco-dto");
import SingleValueHistoryData = require("./SingleValueHistoryData");
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {CommandQueue} from "ecco-commands";
import {apiClient} from "ecco-components";
import {SessionData} from "ecco-dto";
import {setTextAsyncValue, summariseListDef, summariseResidence} from "../service-recipients/dataRenderers";
import {getCommandQueueRepository} from "ecco-offline-data";
import {SingleValueHistoryAjaxRepository} from "ecco-dto";

// ** ONLINE ONLY currently
const singleValueHistoryRepository = new SingleValueHistoryAjaxRepository(apiClient);
const sessionDataQ = SessionDataService.getFeatures();

// ** ONLINE ONLY currently

class SingleValueHistoryControl extends BaseAsyncDataControl<SingleValueHistoryData> {

    private deletedItemIds: number[] = [];

    constructor(private serviceRecipientId: number,
            private key: string,
            private valueIsListDef = true,
            private viewDataQ?: Promise<SingleValueHistoryData>) {
        super();
        this.load();
    }

    protected fetchViewData(): Promise<SingleValueHistoryData> {
        // optional argument
        if (!this.viewDataQ) {
            this.viewDataQ = this.viewDataQuery();
        }
        return this.viewDataQ;
    }

    private viewDataQuery(): Promise<SingleValueHistoryData> {
        const historyQ = singleValueHistoryRepository.findAllByServiceRecipientOrderByKeyAscValidFromDesc(this.serviceRecipientId);
        return Promise.all([historyQ, sessionDataQ])
            .then( ([history, sessionData]) => {
                return new SingleValueHistoryData(history, sessionData);
        });
    }

    protected render(data: SingleValueHistoryData): void {

        const $banner = $("<div>").css("font-style", "bold").css("margin-top", "25px").text("history");

        const $ul = $("<ul>")
            .addClass("entry-list list-unstyled");

        $banner.append($ul);

        if (data.getDataByKey(this.key).length == 0) {
            $ul.append($("<li>")
                .text("no history recorded"));
        } else {
            data.getDataByKey(this.key)
            .filter((item) => {
                return this.deletedItemIds.indexOf(item.id) == -1;
            })
            .forEach((item) => {
                const ctl = new SingleValueHistoryItemControl(item,
                    data.sessionData,
                    (id: number) => {
                        this.deletedItemIds.push(id);
                        this.render(data);
                    });
                $ul.append($("<li>").append(ctl.render()));
            });
        }
        this.element().empty().append($banner);
    }

}

class SingleValueHistoryItemControl {

    constructor(private item: dto.SingleValueHistoryDto,
            private sessionData: SessionData,
            private callbackOnRemove: (id: number) => void) {
    }

    public render() {

        const when = EccoDateTime.parseIso8601(this.item.validFrom).toEccoDate().formatPretty();
        const $val = $("<span>");

        setTextAsyncValue($val, this.lookupValue());

        return $("<span>")
                .append($("<span>").css("font-size", "0.8em").text(when + " "))
                .append($val)
                .append(this.getDeleteButton().element());
    }

    private lookupValue() {
        if (this.item.key.indexOf(":bldg") > 0) {
            // if it's a building then key could be e.g. client.residenceId:bldg
            return summariseResidence(this.item.value);
        }

        // Otherwise assume a listDefinitionEntry
        return summariseListDef(this.item.value) || "";
    }

    private getDeleteButton() {
        return new ActionButton("delete")
            .addClass("btn btn-xs btn-danger pull-right")
            .autoDisable(false)
            .clickSynchronous(() => {
                const command = new commands.SingleValueHistoryCommand(
                    "remove",
                    this.item.serviceRecipientId,
                    this.item.key,
                    this.item.id);
                const cmdQueue = new CommandQueue(getCommandQueueRepository());
                cmdQueue.addCommand(command);
                cmdQueue.flushCommands().then(() => {
                    this.callbackOnRemove(this.item.id);
                });
            });
    }
}

export = SingleValueHistoryControl;
