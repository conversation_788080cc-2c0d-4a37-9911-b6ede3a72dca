import $ = require("jquery");

import ActionButton = require("../../controls/ActionButton");
import CommandEmittingForm = require("../../cmd-queue/CommandEmittingForm");
import Form = require("../../controls/Form");
import InputGroup = require("../../controls/InputGroup");
import RadioButtons = require("../../controls/input/RadioButtons");
import SendEmailPanel = require("../../email/SendEmailPanel");
import TextAreaInput = require("../../controls/TextAreaInput");
import * as commands from "ecco-commands";
import {CommandQueue} from "ecco-commands";
import {SendEmailCommand} from "ecco-commands";
import {ActionComponent, SessionData, SmartStepStatus} from "ecco-dto";
import {SupportAction} from "ecco-dto/evidence-dto";
import {getCommandQueueRepository} from "ecco-offline-data";
import {EvidenceDef} from "ecco-evidence";
import {Uuid} from "@eccosolutions/ecco-crypto";


class RepeatedActionForm implements CommandEmittingForm {

    private submitButton: ActionButton;
    private form = new Form();
    private commandQueue = new CommandQueue(getCommandQueueRepository());
    private submitCallback: (commandQueue: CommandQueue) => void;
    private statusChangeReasonControl: RadioButtons;
    private statusChangeCommentInput = new TextAreaInput("statusChangeCommentInput");
    private workUuid = Uuid.randomV4();
    private emailPanel = new SendEmailPanel();

    /** We provide the form with the referralId and current state, and we get back a CommandQueue
     *  containing the changes which have happened.
     */
    constructor(private sessionData: SessionData, private serviceRecipientId: number,
                private evidenceDef: EvidenceDef, private supportAction: SupportAction,
                private actionDef: ActionComponent, private actionInstanceUuid: string) {

        this.submitButton = new ActionButton("save", "saving...")
                .addClass("btn btn-primary")
                .clickSynchronous( () => this.submitForm() ) // TODO: change to click() and deal with consequences
                .disable();

        let $header = $("<div>").addClass("page-header top-gap-15").appendTo(this.form.element());
        $header.append( $("<div>").append( $("<strong>").text(this.actionDef.getName()) ) );

        const listName = this.sessionData.getActionChecksListNameSetting(this.actionDef);
        if (listName) {
            const entries = this.sessionData.getListDefinitionEntriesByListName(listName)
                .map((entry) => ({name: entry.getName(), id: entry.getId(), iconClasses: entry.getIconClasses()}));

            const $listElem = $("<span>").attr("list-name", listName);
            this.statusChangeReasonControl = new RadioButtons(entries, this.supportAction.statusChangeReasonId);
            this.statusChangeReasonControl.element().addClass("pull-right");
            this.statusChangeReasonControl.change( (id, name) =>
                    this.emailPanel.onChange(this.actionDef.getId(), name, this.actionDef.getName()) );

            this.form.append(new InputGroup("result of check:", this.statusChangeReasonControl));
        }

        this.form.append(new InputGroup("say something about this check (optional)", this.statusChangeCommentInput));

        this.form.append(this.emailPanel.element());
        this.submitButton.enable();
    }

    public onSubmit(submitCallback: (commandQueue: CommandQueue) => void) {
        this.submitCallback = submitCallback;
        return this;
    }

    private submitForm() {

        const uuid = Uuid.randomV4();

        const addGoalCmd = new commands.GoalUpdateCommand("update", uuid, this.workUuid, this.serviceRecipientId,
                this.evidenceDef.getTaskName(), this.actionDef.getId(), this.evidenceDef.getEvidenceGroup(),
                Uuid.parse(this.actionInstanceUuid), null)
                .changeAnnotation("statusChangeComment", null, this.statusChangeCommentInput.val())
                .changeStatus(this.supportAction && this.supportAction.status, SmartStepStatus.AchievedAndStillRelevant)
                .changeStatusChangeReason(this.supportAction && this.supportAction.statusChangeReasonId,
                this.statusChangeReasonControl.getSelectedId())
            .setForceStatusChange();

        this.commandQueue.addCommand(addGoalCmd);

        if (this.emailPanel.shouldIncludeEmail()) {
            let emailCmd = new SendEmailCommand(this.workUuid, this.serviceRecipientId,
                this.evidenceDef.getTaskName(), this.evidenceDef.getEvidenceGroup(),
                this.emailPanel.getEmailSubform().getSubject(), this.emailPanel.getEmailSubform().getBody());
            this.commandQueue.addCommand(emailCmd);
        }

        this.submitCallback && this.submitCallback( this.commandQueue );
    }

    public element() {
        return this.form.element();
    }
    public getFooter() {
        return this.submitButton.element();
    }
    public title() { return "record a check: " + (this.supportAction.goalName || ""); }
}
export = RepeatedActionForm;
