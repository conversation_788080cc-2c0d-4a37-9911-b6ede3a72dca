<!DOCTYPE html>
<html>
<head>
    <title>DrawTests</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>


    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/draw/DrawTests"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>

    <div id="properties" style="border: 1px solid #bbb; border-radius: 4px; padding:2px; text-align:center">(click on three)</div>
    <div id="canvas">
    </div>
    <div id="canvas2">
    </div>
</body>
</html>
