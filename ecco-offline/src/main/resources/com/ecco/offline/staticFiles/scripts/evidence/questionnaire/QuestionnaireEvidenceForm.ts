import {CommandQueue} from "ecco-commands";
import * as referralDto from "ecco-dto";

import EvidenceEmbeddedSwitcherControl = require("../../evidence/EvidenceEmbeddedSwitcherControl");
import {EvidenceDef} from "ecco-evidence";
import QuestionGroupsControl = require("../../evidence/questionnaire/QuestionGroupsControl");
import QuestionnaireEvidenceBaseForm = require("../../evidence/questionnaire/QuestionnaireEvidenceBaseForm");
import {WorkUuidResolver} from "ecco-commands";

class QuestionnaireEvidenceForm extends QuestionnaireEvidenceBaseForm {

    private questionGroupsControl: QuestionGroupsControl;

    // taken from tabular/SmartStepEvidenceForm & graph/SmartStepEvidenceForm
    constructor(workUuidQResolver: WorkUuidResolver,
                planAllocatedId: string,
                embeddedSwitcher: EvidenceEmbeddedSwitcherControl,
                referral: referralDto.ServiceRecipientWithEntities,
                evidenceDef: EvidenceDef,
                _title,
                onCompleted: () => void,
                commandQueue: CommandQueue) {
        super(workUuidQResolver, planAllocatedId, embeddedSwitcher, referral, evidenceDef, _title,
            onCompleted, commandQueue);
    }

    public createMainControl() {
        this.questionGroupsControl = new QuestionGroupsControl(this.serviceRecipient,
            this.workUuidResolver,
            this.evidenceDef);
        return this.questionGroupsControl;
    }

    protected isValid(): boolean {
        return this.questionGroupsControl ? this.questionGroupsControl.isValid() : true;
    }

}

export = QuestionnaireEvidenceForm;
