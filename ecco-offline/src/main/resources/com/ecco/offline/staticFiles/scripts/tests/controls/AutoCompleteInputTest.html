<!DOCTYPE html>
<html>
<head>
    <title>AutoCompleteInput test</title>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../../css/typeahead.css">

    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/controls/AutoCompleteInputTest"])</script>

</head>
<body>
    <style>
      .container {
        width: 800px;
        margin: 50px auto;
      }

      .typeahead-wrapper {
        display: block;
        margin: 50px 0;
      }

      .tt-dropdown-menu {
        background-color: #fff;
        border: 1px solid #000;
      }

      .tt-suggestion.tt-cursor {
        background-color: #ccc;
      }
    </style>

    <h2>AutoCompleteInput</h2>
    <div id="form">
    </div>

</body>
</html>
