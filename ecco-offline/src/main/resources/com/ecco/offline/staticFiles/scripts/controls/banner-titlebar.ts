import $ = require("jquery");
import {ResizeEvent} from "@eccosolutions/ecco-common";

/**
 * Allow #banner to scroll off, but keep #titlebar when it gets to top.  Ensures following content is pushed below
 * #banner and #titlebar by creating a spacer container ($spacer) on which we set the height to match the total of
 * the fixed position elements $banner and $titlebar
 */
class BannerTitlebar {

    public static attach() {
        let $window= $(window);
        let $body = $("body");
        let $html = $("html");

        var $banner = $('#banner-container');
        var $titlebar = $('#titlebar-container');
        if ($banner.length != 1 || $titlebar.length != 1) {
            throw new Error("Could not find banner-titlebar elements");
        }

        var bannerMarginV = parseInt($banner.css("margin-top")) + parseInt($titlebar.css("margin-bottom"));

        /** rel position div to fill the space behind our fixed containers */
        var $spacer = $("<div>");
        $banner.before($spacer);

        function setPositions(){
            let bannerLeft = $body.offset().left;
            let bodyWidth = $body.outerWidth();

            var scrollLeft = $window.scrollLeft();
            var scrollTop = $window.scrollTop();
            var bannerTop = - scrollTop;
            var bannerBottom = bannerTop + $banner.outerHeight() + bannerMarginV;
            $banner.css({
                'z-index': 11,
                'position': 'fixed',
                'width': Math.min($window.width(), bodyWidth),// - bannerPadding,
                'left': bannerLeft,
                'top': bannerTop
            });
            $titlebar.css({
                'z-index': 10,
                'position': 'fixed',
                'width': Math.min($window.width(), bodyWidth),// - titlebarPadding,
                'left': bannerLeft,
                'top': scrollTop < $banner.outerHeight() ? bannerBottom : 0
            });

            $spacer.css({
                'height': $banner.outerHeight() + $titlebar.outerHeight()
            });
        }

        $window.scroll( setPositions );
        $window.resize( setPositions );

        ResizeEvent.bus.addHandler( () => setPositions() );
    }
}
$(() => {
    BannerTitlebar.attach();
});

export = BannerTitlebar