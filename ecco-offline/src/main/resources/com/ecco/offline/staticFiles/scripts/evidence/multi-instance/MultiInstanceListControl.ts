import $ = require("jquery");
import {EventBus} from "@eccosolutions/ecco-common";
import {MultiActionsControl} from "./MultiActionsControl";
import * as types from "@eccosolutions/ecco-common";
import {
    ActionInstanceControl,
    ActionInstanceControlFactory,
    ActionGroupDefs,
    ActionGroupDef
} from "../../evidence/evidenceControls";
import {ActionInstanceParentFeatures, HierarchyPosition} from "ecco-evidence";
import {ActionComponent, OutcomeComponent} from "ecco-dto";
import {CommandQueue} from "ecco-commands";
import BaseControl = require("../../controls/BaseControl");
import {EvidenceContext} from "ecco-evidence";
import {
    BaseServiceRecipientCommandDto,
    GoalUpdateCommandDto,
    SupportAction
} from "ecco-dto/evidence-dto";
import {SmartStepStatus, EvidenceGroup} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EvidenceUpdateEvent} from "ecco-dto";
import Lazy = require("lazy");
import {EvidenceDisplayOptions} from "ecco-evidence";

/**
 * Visual representation of a container with smart steps (Actions). This matches the content of a
 * tab on a needs assessment/support plan/review.
 */
abstract class MultiInstanceListControl<CHILD_TYPE extends ActionInstanceControl> extends BaseControl implements ActionGroupDefs {

    static isActionInstanceCommandDto(cmd: BaseServiceRecipientCommandDto): cmd is GoalUpdateCommandDto {
        return (<GoalUpdateCommandDto>cmd).actionInstanceUuid !== undefined;
    }

    /** event bus for the evidenceGroup we have, so that emitted events are received here
     * i.e. for "needs" or "risk" or "myplan" */
    protected eventBus: EventBus<EvidenceUpdateEvent>;

    private actionsControls: MultiActionsControl<CHILD_TYPE>[] = [];
    private actionsControlsByControlUuid: types.StringToObjectMap<MultiActionsControl<CHILD_TYPE>> = {};

    constructor(protected context: EvidenceContext, protected outcomeDef: OutcomeComponent,
                protected isNeedsBasedOnly: boolean, protected showUnachievedOnly: boolean,
                protected latestEvidence: SupportAction[],
                protected childCtor: ActionInstanceControlFactory<CHILD_TYPE>,
                protected controlsQ: Promise<any>[]) {
        super();
        this.initialisePrivateVars();
        this.setupFromEvent();
        this.initialiseFromDataOrMsg(this.element());
    }

    isValid(): boolean {
        return this.actionsControls.every(ctl => ctl.isValid());
    }

    isEmpty(): boolean {
        return this.actionsControls.length == 0;
    }

    isRelevant(): boolean {
        return this.actionsControls.every(ctl => ctl.isRelevant());
    }

    emitChangesTo(commandQueue: CommandQueue) {
        this.actionsControls.forEach(ctl => ctl.emitChangesTo(commandQueue));
    }

    childControlsOf(parentActionInstanceUuid: string, hierarchy: number): number {
        return this.actionsControls.filter(c => c.getRootUuid() == parentActionInstanceUuid && c.getHierarchy() == hierarchy).length;
    }

    public abstract render();

    /** initialisePrivateVars required since the super() calls are done first which may call overridden methods - so make it clear */
    protected abstract initialisePrivateVars();
    protected abstract initialiseFromDataOrMsg($containerMessage: $.JQuery);
    protected abstract createMultiActionsControlFromCommand(event: EvidenceUpdateEvent,
                                                            rootUuid: string): MultiActionsControl<CHILD_TYPE>;
    protected abstract createMultiActionsControlFromSnapshot(parentControlFeatures: ActionInstanceParentFeatures,
                                                             actionDef: ActionComponent, uuid: Uuid, sa: SupportAction | null,
                                                             actionGroup: ActionGroupDef): MultiActionsControl<CHILD_TYPE>;

    public changedDisplayOptions(filter: EvidenceDisplayOptions) {
    }

    /** Subscribe to various event handlers */
    protected setupFromEvent() {
        this.eventBus = EvidenceUpdateEvent.bus(this.context.evidenceDef.getEvidenceGroup().name);
        this.eventBus.addHandler( (event: EvidenceUpdateEvent) => {
            //console.debug("MultiInstanceListControl: got event with dto: %o", event.commandDto);
            // check its a goal command - since that is all we expect to handle here
            if (!MultiInstanceListControl.isActionInstanceCommandDto(event.commandDto)) {
                return;
            }
            if (!this.isEventRelevant(event)) {
                return;
            }

            // TODO not every command should create a control - what about just updates?
            this.createAddAppendApplyFromEvent(event);
        });
    }

    protected setupFromSnapshot(features: ActionInstanceParentFeatures, actionDef: ActionComponent, controlUuid: Uuid, supportAction: SupportAction | null, actionGroup: ActionGroupDef) {
        if (this.canCreateSnapshotForThisPage(features, supportAction)) {
            this.createAddAppendApplyFromSnapshot(supportAction, features, actionDef, controlUuid, actionGroup);
        }
    }

    protected isEventRelevant(event: EvidenceUpdateEvent) {
        // TODO solve this hack where we don't show any tabs on 'checks' so ignore restrictToThisControl
        // TODO because these commands aren't generated by a control - they are incoming cmds
        if (this.outcomeDef == null) {
            return true;
        }

        const cmd: GoalUpdateCommandDto = event.commandDto as GoalUpdateCommandDto;
        // event may be fired twice
        // TODO clearly the actionInstanceUuid was expected to be the key already
        const alreadyExists = this.actionsControlsByControlUuid[cmd.actionInstanceUuid];
        // check its relevant to our tab (there is a MultiInstanceListControl per tab)
        const correctTab = this.context.evidenceDef.getEvidenceGroup() == EvidenceGroup.threat
            ? this.context.features.getRiskActionById(cmd.actionDefId).getOutcome().getId() == this.outcomeDef.getId()
            : this.context.features.getSupportActionById(cmd.actionDefId).getOutcome().getId() == this.outcomeDef.getId();

        // we want to restrict adding new dynamic commands to the control which instigated it
        // otherwise we have embedded evidence tab handling the event which doesn't exist there without more work
        const restrictToThisControl = Lazy(this.actionsControls).some(mac => mac.uniqueContextUuid == event.uniqueContextUuid);
        return correctTab && !alreadyExists && restrictToThisControl;
    }

    protected addControl(multiActionsControl: MultiActionsControl<CHILD_TYPE>) {
        this.actionsControls.push(multiActionsControl);
        this.actionsControlsByControlUuid[multiActionsControl.getControlUuid().toString()] = multiActionsControl;
    }

    protected appendControl(mac: MultiActionsControl<CHILD_TYPE>,
                           actionDefId: number, hierarchy?: number, position?: HierarchyPosition) {
        this.element().append(mac.element());
    }

    protected canCreateSnapshotForThisPage(features: ActionInstanceParentFeatures, supportAction: SupportAction | null): boolean {
        const allowed = (features.isNeedsBased() || (supportAction != null)) &&
            (!features.isShowUnachievedOnly()
                || (features.isShowUnachievedOnly() && supportAction.status != SmartStepStatus.Achieved));
        const shouldHide  = MultiActionsControl.shouldHideWhenGoalPlanWithNotRelevant(this.context, supportAction);
        return allowed && !shouldHide;
    }

    private createAddAppendApplyFromEvent(event: EvidenceUpdateEvent) {
        const dto = event.commandDto as GoalUpdateCommandDto;
        const alreadyExists = this.actionsControlsByControlUuid[dto.actionInstanceUuid];

        if (alreadyExists) {
            this.actionsControlsByControlUuid[dto.actionInstanceUuid].applyCommand(dto);
            return;
        }

        const hierarchy = dto.hierarchyChange && dto.hierarchyChange.to;
        const position = new HierarchyPosition(dto.positionChange && dto.positionChange.to);
        const rootUuid = dto.parentActionInstanceUuid;
        //const parentControl = this.getParentControlOf(event.commandDto.parentActionInstanceUuid, hierarchy, position);
        console.debug("MultiActionsControl (creating): "
            + " hierarchy " + hierarchy
            + " position " + position
            + " rootUuid " + rootUuid
            + ": handling by outcome: %o", this.outcomeDef
        );
        // create a new container - since each instance can itself become a parent
        this.createAddAppendApplyFromCommand(event, rootUuid, dto, hierarchy, position);
    }

    private createAddAppendApplyFromCommand(event: EvidenceUpdateEvent, rootUuid, dto, hierarchy, position) {
        const multiActionsControl = this.createMultiActionsControlFromCommand(event, rootUuid);
        this.addControl(multiActionsControl);
        this.appendControl(multiActionsControl, dto.actionDefId, hierarchy, position);
        multiActionsControl.applyCommand(event.commandDto);
        if (!multiActionsControl.isAppliedToDom()) {
            multiActionsControl.render();
        }
    }

    private createAddAppendApplyFromSnapshot(sa: SupportAction | null,
                                             features: ActionInstanceParentFeatures,
                                             actionDef: ActionComponent,
                                             controlUuid: Uuid,
                                             actionGroup: ActionGroupDef) {
        // risk doesn't have an actionInstanceUuid yet, so check for one
        const hierarchy = sa ? sa.hierarchy : 0;
        const position = sa ? new HierarchyPosition(sa.position) : new HierarchyPosition(null);

        const multiActionsControl = this.createMultiActionsControlFromSnapshot(features, actionDef, controlUuid, sa, actionGroup);
        this.addControl(multiActionsControl);
        this.appendControl(multiActionsControl, actionDef.getId(), hierarchy, position);
        multiActionsControl.applySnapshot(sa);
        if (!multiActionsControl.isAppliedToDom()) {
            multiActionsControl.render();
        }
    }

}
export = MultiInstanceListControl;
