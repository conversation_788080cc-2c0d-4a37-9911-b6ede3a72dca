import domain = require("./domain");

export function interval(timeseries: domain.Period) {
    return {
        start: calculateSlot(timeseries[0], timeseries[1]),
        end: calculateSlot(timeseries[2], timeseries[3])
    };
}

/** Turn h:m time into a slot number */
export function calculateSlot(h:number, m:number) {
    var p = h * 2;
    if (m >= 30) p+= 1;
    return p;
}

export function convert(start: number, end: number) {
    var startmins = 0;
    var endmins = 0;
    if (start % 2 == 1) {
        startmins = 30;
        start--;
    }
    var starthours = start / 2;
    if (end % 2 == 1) {
        endmins = 30;
        end--;
    }
    var endhours = end / 2;
    return [starthours, startmins, endhours, endmins];
}