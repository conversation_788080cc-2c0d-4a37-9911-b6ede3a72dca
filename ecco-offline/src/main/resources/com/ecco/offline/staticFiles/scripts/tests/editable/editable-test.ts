import QUnit = require("qunit");
import {default as Editable} from "../../editable/editable";
import $ = require("jquery");

// Must manually bootstrap, as TSC would not load editableInit if we try using that.
var editable = new Editable();
editable.attach();

    QUnit.test('Editable text field should have ....', function (){

        QUnit.equal($("#referralReason span").attr("title"), "Click to edit", "referralReason title attribute should be 'Click to edit'");
        QUnit.equal($("#referralDate span").attr("title"), "Click to edit", "referralDate title attribute should be 'Click to edit'");
    });

QUnit.load();
QUnit.start();
