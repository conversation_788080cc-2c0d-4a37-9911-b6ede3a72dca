import * as React from "react";
import {FC, useState} from "react";
import {createDropdownList, createTextInput} from "ecco-components-core";
import {
    LoadingSpinner,
    useIndividual,
    useServicesContext,
    useUserByContactId,
    useUsersWithoutWorker
} from 'ecco-components';
import {Box, Button, Grid, Typography} from '@eccosolutions/ecco-mui';
import {Individual} from 'ecco-dto';
import {UserView} from "../UserView";
import {ReloadEvent} from "@eccosolutions/ecco-common";

const ExistingUser = (props: { contact: Individual }) => {
    const {user} = useUserByContactId(props.contact.contactId);

    if (!user) return <LoadingSpinner/>

    return <>
        <Box m={2}>
            <Typography variant="body1">
                {props.contact.firstName} {props.contact.lastName} can login with the username: {user.username}
            </Typography>
        </Box>
        <UserView username={user.username}/>
    </>;
};

const LinkOrCreateUser = ({workerId, contact}: {workerId: number, contact: Individual}) => {
    const {users} = useUsersWithoutWorker(); // TODO: We could do with this being available for client.contact too
                                             // instead of creating a user account and disabling it for all clients

    const [linkUsername, setLinkUsername] = useState<string | number | null>(null);
    const [newUsername, setNewUsername] = useState<string | null>(null);

    const {contactsRepository, workersRepository} = useServicesContext();

    if (!users) return <LoadingSpinner/>;

    // FIXME: Do as 2 cards
    return <Box m={2}>
        <Grid container>
            <Grid item xs={12}>
                {contact.firstName} {contact.lastName} can be associated with an existing user account
            </Grid>
            <Grid item xs={12}>
                {createDropdownList(
                    "users", "available users", linkUsername,
                    setLinkUsername,
                     users.map(user => ({
                         name: `${user.username} - ${user.individual.firstName} ${user.individual.lastName}`,
                         id: user.username
                     })),
                    {}
                )}
            </Grid>
            <Grid item xs={12}>
                <Box textAlign="right">
                    <Button onClick={() => {
                        workersRepository.linkUser(workerId, linkUsername as string).then(() => ReloadEvent.bus.fire());
                    }}>link</Button>
                </Box>
            </Grid>
            <Grid item xs={12}>
                or you can create a new one for this person
            </Grid>
            <Grid item xs={12}>
                {createTextInput("newUsername", "username", newUsername, setNewUsername, 'text')}
                <Box textAlign="right">
                    <Button
                        disabled={!newUsername}
                        onClick={() => contactsRepository.createUserFromContact(contact.contactId, newUsername!)
                            .then(() => ReloadEvent.bus.fire())}
                    >add user for this contact</Button>
                </Box>
            </Grid>
        </Grid>
    </Box>;
};

/**
 * Allow system access for any contact to be shown, but if workerId is provided,
 * then activate worker specific features
 */
export const SystemAccess: FC<{contactId: number, workerId?: number | undefined}> = ({contactId, workerId}) => {

    const {contact} = useIndividual(contactId);

    if (!contact) return <LoadingSpinner/>;

    if (contact.isUser) return <ExistingUser contact={contact}/>;

    if (workerId) return <Grid container>
        <LinkOrCreateUser workerId={workerId} contact={contact}/>
    </Grid>;

    return <Typography>No associated user account</Typography>;

};