import $ = require("jquery");

import {getUserSessionManager} from "ecco-offline-data";
import {setGlobalApiClient} from "ecco-dto";
import {apiClient} from "ecco-components";

setGlobalApiClient(apiClient);

if (getUserSessionManager()) {
    // const welcomePageUri = URI("nav/secure/welcome.html")
    //     .absoluteTo(URI(applicationRootPath));

    $(() => {
        const $loginForm = $("form.login-form");
        const $usernameInput = $loginForm.find("input.username");
        const $passwordInput = $loginForm.find("input.password");

        const onSubmit = (event: $.BaseJQueryEventObject) => {
            const username = $usernameInput.val();
            const password = $passwordInput.val();

            getUserSessionManager().login(username, password)
                .catch((reason: any) => {
                    $loginForm.unbind("submit", onSubmit);
                    $loginForm.submit();

                    throw reason;
                })
                .then(() => {
                    // login a second time to allow any login redirect to go back to original targetURI
                    $loginForm.unbind("submit", onSubmit);
                    $loginForm.submit();
                    // window.location.href = welcomePageUri.toString();
                });

            event.preventDefault();
        };

        $loginForm.submit(onSubmit);
    });
}
