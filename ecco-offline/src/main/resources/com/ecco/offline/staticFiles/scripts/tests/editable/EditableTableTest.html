<!DOCTYPE html>
<html>
<head>
    <title>EditableTableTest</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>
    <link rel="stylesheet" href="../../../css/editable/editable.css"/>
    <link rel="stylesheet" href="../../../font-awesome/css/font-awesome.min.css"/>


    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/editable/EditableTableTest"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">

    </div>

    <div id="table">
    </div>
</body>
</html>
