import $ = require("jquery");
import {apiClient} from "ecco-components";
import {StatsAjaxRepository} from "./StatsAjaxRepository";

var repository = new StatsAjaxRepository(apiClient);

repository.getAuditStats().then( stats => {
    var $dash = $(".sysadmin-dashboard");

    $dash
        .append( $("<dt>").text("active users in last 7 days") )
        .append( $("<dd>").text(stats.activeUsersLast7Days.toString()) )
        .append( $("<dt>").text("active users in last 30 days") )
        .append( $("<dd>").text(stats.activeUsersLast30Days.toString()) )
        .append( $("<dt>").text("offline commands executed") )
        .append( $("<dd>").text(stats.offlineStats.totalExecuted) )
        .append( $("<dt>").text("not executed") )
        .append( $("<dd>").text(stats.offlineStats.totalNotExecuted) );


});


