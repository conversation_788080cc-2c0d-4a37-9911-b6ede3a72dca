import $ = require("jquery-jqplot-bundle");

import BaseControl = require("./BaseControl");
import {bus, EventHandler} from "@eccosolutions/ecco-common";
import {KeySelector} from "ecco-reports";

export class ChartData {
    constructor(private dataSeries: DataSeries<any>[]) {
    }

    public getDataSeries(): DataSeries<any>[] {
        return this.dataSeries;
    }
}

export class ChartOptions {
    constructor(public canClickThrough = true) {
    }
}

export interface KeyValuePair {
    key: string;
    value: number;
}

export class DataSeries<TDatum> {
    private clickEventBus = bus<ClickEvent<TDatum>>();
    private clickMany = false;
    private clickIndexes: number[] = [];

    constructor(private label: string,
                private selectionKeys: KeySelector,
                private data: TDatum[],
                private representation: DataRepresentation<TDatum>) {
        // TODO pre-defined selectionKeys need to update, or replace, clickIndexes
    }

    public getLabel(): string {
        return this.label;
    }

    public getData(): TDatum[] {
        return this.data;
    }

    public getRepresentation(): DataRepresentation<TDatum> {
        return this.representation;
    }

    public getSelectedIndexes() {
        return this.clickIndexes;
    }

    public click(index: number): void {
        if (index >= 0 && index < this.data.length) {
            if (this.clickMany) {
                let existingIdx = this.clickIndexes.indexOf(index);
                if (existingIdx > -1) {
                    this.clickIndexes.splice(existingIdx, 1);
                } else {
                    this.clickIndexes.push(index);
                }
                let data = this.clickIndexes.reduce((cumulative: TDatum[], idx: number) => {
                    return cumulative.concat(this.data[idx]);
                }, []);
                this.clickEventBus.fire(new ClickEvent<TDatum>().selectManyItems(this.clickIndexes, data));
            } else {
                this.clickIndexes = [index];
                this.clickEventBus.fire(new ClickEvent<TDatum>().selectSingleItem(index, this.data[index]));
            }
        }
    }

    public clickAll(): void {
        this.clickEventBus.fire(new ClickEvent<TDatum>().selectAll(this.data));
    }

    public updateClickManyState(state: boolean): void {
        this.clickMany = state;
    }

    public addClickEventHandler(handler: EventHandler<ClickEvent<TDatum>>): void {
        this.clickEventBus.addHandler(handler);
    }

    public removeClickEventHandler(handler: EventHandler<ClickEvent<TDatum>>): void {
        this.clickEventBus.removeHandler(handler);
    }
}

/**
 * A throw-away event which captures the meaning of the click event
 */
export class ClickEvent<TDatum> {
    private indexes: number[] = [];
    private selectedDatums: TDatum[];
    private selectedAll = false;

    constructor() {
    }

    public selectSingleItem(index: number, datum: TDatum) {
        this.indexes = [index];
        this.selectedDatums = [datum];
        return this;
    }

    public selectManyItems(indexes: number[], datum: TDatum[]) {
        this.indexes = indexes;
        this.selectedDatums = datum;
        return this;
    }

    public selectAll(all: TDatum[]) {
        this.indexes = [];
        this.selectedAll = true;
        this.selectedDatums = all;
        return this;
    }

    public isSelectedAll(): boolean {
        return this.selectedAll;
    }

    public getDatums(): TDatum[] {
        return this.selectedDatums;
    }
}

export interface DataRepresentation<TDatum> {
    represent(datum: TDatum): any;
    getJqplotRenderer(): { new(); };
    getJqplotRendererOptions(): any;
    getXAxisRenderer(): { new(); }
    getXAxisTickRenderer(): { new(); }
    getXAxisTickOptions(): any;
}


class BarChartOptions {
    angleLabels: boolean = true;
}

export class BarChartDataRepresentation<TDatum> implements DataRepresentation<TDatum> {
    protected renderOptions: BarChartOptions = new BarChartOptions();
    constructor(private toKeyValue: (datum: TDatum) => KeyValuePair, private renderOptionsJsonObj?: any) {
        if (renderOptionsJsonObj) {
            this.renderOptions = <BarChartOptions>(renderOptionsJsonObj);
        }
    }

    public getJqplotRenderer(): { new(); } {
        return (<any> $).jqplot.BarRenderer;
    }

    public getJqplotRendererOptions(): any {
        return {
            fillToZero: true
        };
    }

    public getXAxisRenderer(): { new(); } {
        return (<any> $).jqplot.CategoryAxisRenderer;
    }

    public getXAxisTickRenderer(): { new() } {
        if (this.renderOptions.angleLabels) {
            return (<any> $).jqplot.CanvasAxisTickRenderer;
        }
        return null;
    }

    public getXAxisTickOptions(): any {
        if (this.renderOptions.angleLabels) {
            return {
                angle: -90
                //,fontSize: '10pt'
            }
        }
    }

    public represent(datum: TDatum): any[] {
        var kv = this.toKeyValue(datum);
        return [kv.key, kv.value];
    }
}


class PieChartOptions {
    percentage: boolean;
    highlightMouseOver: boolean;
}

export class PieChartDataRepresentation<TDatum> implements DataRepresentation<TDatum> {
    protected renderOptions: PieChartOptions = new PieChartOptions();
    constructor(private toKeyValue: (datum: TDatum) => KeyValuePair, renderOptionsJsonObj?: any) {
        if (renderOptionsJsonObj) {
            this.renderOptions = <PieChartOptions>(renderOptionsJsonObj);
        }
    }

    public getJqplotRenderer(): { new() } {
        return (<any> $).jqplot.PieRenderer;
    }

    public getJqplotRendererOptions(): any {
        return {
            showDataLabels: true,
            dataLabels: this.renderOptions.percentage ? 'percent' : 'value',
            sliceMargin: 3,
            highlightMouseOver: this.renderOptions.highlightMouseOver
        }
    }

    public getXAxisRenderer(): { new(); } {
        return null;
    }

    public getXAxisTickRenderer(): { new() } {
        return null;
    }

    public getXAxisTickOptions(): any {
        return null;
    }

    public represent(datum: TDatum): any[] {
        var kv = this.toKeyValue(datum);
        return [kv.key, kv.value == 0 ? 0.0001 : kv.value];
    }
}


class DonutChartOptions {
    totalLabel: boolean; // UNAVAILABLE in the jqPlot bundle version we have
}

export class DonutChartDataRepresentation<TDatum> extends PieChartDataRepresentation<TDatum> {
    private renderDonutOptions: DonutChartOptions = new DonutChartOptions();
    constructor(toKeyValue: (datum: TDatum) => KeyValuePair, renderOptionsJsonObj?: any) {
        super(toKeyValue, renderOptionsJsonObj);
        if (renderOptionsJsonObj) {
            this.renderDonutOptions = <DonutChartOptions>(renderOptionsJsonObj);
        }
    }

    public override getJqplotRendererOptions(): any {
        var options = super.getJqplotRendererOptions();
        if (this.renderDonutOptions.totalLabel) {
            // see http://www.jqplot.com/docs/files/plugins/jqplot-donutRenderer-js.html#$.jqplot.DonutRenderer.totalLabel
            // NB UNAVAILABLE in the jqPlot bundle version we have
            options['totalLabel'] = true;
        }
        return options;
    }

    public override getJqplotRenderer(): { new() } {
        return (<any> $).jqplot.DonutRenderer;
    }
}

export class ChartControl extends BaseControl {
    private plot: any;

    private data: ChartData;
    private options: ChartOptions;

    private $inner: $.JQuery;
    constructor() {
        var $inner = $("<div>")
                .addClass("chart");

        super($("<div>").append($inner)); // We need to wrap or else jqplot complains (Dan??)

        this.$inner = $inner;
        this.plot = null;
        this.data = null;
    }

    public setData(data: ChartData, options: ChartOptions) {
        this.data = data;
        this.options = options;

        this.redraw();
    }

    public saveImage(filename: string): void {
        this.$inner.data("filename", filename.concat(".png"));
        var result = (<any>this.$inner).jqplotSaveImage();
    }

    private redraw(): void {
        if (this.plot) {
            this.plot.destroy();
        }

        var $container = this.$inner;

        var tempId = "chart-temp-" + (new Date().getTime());
        $container.attr("id", tempId);

        // NOTE: $container must be attached to page otherwise jqplot cannot work out the size
        if (this.data) {
            $container.css('width', ''); // remove explicit width that jqplot set last time
            this.plot = (<any> $).jqplot(tempId, this.computeJqplotData(), this.computeJqplotOptions());

            if (this.options.canClickThrough) {
                $container.bind("jqplotDataClick", (e, seriesIndex: number, pointIndex: number) => {
                    this.data.getDataSeries()[seriesIndex].click(pointIndex);
                });
                // based on http://stackoverflow.com/questions/13378347/jqplot-pie-chart-how-can-i-capture-a-click-on-a-legend-item
                // NB requires [placement: 'outsideGrid'] - see the comments on above link
                // HACK we hard code the series number '0' since this works for us at the moment
                let $labels = $container.find("table .jqplot-table-legend-label");
                $labels.addClass("clickable");
                $container.find("table.jqplot-table-legend").on("click", "tr", (event) => {
                    var $jqElem = $(event.target).closest('tr');
                    var pointIndex = $jqElem.index();
                    this.clickIndex($container, pointIndex);
                });
            }
        } else {
            this.plot = null;
            $container.unbind();
        }

        $container.removeAttr("id");
    }

    private clickIndex($container, index: number) {
        // HACK assume one series '0' at the moment
        this.data.getDataSeries()[0].click(index);

        // update 'this' chart with the hover info
        let $labels = $container.find("table .jqplot-table-legend-label");
        // highlight the legend items if multi-select
        // HACK assume one series '0' at the moment
        let selectedIndexes = this.data.getDataSeries()[0].getSelectedIndexes();
        $labels.each(idx => {
            selectedIndexes.indexOf(idx) > -1
                ? $labels.eq(idx).addClass("mimic-hover")
                : $labels.eq(idx).removeClass("mimic-hover");
        });
    }

    private computeJqplotData(): any[] {
        return this.data.getDataSeries()
                .map((dataSeries: DataSeries<any>): any[] =>
                        dataSeries.getData()
                                .map((datum) => dataSeries.getRepresentation()
                                        .represent(datum)));
    }

    private computeJqplotOptions(): any {
        // error if the series aren't all the same xAxisRenderer (or null)
        // because this setting is global to the chart, not just the series
        var xAxisRenderer = this.data.getDataSeries()
                .map((series) => series.getRepresentation().getXAxisRenderer())
                .reduce((prev, curr) => {
                    if (prev === curr) {
                        return prev;
                    } else if (prev == null) {
                        return curr;
                    } else {
                        throw new Error("Unsupported combination of data representations.");
                    }
                });

        // error if the series aren't all the same xAxisTickRenderer (or null)
        // because this setting is global to the chart, not just the series
        let xAxisTick = this.data.getDataSeries()
                .map((series) => ({
                    renderer: series.getRepresentation().getXAxisTickRenderer(),
                    options: series.getRepresentation().getXAxisTickOptions()
                }))
                .reduce((prev, curr) => {
                    if (prev.renderer === curr.renderer) {
                        return prev;
                    } else if (prev == null) {
                        return curr;
                    } else {
                        throw new Error("Unsupported combination of data representations.");
                    }
                });

        return {
            series: this.data.getDataSeries()
                    .map((series) => ({
                        label: series.getLabel(),
                        renderer: series.getRepresentation().getJqplotRenderer(),
                        rendererOptions: series.getRepresentation().getJqplotRendererOptions()
                    })),
            legend: {
                show: true,
                placement: 'outsideGrid'
            },
            axes: {
                xaxis: {
                    tickRenderer: xAxisTick.renderer || undefined,
                    tickOptions: xAxisTick.options || undefined,
                    renderer: xAxisRenderer || undefined
                }
            },
            grid: {
                drawGridLines: true,        // wether to draw lines across the grid or not.
                gridLineColor: '#cccccc',   // CSS color spec of the grid lines.
                background: 'transparent',  // CSS color spec for background color of grid.
                borderWidth: 0,             // pixel width of border around grid.
                shadow: false               // draw a shadow for grid.
            },
            noDataIndicator: {
                show: true,
                indicator: "no data",
                axes: {
                    xaxis: {
                        show: false
                    },
                    yaxis: {
                        show: false
                    }
                }
            }
        };
    }
}