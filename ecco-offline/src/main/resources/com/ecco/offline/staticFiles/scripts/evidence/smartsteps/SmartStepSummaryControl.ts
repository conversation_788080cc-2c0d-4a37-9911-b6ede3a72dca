import $ = require("jquery");

import SummarisedElement = require("../../controls/SummarisedElement");
import {Action} from "ecco-dto";

/** Summary of a referral - renders itself as a panel but you can also pull out the title and body content for rendering elsewhere e.g. in an Accordion. */
class SmartStepSummaryControl implements SummarisedElement<Action> {

    private $title: $.JQuery;
    private $icon: $.JQuery;

    /** Pass supportAction = null, if this action is not yet added (or we are adding a new multi-instance). */
    constructor(public action: Action, private onSelect: (action: Action) => void) {
        this.render();
    }

    private render() {
        const disabled = false; // was this.supportAction != null, but we now support multiple instances

        this.$icon = $("<i>").addClass(disabled ? "fa fa-star lightgrey" : "fa fa-check-circle");
        this.$title = $("<div>").addClass("container-fluid")
            .append($("<div>").addClass("row").addClass(disabled ? "grey" : "cursor-pointer")
                .append($("<div>").addClass("col-xs-11")
                    .text(this.action.getName() + " [" + this.action.actionGroup.outcome.getName() + "]")
                )
                .append($("<div>").addClass("col-xs-1")
                    .append(this.$icon)
                    .append("&nbsp;")
                )
            .click(
                () => {
                    if (!disabled) { this.onSelect(this.action); }
                })
            );
    }

    public searchId() {
        return String(this.action.getId());
    }

    public title(): $.JQuery {
        return this.$title;
    }

    public body(): $.JQuery {
        return null;
    }

    initiallyHidden():boolean {
        return false;
    }

    public target() {
        return this.action;
    }
}
export = SmartStepSummaryControl
