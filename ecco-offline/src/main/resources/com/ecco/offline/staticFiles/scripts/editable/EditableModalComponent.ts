import $ = require("jquery");

import DateInput = require("../controls/DateInput");
import Form = require("../controls/Form");
import ElementBase = require("../controls/Element");
import commands = require("./commands");
import EditableCommandControl = require("./EditableCommandControl");
import {EccoDateTime} from "@eccosolutions/ecco-common";
import DialogContent from "../controls/DialogContent";
import {ActionsChangedCallback} from "@eccosolutions/ecco-common";

// TODO: Refactor this to be based on BaseAsyncCommandForm
abstract class EditableModalComponent implements ElementBase, DialogContent {

    private form = new Form();
    private onFinished: () => void;
    private commandControl: EditableCommandControl;
    protected saveHistoryDate: DateInput;
    protected propertyPath: string;

    constructor(element: Element, protected oldText: string, private entityType: string,
            private entityId: string,
            protected serviceRecipientId: number,
            private path: string, refreshUrl: string, updateId: string,
            updateCharLimit: number, protected readOnly = false) {
        this.commandControl = new EditableCommandControl(entityType, entityId, path, refreshUrl,
            () => {
                this.onFinished();
                this.updateData(element, this.getNewDataValue());
                EditableModalComponent.updateDisplay(updateId, updateCharLimit, this.getNewText());
            });

        this.propertyPath = this.entityType + "." + this.path;

        this.layoutForm();
    }

    protected saveHistoryCommand(updateCmd: commands.UpdateCommand): commands.SingleValueHistoryCommand {
        if (this.oldText != updateCmd.newValue) {
            var date: EccoDateTime = this.saveHistoryDate
                    ? this.saveHistoryDate.getDate().toDateTimeMidnight()
                    : EccoDateTime.nowLocalTime();

            var command = new commands.SingleValueHistoryCommand(
                "add", +this.serviceRecipientId, this.propertyPath, null);
            command.changeValue(this.oldText, updateCmd.newValue);
            command.changeValidFrom(null, date.formatIso8601());

            return command;
        }

        return null;
    }

    abstract layoutForm();

    getNewDataValue(): string {
        return this.getNewText();
    }

    abstract getNewText(): string;

    abstract submitEdit(commandControl: EditableCommandControl);

    public getOldText() {
        return this.oldText;
    }

    public element() {
        return this.form.element();
    }

    getFooter(): $.JQuery {
        return null;
    }

    getTitle() {
        return "edit";
    }

    setOnFinished(callback: () => void): void {
        this.onFinished = callback;
    }

    registerActionsChangeListener(updateActions: ActionsChangedCallback): void {
        if (!this.readOnly) {
            updateActions([{
                onClick: () => this.submitEdit(this.commandControl), label: "save", clickedLabel: "saving..."
            }])
        }
    }
    private static updateDisplay(updateId: string, updateCharLimit: number, newText: string) {
        if (updateId) {
            var txt = (newText.length > updateCharLimit) ? newText.substring(0, updateCharLimit) + "..." : newText;
            $("#"+updateId).text(txt);
        }
    }

    protected updateData(element: Element, newData: string) {
        $(element).attr("data-text", newData);
    }

}

export = EditableModalComponent;
