import $ = require("jquery");
import Control = require("./Control");
import EccoElement = require("./Element");


class BaseControl implements Control {
    private $replacedNode: $.JQuery | null;

    constructor(private $container = $("<div>")) {
    }

    /** Append either an EccoElement, DOM Element some content JQuer<PERSON> is happy to append() */
    append(element: $.JQuery|string|EccoElement|Element) {
        var $child: $.JQuery;

        if ((<EccoElement>element).element) {
            $child = (<EccoElement> element).element();
        } else {
            $child = $(element);
        }

        if ($child) {
            this.$container.append($child);
        }

        return this;
    }

    /**
     * TODO This method should be called replace !
     */
    public attach($nodeToReplace: $.JQuery): void {
        this.detach();
        this.$replacedNode = $nodeToReplace;

        if ($nodeToReplace) {
            // copy across all attributes including data attrs
            var attributes = $nodeToReplace.prop("attributes");
            $.each(attributes, attr => {
                this.$container.attr(attr.name, attr.value);
            });

            $nodeToReplace.replaceWith(this.$container);
        }
        this.afterAttach();
    }

    protected afterAttach() {
        // empty: override in subclasses.
    }

    public detach(): void {
        this.beforeDetach();

        if (this.$replacedNode) {
            this.$container.replaceWith(this.$replacedNode);
            this.$replacedNode = null;
        } else {
            this.$container.detach();
        }
    }

    protected beforeDetach() {
        // empty: override in subclasses.
    }

    /** Gets the DOM element that implements the Control.
     *
     * This DOM element can be attached to the page directly, or it can be
     * attached to a parent DOM element by calling attach().
     *
     * Do not modify the content or attributes of this DOM element, except
     * in a subclass.
     *
     * Do not override this method. */
    public element(): $.JQuery {
        return this.$container;
    }

    public domElement(): HTMLElement {
        return this.$container[0];
    }
}

export = BaseControl;
