import * as React from "react";
import {RouteComponentProps} from "react-router";
import {WithWidth} from "@eccosolutions/ecco-mui";
const Chart = React.lazy(() => import("../components/Chart"));


// alternatively see ServiceRecipientTasks
interface Props extends RouteComponentProps<{serviceId: string}, {}>, Partial<WithWidth> {
    //summary: ServiceSummaryDto;
}

class ServiceOverview extends React.Component<Props, {}> {

    override render() {

        const data = [
            {
                name: 'Page A', uv: 4000, pv: 2400, amt: 2400,
            },
            {
                name: 'Page B', uv: 3000, pv: 1398, amt: 2210,
            },
            {
                name: 'Page C', uv: 2000, pv: 9800, amt: 2290,
            },
            {
                name: 'Page D', uv: 2780, pv: 3908, amt: 2000,
            },
            {
                name: 'Page E', uv: 1890, pv: 4800, amt: 2181,
            },
            {
                name: '<PERSON> <PERSON>', uv: 2390, pv: 3800, amt: 2500,
            },
            {
                name: 'Page G', uv: 3490, pv: 4300, amt: 2100,
            },
        ];

        return (
            <Chart chartUuid={"04900000-0000-babe-babe-dadafee1600d"} autoRun={true} />
        );
    }
}
export = ServiceOverview;