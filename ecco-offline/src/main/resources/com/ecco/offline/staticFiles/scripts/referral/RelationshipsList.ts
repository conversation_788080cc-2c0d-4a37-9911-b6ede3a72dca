import $ = require("jquery");
import services = require("ecco-offline-data");
import {RelatedRelationship} from "ecco-dto";
import BaseAsyncDataControl from "../controls/BaseAsyncDataControl";
import {onParentTabActivated} from "../common/tabEvents";

class BackingData {
    relatedReferrals: RelatedRelationship[] = [];
}

/**
 * List representation of a primary referral and related referrals
 */
class RelationshipsList extends BaseAsyncDataControl<BackingData> {

    constructor(private referralId: number) {
        super();
    }

    protected override afterAttach() {
        onParentTabActivated(this.element(), () => {
            this.load();
        })
    }

    protected fetchViewData(): Promise<BackingData | null> {
        return services.getReferralRepository().findRelatedReferrals(this.referralId)
            .then( (referrals: RelatedRelationship[]) => {
                if (referrals) {
                    const bd = new BackingData();
                    bd.relatedReferrals = referrals.filter( (referral: RelatedRelationship) => referral.relationship != null );
                    return bd;
                }
                return new BackingData();
            });
    }

    protected render(data: BackingData) {
        data.relatedReferrals
                .forEach( (referral: RelatedRelationship) => this.listRelationship(referral) );
    }

    private listRelationship(referral: RelatedRelationship) {
        var $row = $("<div>").attr("class", "e-row");
        $row.append(
                $("<span>").attr("class", "e-label").text(referral.relationship)
            ).append(
                $("<span>").attr("class", "input").text(referral.clientDisplayName)
            );
        this.element().append($row);
    }

}
export = RelationshipsList;
