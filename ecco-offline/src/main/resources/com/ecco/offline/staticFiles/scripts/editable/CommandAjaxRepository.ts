import URI = require("URI");
import commands = require("./commands");
import {ApiClient} from "ecco-dto";
import {ServiceRecipientAttributeChangeCommandDto} from "ecco-commands";

export class CommandAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    public postReferralUpdateCommand(referralId: string, fieldPath: string, command: commands.UpdateCommand): Promise<void> {
        const path = URI("referrals")
            .segmentCoded(referralId)
            .segmentCoded(command.type + "Update")
            .segmentCoded(fieldPath)
            .segmentCoded("");

        return this.apiClient.post<void>(path, {"oldValue": command.oldValue, "newValue": command.newValue});
    }

    public postClientUpdateCommand(clientId: string, fieldPath: string, command: commands.UpdateCommand): Promise<void> {
        const path = URI("clients")
            .segmentCoded(clientId)
            .segmentCoded(command.type + "Update")
            .segmentCoded(fieldPath)
            .segmentCoded("");

        return this.apiClient.post<void>(path, {"oldValue": command.oldValue, "newValue": command.newValue});
    }

    public postServiceRecipientAttributeCommand(serviceRecipientId: number, commandDto: ServiceRecipientAttributeChangeCommandDto): Promise<void> {
        const path = URI("service-recipients")
            .segmentCoded(serviceRecipientId.toString())
            .segmentCoded("attributeChange")
            .segmentCoded("");

        return this.apiClient.post<void>(path, commandDto);
    }
}
