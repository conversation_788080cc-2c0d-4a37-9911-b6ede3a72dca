import $ = require("jquery");
import ActionButton = require("../controls/ActionButton");
import ActivityInterestForm = require("../groupsupport/ActivityInterestForm");
import EvidenceDelegatingForm from "../evidence/EvidenceDelegatingForm";
import ChecklistEvidenceForm from "./checklist/ChecklistEvidenceForm";
import {EvidencePageType} from "ecco-dto";
import {getReferralRepository} from "ecco-offline-data";

export class EvidencePopups {

    //noinspection JSUnusedLocalSymbols - Suppressed as we use singleton to instantiate
    /** Singleton which kicks off attach() once only */
    private static instance = new EvidencePopups();

    public static showInModal($element: $.JQuery) {
        this.instance.clickAction($element);
    }

    constructor() {
        $( () => {
            this.attach();
        });
    }

    /** Find the different items we support and attach the appropriate component to each of them */
    private attach() {
        $(".evidence-button").each( (index, element) => {
            this.enhanceForm($(element));
        });
        $(".activities-button").each( (index, element) => {
            this.enhanceActivities($(element));
        });
        $(".evidence-control").each( (index, element) => {
            this.enhanceControl($(element));
        });
    }

    public clickAction($el: $.JQuery) {

        var serviceRecipientId = parseInt($el.attr("data-service-recipient-id"));
        var taskName = $el.attr("data-task-name");
        var visual = "visual".indexOf($el.attr("data-evidence-style")) > -1;
        var reviewForwardUrl = $el.attr("data-review-forward-url");
        var reviewTextId = $el.attr("data-review-text-id");

        var title = $el.text();

        switch ($el.attr("data-evidence-style")) {
            // case "visual":
            //     EvidenceDelegatingForm.showVisualInModalByIds(serviceRecipientId, title, taskName);
            //     return;
            case "checklist":
                const actAs = $el.attr("data-act-as");
                const pageType = actAs && EvidencePageType[actAs];
                ChecklistEvidenceForm.showInModalByIds(serviceRecipientId, title, taskName, pageType);
                return;
            default:
                EvidenceDelegatingForm.showInModalByIds(serviceRecipientId, title, taskName); // not fixing the refactoring
        }
    }

    private enhanceForm($el: $.JQuery) {
        const title = $el.text();

        const button = new ActionButton(title)
            .addClass("button btn btn-link")
            .autoDisable(false)
            .clickSynchronous(() => this.clickAction($el));
        $el.empty().append(button.element());
    }

    private enhanceActivities($el: $.JQuery) {

        var serviceRecipientId = parseInt($el.attr("data-service-recipient-id"));
        var title = $el.text();

        var button = new ActionButton(title)
            .addClass("button btn btn-link")
            .autoDisable(false)
            .clickSynchronous( () => {
                ActivityInterestForm.showInModalByIds(serviceRecipientId, title);
            });
        $el.empty().append(button.element());
    }

    private enhanceControl($el: $.JQuery) {
        var serviceRecipientId = parseInt($el.attr("data-service-recipient-id"));
        var taskName = $el.attr("data-task-name");
        var footerSelector = $el.attr("data-footer-selector");
        var planAllocatedId = $el.attr("data-plan-allocated-id");
        var hactAllocatedId = $el.attr("data-hact-allocated-id");
        var loneWorkerId = $el.attr("data-loneworker-allocated-id");
        var timerId = $el.attr("data-timer-allocated-id");
        var switcherAllocatedId = $el.attr("data-switcher-allocated-id");
        var onDoneUrl = $el.attr("data-done-url");

        var $footer = $el.parent().find(footerSelector);

        function onCompleted() {
            if (onDoneUrl) {
                window.location.href = onDoneUrl;
            }
        }

        getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId).then( svcRec => {
            EvidenceDelegatingForm.loadAndAttach('r', $el, $footer, planAllocatedId, hactAllocatedId, loneWorkerId, timerId,
                switcherAllocatedId, svcRec, taskName, () => {}, onCompleted);
        });
    }
}
