import $ = require("jquery");
import OutcomesCreate = require("./OutcomesCreate");

import OutcomesFind = require("./OutcomesFind");
import {apiClient} from "ecco-components";
import {OutcomeAjaxRepository} from "../../service-config/OutcomeAjaxRepository";

var outcomeRepository = new OutcomeAjaxRepository(apiClient);

/**
 * The class which sets up the dashboards and controls
 */
class OutcomesView {

    private outcomesFind: OutcomesFind;
    private outcomesCreate: OutcomesCreate;

    public init() {
        this.outcomesFind = new OutcomesFind(outcomeRepository, $("#outcomesFind"));
        this.outcomesCreate = new OutcomesCreate(outcomeRepository, $("#outcomesCreate"));
    }

}

export = OutcomesView;
