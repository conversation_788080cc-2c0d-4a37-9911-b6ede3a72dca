import dto = require("./dto");
import {LdapRepository} from "./LdapRepository"
import URI = require("URI");
import {ApiClient} from "ecco-dto";


interface BooleanWrapper {
    ldapEnabled: boolean;
}

export class LdapAjaxRepository implements LdapRepository {
    constructor(private apiClient: ApiClient) {
    }

    public enableLdap(): Promise<boolean> {
        var path = URI("ldap/enabled/")
            .segmentCoded("");

        return this.apiClient.get<boolean>(path);
    }

    public listEntries(): Promise<dto.LdapGroupAggregateDto[]> {
        var path = URI("ldap/entries/")
            .segmentCoded("");

        return this.apiClient.get<dto.LdapGroupAggregateDto[]>(path);
    }

}
