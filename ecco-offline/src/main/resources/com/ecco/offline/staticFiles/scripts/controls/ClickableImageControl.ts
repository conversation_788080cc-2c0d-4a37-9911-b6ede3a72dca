import $ = require("jquery");
import Element = require("./Element");

class ClickableImageControl implements Element {

    private $img: $.JQuery;

    constructor(private imgClass: string, private onClick: (source: ClickableImageControl) => void) {
        this.$img = $("<span>").addClass(`clickable-image ${this.imgClass}`)
            .attr("role", "img");

        this.$img.click((event: $.JQueryMouseEventObject) => {
            this.onClick(this);
        });
    }

    public setSrc(imgClass: string) {
        this.$img.attr("class", `clickable-image ${imgClass}`);
    }

    public element() {
        return this.$img;
    }

}

export = ClickableImageControl
