import {CommandDtoSentEvent, CommandQueue, GoalUpdateCommand} from "ecco-commands";
import {Action, GoalUpdateCommandDto, SupportAction} from "ecco-dto";
import {EccoDate, StringUtils} from "@eccosolutions/ecco-common";
import {showErrorAsAlert} from "ecco-offline-data";
import {getGlobalEccoAPI} from "ecco-components";
import dynamicTree = require("../../draw/dynamic-tree");
import EditGoalForm = require("../smartsteps/EditGoalForm");
import GraphContext = require("./GraphContext");
import Modal = require("../../controls/Modal");
import ModalMode = require("../../controls/ModalMode");
import NodeProxy = require("./NodeProxy");
import AnnotationNode = dynamicTree.AnnotationNode;
import Node = dynamicTree.DynamicTreeNode;


class ActionNode implements NodeProxy {

    private node: Node;
    private scoreNode: AnnotationNode;

    constructor(private action: Action, private supportAction: SupportAction, private context: GraphContext) {
        var label = supportAction.goalName || action.getName();
        this.node = new Node(StringUtils.wrapString(label,20));
        this.node.addClickEventHandler( () => { this.showEditGoalForm();} );

        this.scoreNode = new AnnotationNode(supportAction.score && supportAction.score.toString() || "-");
        this.node.addAnnotation(this.scoreNode);
    }

    private showEditGoalForm() {
        const messages = getGlobalEccoAPI().sessionData.getMessages()
        // FIXME: Modal should be able to be private attr and reused, but show/hide/show means click handlers don't work!
        var editGoalModal = new Modal("modal-full z-raise", ModalMode.fillScreenHeight);

        var form = new EditGoalForm(this.supportAction, this.action, this.context,
            (commandQueue: CommandQueue) => {
                this.applyEditCommands(commandQueue)
                    .then(() => editGoalModal.dialogHide())
                    .catch(showErrorAsAlert);
            });

        editGoalModal.title("Edit " + messages["terminology.smartstep"]);
        editGoalModal.setFooter(form.getFooter());
        editGoalModal.popWithJQueryContent(form.element());
    }


    /** Called when editing - may want to make entirely event based. Note will fail (see above) not just due to failure to save to database */
    public applyEditCommands(commandQueue: CommandQueue): Promise<void> {

        var handler = (event: CommandDtoSentEvent) => this.applyEditCommand(event);

        commandQueue.addCommandDtoSentEventHandler(handler);
        return commandQueue.flushCommands().then(() => {
            commandQueue.removeCommandDtoSentEventHandler(handler);
        });
    }

    private applyEditCommand(event: CommandDtoSentEvent) {
        console.info("ActionNode: applying cmd: %s", JSON.stringify(event.command));

        if (event.command instanceof GoalUpdateCommand) {
            let cmd = <GoalUpdateCommand>event.command;
            // HACK: Just updating for now - we could use GoalUpdateEvent to trigger an update method
            // in BaseEvidenceControl which calls OutcomesControls which pushes the change through
            // ReferralNode through to ActionNode in order to just do the below
            // events.GoalUpdateEvent.bus.fire(
            //     new events.GoalUpdateEvent(cmd, cmd.getCommandDto())
            // );
            var goalUpdateCommandDto = <GoalUpdateCommandDto> cmd.toCommandDto();
            if (goalUpdateCommandDto.goalNameChange) {
                this.supportAction.goalName = goalUpdateCommandDto.goalNameChange.to;
                this.node.setCaption(StringUtils.wrapString(goalUpdateCommandDto.goalNameChange.to!, 20));
            }
            if (goalUpdateCommandDto.scoreChange) {
                this.setScore(goalUpdateCommandDto.scoreChange.to);
            }
            if (goalUpdateCommandDto.targetDateChange) {
                this.supportAction.targetDateTime = EccoDate.parseIso8601(goalUpdateCommandDto.targetDateChange.to)?.toDateTimeMidnight().formatIso8601() || null;
            }
        }
    }

    private setScore(score: number | null) {
        this.supportAction.score = score;
        this.scoreNode.setCaption(score ? score.toString() : "-");
    }

    public addAction(action: Action, supportAction: SupportAction): never {
        throw new Error("unsupported operation on ActionNode");
    }

    public withAllLeafNodes( callback: (leaf: ActionNode) => void): void {
        callback(this);
    }

    public getNode(): Node {
        return this.node;
    }

}
export = ActionNode;
