<!DOCTYPE html>
<html>
<head>
    <title>listdef-select2-test</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>


    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/data-attr/listdef-select2-test"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>

    <b>loads data</b>
    <div id="test1_all" class="listdef-select2-list1"
        data-name="sharedName1"
        data-initial-value="25"
        list-name="my-countries2"
        list-id="my-countries2"></div>
    <br/>

</body>
</html>
