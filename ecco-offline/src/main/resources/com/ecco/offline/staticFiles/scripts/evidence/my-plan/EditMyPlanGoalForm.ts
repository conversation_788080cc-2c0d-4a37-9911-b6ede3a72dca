
import * as commands from "ecco-commands";
import BaseCommandEmittingForm = require("../../controls/BaseCommandEmittingForm");
import {EvidencePageType, EvidenceGroup as EvidenceSourceType} from "ecco-dto";
import {EvidenceDef} from "ecco-evidence";
import InputGroup = require("../../controls/InputGroup");
import TextAreaInput = require("../../controls/TextAreaInput");
import {Uuid} from "@eccosolutions/ecco-crypto";


/** Beware HARD-CODED but also represented in the database as a new Outcome(39),ActionGroup(39),Action(99)
 *  UUID would be safer here - but we know that! */
const MY_PLAN_ACTION_ID = 99;

const evidenceDef = new EvidenceDef("myPlan", EvidencePageType.auditonly, EvidenceSourceType.auditonly);


interface FormFields {
    need?: string;
    objectives?: string;
    actions?: string;
    comments?: string;
}

/** Provides ability to add or edit a 'my plan' goal */
class EditMyPlanGoalForm extends BaseCommandEmittingForm {

    private needInput = new TextAreaInput("need");
    private objectivesInput = new TextAreaInput("objectives");
    private actionsInput = new TextAreaInput("actions");
    private commentsInput = new TextAreaInput("comments");

    /** We provide the form with the serviceRecipientId and and ServiceType, and we get back a CommandQueue
     *  containing the changes which have happened.
     * If we provide actionInstanceUuid, then we are editing a previous instance.
     */
    constructor(private serviceRecipientId: number, private actionInstanceUuid?: string, private latest: FormFields = {}) {
        super(actionInstanceUuid ? "edit entry" : "add new entry");
        this.form.append(new InputGroup(
            "What do I need? The identified need, choice or risk from the assessments (reference the particular assessment i.e. SURM-FM1/A&P-FM1)",
            this.needInput));
        this.form.append(new InputGroup(
            "What needs to happen? (objectives/outcomes) These are the achievable steps needed to meet the identified needs",
            this.objectivesInput));
        this.form.append(new InputGroup(
            "How will it happen? (actions/interventions) Clearly state what activities and interventions are to be put in place to fulfil the objective",
            this.actionsInput));
        this.form.append(new InputGroup(
            "Comments (include Recovery Star scores if appropriate) e.g. crisis contingency planning, safeguarding or breakdown of support",
            this.commentsInput));
        if (actionInstanceUuid) {
            this.needInput.setVal(latest.need);
            this.objectivesInput.setVal(latest.objectives);
            this.actionsInput.setVal(latest.actions);
            this.commentsInput.setVal(latest.comments);
        }
    }


    protected override submitForm() {
        // NOTE: We use custom GoalUpdateCommand that adds actionInstanceUuid ahead of ECCO-1310 being completed
        let addOrUpdate = this.actionInstanceUuid ? "update" : "add";
        let actionInstanceUuid = this.actionInstanceUuid ? Uuid.parse(this.actionInstanceUuid) : Uuid.randomV4();
        let workUuid = Uuid.randomV4();
        let uuid = Uuid.randomV4();

        let addGoalCmd =
                // evidenceDef.getTaskName() is "myPlan" which has changed from the previous argument of "my plan"
                // this actually doesn't matter since my plan is only stored and retreived using the group id which
                // hasn't changed - and the taskName is only in the command body and not used for my plan
                new commands.MyPlanUpdateCommand(addOrUpdate, uuid, workUuid, this.serviceRecipientId,
                        evidenceDef.getTaskName(), MY_PLAN_ACTION_ID, actionInstanceUuid)
                    .changeGoalName(this.latest.need, this.needInput.val())
                    .changeAnnotation('objectives', this.latest.objectives, this.objectivesInput.val())
                    .changeAnnotation('actions', this.latest.actions, this.actionsInput.val())
                    .changeAnnotation('comments', this.latest.comments, this.commentsInput.val());
        this.queueCommand(addGoalCmd);
        return super.submitForm();
    }
}

export = EditMyPlanGoalForm;
