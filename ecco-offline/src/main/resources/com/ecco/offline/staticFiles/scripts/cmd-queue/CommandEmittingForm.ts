import $ = require("jquery");
import CommandEmittingComponent = require("./CommandEmittingComponent");
import Element = require("../controls/Element");


interface CommandEmittingForm extends CommandEmittingComponent, Element {

    /** Title to be used as the label for an embedded control or the title for a dialog */
    title(): string;

    /**Element containing actions to put in the footer of the component that embeds this form. */
    getFooter(): $.JQuery;
}
export = CommandEmittingForm;