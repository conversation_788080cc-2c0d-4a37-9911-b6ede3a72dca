import $ = require("jquery");
import HtmlElement = require("./HtmlElement");

/**
 * A way of providing info in a consistent UX to the forms we generate
 * using 'form-horizontal'. We could incorporate into InputGroup.
 * See http://getbootstrap.com/css/index.html#forms-horizontal.
 *
 * This seems rather like ComponentUtils FieldGroup with labelClassName and controlClassName and implements
 * label + control that React Bootstrap used to do as deprecated Input component.
 */
class HorizontalInfoGroup extends HtmlElement {

    constructor(private label: string, private info: string) {
        super($("<div>"));

        // layout based on http://www.w3schools.com/bootstrap/bootstrap_forms_sizing.asp (shock)
        // also see http://getbootstrap.com/css/#forms
        var id = label+"Id";
        var $label = $("<label>").addClass("control-label").addClass("col-sm-5").attr("for", id).text(label);
        // remove the borer if we aren't really an input box
        const $info = $("<span>")
            .attr("id", id)
            .attr("class", "form-control")
            .css("border", "none")
            .css("box-shadow", "none")
            .text(info);
        var $inputControl = $("<div>").attr("class", "col-sm-3").append($info);
        var $formGroup = $("<div>").addClass("form-group").append($label).append($inputControl);
        this.element().append($formGroup);
    }

    /** ARCHIVE - some things we tried to get left/right horizontal form type things
    var $dl = $("<dl>").addClass("dl-horizontal");
    this.addSplitLine($dl, "started", this.reviewChoicesDto.currentReviewDate);
    this.addSplitLine($dl, "completed", txtCompleted + "%");
    this.$container.append($dl);
    private addDDLine($dl: $.JQuery, left: string, right: string) {
        var $dt = $("<dt>").text(left);
        var $dd = $("<dd>").text(right);
        $dl.append($dt).append($dd);
    }
    */

}

export = HorizontalInfoGroup;
