import _ = require("lodash");
import Dictionary = require("../Dictionary");
import URI = require("URI");

module mapping {

    function regExpEquals(a:RegExp, b:RegExp) {
        return a.source === b.source && a.global === b.global &&
                a.ignoreCase === b.ignoreCase && a.multiline === b.multiline;
    }

    class RegExpControllerMapping<T> {
        constructor(public regExp:RegExp) {
        }

        public mappings = new ControllerMappings<T>();
    }

    class RegExpControllerMappings<TController> {
        private mappings:RegExpControllerMapping<TController>[] = [];

        public registerController(regExp:RegExp, subPath:any[], controller: TController) {
            var mapping:RegExpControllerMapping<TController>;
            for (var i = 0; i < this.mappings.length; ++i) {
                if (regExpEquals(regExp, this.mappings[i].regExp)) {
                    mapping = this.mappings[i];
                }
            }

            if (!mapping) {
                mapping = new RegExpControllerMapping<TController>(regExp);
                this.mappings.push(mapping);
            }

            mapping.mappings.registerController(subPath, controller);
        }

        public resolveController(path:string[]): TController {
            var pathSegment = path[0];
            var controller: TController;

            for (var i = 0; i < this.mappings.length; ++i) {
                var mapping = this.mappings[i];
                if (mapping.regExp.test(pathSegment)) {
                    controller = mapping.mappings.resolveController(_.tail(path));
                    if (controller) {
                        return controller;
                    }
                }
            }

            return null;
        }
    }

    export class ControllerMappings<TController> {
        private staticMappings = new Dictionary<string, ControllerMappings<TController>>();
        private regExpMappings = new RegExpControllerMappings<TController>();
        private fileController: TController = null;
        private directoryController: TController = null;

        private static pathToPathSegments(path:any):any[] {
            if (_.isArray(path)) {
                return path;
            } else {
                var uri = URI(String(path)).normalize();

                if (uri.scheme() || uri.authority() || uri.query() || uri.fragment()) {
                    throw new Error("Invalid path");
                }

                return uri.segmentCoded();
            }
        }

        public registerController<TModel>(path: string, controller: TController);
        public registerController<TModel>(path: any[], controller: TController);
        public registerController<TModel>(path: any, controller: TController) {
            path = ControllerMappings.pathToPathSegments(path);
            var pathSegment = path[0];

            if (path.length === 0) {
                this.fileController = controller;
            } else if (_.isRegExp(pathSegment)) {
                this.regExpMappings.registerController(pathSegment, _.tail(path), controller);
            } else {
                pathSegment = String(pathSegment);

                if (pathSegment === "") {
                    if (path.length === 1) {
                        this.directoryController = controller;
                    } else {
                        throw new Error("Invalid path");
                    }
                } else if (pathSegment === "." || pathSegment === "..") {
                    throw new Error("Invalid path");
                } else {
                    var staticMapping: ControllerMappings<TController>;
                    if (this.staticMappings.containsKey(pathSegment)) {
                        staticMapping = this.staticMappings.get(pathSegment);
                    } else {
                        staticMapping = new ControllerMappings<TController>();
                        this.staticMappings.set(pathSegment, staticMapping);
                    }
                    staticMapping.registerController(_.tail(path), controller);
                }
            }
        }

        public resolveController(path:string[]): TController {
            if (path.length === 0) {
                return this.fileController;
            }

            var pathSegment = path[0];

            if (pathSegment === "") {
                if (path.length === 1) {
                    return this.directoryController;
                } else {
                    throw new Error("Invalid path");
                }
            }

            if (this.staticMappings.containsKey(pathSegment)) {
                var controller = this.staticMappings
                        .get(pathSegment)
                        .resolveController(_.tail(path));
                if (controller) {
                    return controller;
                }
            }

            return this.regExpMappings.resolveController(path);
        }
    }
}
export = mapping;