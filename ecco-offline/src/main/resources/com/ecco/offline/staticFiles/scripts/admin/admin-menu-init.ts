import cacheCommands = require("./cache/commands");
import $ = require("jquery");
import services = require("ecco-offline-data");
import FeatureConfigForm = require("./feature-config/FeatureConfigForm");
import {delay} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {AclAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {CacheCommand} from "./cache/commands";

const commandRepository = services.getCommandQueueRepository();


const aclAjaxRepository = new AclAjaxRepository(apiClient);

export function clearCacheLinkEnhance() {
    const cacheLink = $("#cache-configuration-link");
    const origCacheLinkText = cacheLink.text();
    cacheLink
            .click(() => {
                const cmd = new cacheCommands.CacheCommand(CacheCommand.CONFIG);
                cacheLink.text("... clearing config cache ...");
                commandRepository.sendCommand(cmd)
                        .then(() => {
                            cacheLink.text("... ... cleared config cache");
                            delay(2000).then(() => {
                                cacheLink.removeClass("bold").text(origCacheLinkText);
                            })
                        })
                        .catch((e) => {
                            alert("config cache clear: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                            throw e; // allows .fail to log the error on console and throw for NR
                        });
            })
            .css("cursor", "pointer");
}

$(function () {
    $("#feature-configuration-link")
            .click( () => {
                const form = new FeatureConfigForm();
                showFormInModalDom(form);
                form.load();
            })
            .css("cursor", "pointer");

    clearCacheLinkEnhance();

    $("#acl-ensure-link")
            .click(function () {
                aclAjaxRepository.ensureAcls()
                    .then(() => {
                        alert("acl ensure: successful");
                    })
                    .catch((e) => {
                        alert("acl ensure: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                        throw e; // allows .fail to log the error on console and throw for NR
                    });
            })
            .css("cursor", "pointer");

    const entriesLink = $("#acl-apply-link");
    const origEntriesLinkText = entriesLink.text();
    entriesLink
        .click(function () {
            entriesLink.text("... applying (please wait) ...");
            aclAjaxRepository.ensureAclEntries()
                .then(() => {
                    entriesLink.text("... ... success");
                    delay(2000).then( () => {
                        entriesLink.text(origEntriesLinkText);
                    })
                })
                .catch((e) => {
                    alert("acl ensure entries: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                    throw e; // allows .fail to log the error on console and throw for NR
                });
        })
        .css("cursor", "pointer");

});
