import {EccoDate, ReloadEvent} from "@eccosolutions/ecco-common";
import {ServiceRecipientAttributeChangeCommand} from "ecco-commands";
import {LoadingSpinner, useServicesContext, useStaff} from "ecco-components";
import {createStyles, makeStyles, Theme} from "@eccosolutions/ecco-mui";

import {default as React, useEffect, useState} from "react";
import ControlWrapper, {LoadableControl} from "../../components/ControlWrapper";
import {componentAsElementForMui} from "../../components/MUIAsElement";
import StaffLocationControl from "../../hr/StaffLocationControl";

export function staffOverviewPaneAsElement(srId: number, workerId: number) {
    return componentAsElementForMui(
        <StaffOverviewPane srId={srId} workerId={workerId} />,
        "staffOverview");
}

const useStyles = makeStyles((theme: Theme) => // NOTE: Must always have root:
    createStyles({
        root: {
            // marginTop: theme.spacing(2),
            // marginBottom: theme.spacing(5),
            // display: "block"
        }
    })
);

export function StaffOverviewPane({srId, workerId}: { srId: number, workerId: number }) {
    const {getCommandRepository} = useServicesContext();
    const [locationControl, setLocationControl] = useState<LoadableControl>();

    const attributeTextChange = (path: string, oldValue: string, newValue: string) => {
        const cmd = new ServiceRecipientAttributeChangeCommand(srId, "text", path);
        cmd.changeValue(oldValue, newValue);
        if (cmd.hasChanges()) {
            getCommandRepository().sendCommand(cmd).then(() => {
                ReloadEvent.bus.fire();
                return;
            });
        }
    };

    const attributeDateChange = (path: string, oldValue: EccoDate | null, newValue: EccoDate | null) => {
        const cmd = new ServiceRecipientAttributeChangeCommand(srId, "date", path);
        cmd.changeValue(oldValue?.formatIso8601(), newValue?.formatIso8601());
        if (cmd.hasChanges()) {
            getCommandRepository().sendCommand(cmd).then(() => {
                ReloadEvent.bus.fire();
                return;
            });
        }
    };

    useEffect(() => {
            const location = new StaffLocationControl(srId, workerId, true);
            setLocationControl(location);
        },
        [srId]);

    const {staff} = useStaff(workerId);

    // @ts-ignore
    const classes = useStyles(); // WTF: Why is this different to when used elsewhere
    return !staff ? <LoadingSpinner/> : <>
        <div className="MuiFormControl-root">
            <label
                style={{top: "auto", left: "auto"}}
                className="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiFormLabel-filled">
                primary location
            </label>
            {locationControl && <div style={{marginTop: 20}}>
                <ControlWrapper control={locationControl} className="" // deliberately override default classname
                />
            </div>
            }
        </div>
        {/*<ClickToEditTextField
            className={classes.root}
            label={"employee number"} value={staff.textMap ? staff.textMap["employeeNumber"] : null}
            onChange={newValue =>
                attributeTextChange('workerJob.worker.textMap["employeeNumber"]', staff.textMap ? staff.textMap["employeeNumber"] : null, newValue)}/>
        <ClickToEditTextField
            className={classes.root}
            label={"dbs number"} value={staff.crbNumber}
            onChange={newValue =>
                attributeTextChange('workerJob.worker.crbNumber', staff.crbNumber, newValue)}/>*/}
    </>;
}