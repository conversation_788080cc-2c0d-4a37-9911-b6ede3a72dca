import $ = require("jquery");
import ListDefSelect2List = require("./ListDefSelect2List");
import {apiClient} from "ecco-components";
import {SessionDataAjaxRepository} from "ecco-dto";

var repositoryDefault = new SessionDataAjaxRepository(apiClient);

/**
 * Enhance with select controls with backing data from cfg_list_definitions.
 */
class Enhancer {

    constructor() {
    }

    /** Find the items we support and attach the appropriate component to them */
    public attach() {
        var $roots = $(".listdef-select2-list");
        $roots.each( (index, element) => {
            new ListDefSelect2List($(element), repositoryDefault).load();
        });
    }

}

var enhancer = new Enhancer();
enhancer.attach();
