{
    "extends": "../tsconfig-base.json",
    "compilerOptions": {
        "allowSyntheticDefaultImports": true,
        "noEmit": true,
        "module": "AMD",
        "moduleResolution": "Node",
        // Doesn't seem to be read when run via gulp, but need for running via IDEA
        "esModuleInterop": true,
        "target": "ES5",
        "baseUrl": "",
        "paths": {
            "*": ["*", "./typings/*"]
        },
        "lib": [
            "ES2017",
            "DOM",
            "DOM.Iterable"
        ],
        "alwaysStrict": true,
        "jsx": "react",
        "strictPropertyInitialization": false // TODO: can switch to true when start using strictNullChecks
    },
    "references": [
        { "path": ".." }
    ]
}
