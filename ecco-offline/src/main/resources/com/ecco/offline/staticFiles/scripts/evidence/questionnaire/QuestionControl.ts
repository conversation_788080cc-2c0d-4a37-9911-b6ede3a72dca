import $ = require("jquery");

import BaseControl = require("../../controls/BaseControl");
import {Question, QuestionAnswerChoice, QuestionAnswerFreeType} from "ecco-dto/service-config-dto";
import RadioGroupInput = require("../../controls/RadioGroupInput");
import TextInput = require("../../controls/TextInput");
import TextAreaInput = require("../../controls/TextAreaInput");
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../../common/validation";
import CheckboxGroupInput = require("../../controls/CheckboxGroupInput");
import {QuestionAnswerTransientEvent, SessionData} from "ecco-dto";
import SelectList from "../../controls/SelectList";

class QuestionControl extends BaseControl {

    private initialAnswer: string = undefined; // be explicit that no value is undefined
    private inputControl: BaseControl;
    private getValue: () => string = () => null; // FIX undefined, eg markdown, causing error on save

    private errors = new ValidationErrors("");
    private validationCallback: (valid: boolean, message?: string) => void = () => {};
    private $helpBlock: $.JQuery;
    private onChange: (questionDefId: number, answer: string) => void;

    constructor(private sessionData: SessionData, private question: Question,
            onChange?: (questionDefId: number, answer: string) => void) {
        super($("<div>").addClass("form-group"));
        // easier if there is always an onChange
        this.onChange = onChange
            ? onChange
            : (questionDefId: number, answer: string) => {};
    }

    public render(): void {
        this.element().empty();

        const type = SessionData.questionType(this.question, false);

        // if 'markdown' then show across the space
        if (type == "markdown") {
            const $label = $("<div>").addClass("col-sm-12").append(this.question.name);
            this.element().append($label);

        } else {

            this.$helpBlock = $("<span>").addClass("help-block pull-right").css("margin-right", "15px");
            const $labelText = $("<label>").addClass("control-label").width("100%").text(this.question.name);
            // we shouldn't need to wrap the label in a div, but it works
            const $label = $("<div>").addClass("col-sm-4").append($labelText);
            const $input = $("<div>").addClass("col-sm-8");
            this.validationCallback = (valid: boolean, message?: string) => {
                valid
                        ? super.element().removeClass("has-error")
                        : super.element().addClass("has-error");
                this.$helpBlock.html(message);
            };

            this.element().append($label);
            this.element().append($input);
            this.element().append(this.$helpBlock);

            this.inputControl = this.createInputControl();
            $input.append(this.inputControl.element());
        }
    }

    private createInputControl(): BaseControl {
        var type = SessionData.questionType(this.question, false);
        switch (type) {
            case "choices":
                return this.createRadioOptions();
            case "integer":
                return this.createTextBox(false, 'integer'); // historical oddity
            case "money":
                return this.createTextBox(false, 'money');
            case "number":
                return this.createTextBox(false, 'number');
            case "textarea":
                return this.createTextBox(true, 'textarea');
            case "text":
                return this.createTextBox(false, 'text');
            case "checkbox":
                return this.createCheckbox();
            case "date":
                return this.createTextBox(false, 'text');
            case "list":
                return this.createDropDownList();
            case "markdown":
                return null;
        }
        throw new Error("misconfigured question: " + this.question.id);
    }

    /**
     * Apply an initial value. Any value is treated as a value, except null and undefined.
     */
    public applyValue(answer: string) {
        // don't allow null - its meaningless and has potential to cause issues
        if (answer !== null) {
            this.initialAnswer = answer;
        }
    }

    /**
     * RADIO INPUT
     */
    private createRadioOptions(): BaseControl {
        const answerChoices = new RadioGroupInput("choicesForQn" + this.question.id, "radio-inline");

        // before populateFromList and onChange
        if (this.question.answerRequired) {
            answerChoices.withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors, this.validationCallback);
        }

        // hmmmm.. it appears we need to set this here to take effect? (ie not late binding)
        answerChoices.change((answer: string) => {
            QuestionAnswerTransientEvent.bus.fire(
                new QuestionAnswerTransientEvent(this.getQuestion().id, this.getValue()));
                return this.onChange(this.question.id, answer);
        });
        this.getValue = () => answerChoices.getValueOrNull(this.hasInitialAnswer());

        answerChoices.populateFromList(
            this.question.choices.filter(c => !c.disabled),
            (item: QuestionAnswerChoice) => {
                return {
                    id: `${this.question.id}-${item.id}`,
                    label: item.displayValue,
                    value: item.value.toString(),
                    selected: item.value === this.initialAnswer
                };
            }
        );

        if (this.question.answerRequired) {
            answerChoices.enableValidation();
        }

        return answerChoices;
    }

    /**
     * TEXT INPUT
     */
    private createTextBox(large: boolean, type: QuestionAnswerFreeType): BaseControl {
        const ctlId = "questionId:" + this.question.id.toString();
        const answerTextBox = large
            ? new TextAreaInput(ctlId, undefined, undefined, "form-control") // goes red with validation
            : new TextInput(ctlId, undefined, "form-control");

        // NB this triggers a 'change' so, although the command should handle a non-change
        // we may as well set the 'change' function after it
        if (this.hasInitialAnswer()) {
            answerTextBox.setVal(this.initialAnswer);
        }

        let validationChecks = new ValidationChecksBuilder();
        if (this.question.answerRequired) {
            validationChecks.addCheck(ValidationCheck.Required);
        }
        if (type == 'money') {
            validationChecks.addCheck(ValidationCheck.Money);
        }
        if (type == 'number') {
            validationChecks.addCheck(ValidationCheck.Number);
        }
        if (validationChecks.hasChecks()) {
            answerTextBox.withValidationChecks(validationChecks, this.errors, this.validationCallback);
        }

        answerTextBox.change((answer: string) => {
            QuestionAnswerTransientEvent.bus.fire(
                new QuestionAnswerTransientEvent(this.getQuestion().id, this.getValue()));
            return this.onChange(this.question.id, answer);
        });

        this.getValue = () => answerTextBox.getValueOrNull(this.hasInitialAnswer());

        if (validationChecks.hasChecks()) {
            answerTextBox.enableValidation();
        }

        return answerTextBox;
    }

    /**
     * LIST INPUT
     */
    private createDropDownList(): BaseControl {
        const ctlId = "questionId:" + this.question.id.toString();

        const emptyText = "-";
        const listControl = new SelectList(`${ctlId}-list`, undefined, emptyText);
        listControl.withEmptyEntryValue(null);
        const answerId = this.initialAnswer != null ? parseInt(this.initialAnswer) : null;
        listControl.populateFromList(this.sessionData.getListDefinitionEntriesByListName(this.question.parameters.listName, undefined, answerId || true),
                (ct) => ({ key: ct.getId().toString(), value: ct.getDisplayName(), isHidden: ct.getDisabled(), isDefault: false, readOnly: false }),
                (ct) => this.initialAnswer == ct.getId().toString());

        if (this.question.answerRequired) {
            //listGroup
            listControl
                    .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors, this.validationCallback)
                    .enableValidation();
        }

        listControl.change((answer: string) => {
            QuestionAnswerTransientEvent.bus.fire(
                    new QuestionAnswerTransientEvent(this.getQuestion().id, this.getValue()));
            return this.onChange(this.question.id, answer);
        });

        this.getValue = () => {
            const v = listControl.getValueOrNull(this.hasInitialAnswer());
            return v == emptyText ? null : v;
        }

        return listControl;
    }

    /**
     * CHECKBOX INPUT
     */
    private createCheckbox(): BaseControl {
        // we use a group to get the <fieldset> for validation, as with other inputs
        // even though this group only has one checkbox in it
        const grpCtlId = "grpQnId:" + this.question.id.toString();
        const answerCheckboxes = new CheckboxGroupInput(grpCtlId);

        // odd to force a tickbox, but it could be some regulatory thing
        if (this.question.answerRequired) {
            answerCheckboxes.withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors, this.validationCallback)
                .enableValidation();
        }

        answerCheckboxes.change((value: string, state: boolean) => {
            QuestionAnswerTransientEvent.bus.fire(
                new QuestionAnswerTransientEvent(this.getQuestion().id, this.getValue()));
            return this.onChange(this.question.id, state.toString());
        });

        this.getValue = () => {
            const values = answerCheckboxes.getValuesOrNull(this.hasInitialAnswer());
            return values
                ? values[0].getValueOrNull(this.hasInitialAnswer())
                : null;
        };

        const ctlId = "questionId:" + this.question.id.toString();
        // initialAnswer will be undefined or a value (not null, since we don't apply null in applyValue)
        const singleCheckbox: any = {id: ctlId, value: this.hasInitialAnswer() ? this.initialAnswer.toLowerCase() == "true" : false};
        answerCheckboxes.populateFromList(
            [singleCheckbox],
            (item: {id: string, value: boolean}) => {
                return {
                    value: item.id,
                    id: item.id,
                    label: "",
                    selected: item.value,
                    readOnly: false
                };
            }
        );

        return answerCheckboxes;
    }

    public isValid(): boolean {
        return this.errors.isValid();
    }

    public getQuestion() {
        return this.question;
    }

    /**
     * Get the string representation of the answer value,
     * or null if no answer has been selected
     */
    public getAnswer(): string {
        return this.getValue();
    }

    /**
     * If we have not touched the answer, then false.
     * NB Its possible to change the answer then clear it a
     */
    public hasAnswerChanged() {
        return this.hasInitialAnswer()
            // compare the values
            // getValue can be the value selected or null if no answer
            // (we can have an initialAnswer and null getValue if the initialAnswer could not be applied to radio buttons)
            // (so use double equals to allow null == undefined being true)
            ? this.getValue() !== this.initialAnswer
            // if no initialAnswer, compare against null to see if we have a value
            : this.getValue() !== null;
    }

    /**
     * See if we provided an initial value.
     * NB The initial value could be set as empty/null - undefined is our indication for unset.
     */
    public hasInitialAnswer() {
        // triple-equals to catch null == undefined being true, but we have safeguarded against null initialValue
        return this.initialAnswer !== undefined;
    }
    public getInitialAnswer() {
        return this.initialAnswer;
    }
}

export = QuestionControl;