import $ = require("jquery");
import commands = require("../../service-config/commands");

import events = require("../../common/events");
import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import TableControl = require("../../controls/TableControl");
import TableRow = require("../../controls/TableRow");
import {AdminMode, SparseArray} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {ActivityType, ServiceDto} from "ecco-dto/service-config-dto";
import {EntityRestrictionsAjaxRepository} from "../../entity-restrictions/EntityRestrictionsAjaxRepository";
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import {adminModeEnabled} from "ecco-dto";

var activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(apiClient);
var entityRestrictionRepository = new EntityRestrictionsAjaxRepository(apiClient.withCachePeriod(0));

class BackingData {
    constructor(public activityTypes: ActivityType[], public services: ServiceDto[]) {
    }
}

class ManageServiceActivitiesControl extends BaseAsyncCommandForm<BackingData> {

    private footer: $.JQuery;

    public static showInModal() {
        var form = new ManageServiceActivitiesControl();
        showFormInModalDom(form);
        form.load();
    }

    private commandsByActivityId: SparseArray<commands.ListWithServicesChangeCommand> = {};

    constructor() {
        super("manage activity types");
        this.setOnFinished( () => {} );
    }

    fetchViewData(): Promise<BackingData> {
        return activityTypeRepository.findAllActivityTypes()
            .then( (activityTypes) => {
                return entityRestrictionRepository.findRestrictedServicesProjects()
                    .then( services => {
                        return new BackingData(activityTypes, services);
                    });
            });
    }

    private renderCell(service: ServiceDto, activityType: ActivityType) {
        var selected = (activityType.serviceIds.some( (serviceId) => serviceId == service.id ) );
        return $("<td>").text(selected ? "Y" : "-");
    }

    private activityChange(service: ServiceDto, activityType: ActivityType, checked: boolean) {
        var cmd = this.commandsByActivityId[activityType.id];
        if (!cmd) {
            cmd = new commands.ListWithServicesChangeCommand("config/activityType/", "update", activityType.id);
            this.commandsByActivityId[activityType.id] = cmd;
        }
        if (checked) {
            cmd.addService(service.id);
        }
        else {
            cmd.removeService(service.id);
        }
        this.commandQueue.addCommand(cmd);
        if (this.commandQueue.size() > 0) {
            this.enableSubmit();
        }
    }

    private onCellClick(service: ServiceDto, activity: ActivityType, $cell: $.JQuery) {
        if (!adminModeEnabled()) {
            return
        }
        var wasFalse = $cell.text() == "-";
        this.activityChange(service, activity, wasFalse);
        $cell.text(wasFalse ? "Y" : "-");
    }

    render(data: BackingData) {
        this.element().empty();

        var table = new TableControl<ActivityType,ServiceDto>(data.activityTypes,
            (activity) => activity.name, (service) => service.name);
        this.append(table);
        data.services.forEach( (service) => {
            var row = new TableRow<ActivityType, ServiceDto>(service.name, undefined, service,
                    (r,c) => this.renderCell(r,c),
                    data.activityTypes, false,
                    (r,c,$cell) => this.onCellClick(r,c,$cell));
            table.appendRow(row);
        });

        this.footer = this.getFooter();

        const $nav = $("<div>")
                .append($("<p>").append("manage settings for: service mappings", this.footer))
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $nav) );

        this.setAdminMode(adminModeEnabled());

        AdminMode.bus.addHandler(event => {
            this.setAdminMode(event.enabled);
        });

    }

    private setAdminMode(adminMode: boolean) {
        if (adminMode) {
            this.footer.show();
        } else {
            this.footer.hide();
        }
    }

}
export = ManageServiceActivitiesControl;
