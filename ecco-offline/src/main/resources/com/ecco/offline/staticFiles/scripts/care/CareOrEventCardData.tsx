import {EccoDateTime} from "@eccosolutions/ecco-common";
import {CardData} from "ecco-components";
import {
    EventResourceDtoWithUserCalendar,
    getGlobalApiClient,
    getRelation
} from "ecco-dto";
import {Activity, calendarHrefToCalendarId, REL_RUN_BREAKDOWN} from "ecco-rota";

export class CareOrEventCardData implements CardData {
    constructor(public dto: EventResourceDtoWithUserCalendar) {
    }

    getKey() {
        return "careevent-" + this.dto.uid
    }

    getPriority() {
        return EccoDateTime.parseIso8601(this.dto.start as string);
    }

    isCareRun = () => Activity.isCareRun(this.dto)

    getCareRunBreakdown = () => careRunBreakdownOf(this.dto).then(events => events.filter(e => !Activity.isCareRun(e)))
}

function careRunBreakdownOf(event: EventResourceDtoWithUserCalendar) {
    const apiClient = getGlobalApiClient();
    return apiClient.fetchRelation<EventResourceDtoWithUserCalendar[]>(event, REL_RUN_BREAKDOWN).then(events => events.map(e => {
        const href = getRelation(event, REL_RUN_BREAKDOWN).href;
        // set the events inside the run to have the run's calendarId so we can pretend its the ownerCalendarId
        // otherwise we get the events where the run is an attendee - and therefore we only have attendee info
        // which means we can't get the event 'shift breakdown' link to determine it
        // see SRRotaDecorator and 5af3c56d
        e.requestedCalendarId = calendarHrefToCalendarId(href)
        return e
    }));
}
