import URI = require("URI");
import {Mergeable} from "ecco-dto";

import * as cmdDtos from "ecco-dto/command-dto";
import * as commands from "ecco-commands";


/** see CacheCommandViewModel.java */
interface CacheCommandDto extends cmdDtos.UpdateCommandDto {
    all: boolean;
    cacheNames: string[] | null;
    messageSources: boolean;
}

export class CacheCommand extends commands.BaseUpdateCommand {

    public static CONFIG = ["config"];
    public static ADMIN = ["admin"];
    private cacheNames: string[];

    constructor(cacheNames: string[]) {
        super(URI("cache")
            .segmentCoded("").toString());
        this.cacheNames = cacheNames;
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public toDto(): CacheCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    all: !this.cacheNames,
                    cacheNames: this.cacheNames,
                    messageSources: false
        });
    }
}
