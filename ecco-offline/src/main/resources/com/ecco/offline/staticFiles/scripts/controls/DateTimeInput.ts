import $ = require("jquery-bundle");
import BaseControl = require("./BaseControl");
import environment = require("../environment");
import {bus, EccoDateTime, EventBus, EventHandler} from "@eccosolutions/ecco-common";
import * as bowser from "bowser";
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../common/validation";

// Test if the browser provides a native HTML 5 date picker.
const nativeDatePicker = (bowser.mobile || bowser.tablet || bowser.chrome || bowser.firefox) && $("<input>")
    .attr("type", "datetime-local")
    .prop("type") == "datetime-local";

class DateTimeInput extends BaseControl {
    private static CSS_CLASS = "date-input";

    private $input: $.JQuery;
    private changeEventBus: EventBus<EccoDateTime>;

    /**
     * Derived from DateInput (TODO: These could be refactored to have a common base class)
     *
     * @param date to prime the datepicker with
     * @param bootstrap whether the ecco date picker uses bootstrap styling
     *
     * NB bootstrap applies to non-nativeDatePicker ONLY currently because we need to allow an 'inline' style
     * so it can be used for SupportInstanceControl.ts.
     */
    constructor(date?: EccoDateTime, id?: string, bootstrap?: boolean) {
        var $input: $.JQuery;
        if (nativeDatePicker) {
            $input = $("<input>")
                .attr("id", id)
                .attr("name", id)
                .attr("type", "datetime-local") // Poss deprecated: See note at http://caniuse.com/#search=date
                .addClass(DateTimeInput.CSS_CLASS);

            if (date) {
                $input.val(date.formatIso8601());
            }

            super($input);
        }
        else {
//            var $hiddenInput = $("<input>")
//                .attr("type", "hidden")
//                .attr("name", id);

            $input = $("<input>")
                .attr("id", id)
                .attr("type", "text");

            if (bootstrap) {
                $input.addClass("form-control");
            }

            var $container;
            if (!bootstrap) {
                // span was used in legacy days as a way to wrap the input and button in the same row
                $container = $("<span>")
                    .addClass(DateTimeInput.CSS_CLASS)
//                    .append($hiddenInput)
                    .append($input);
            } else {
                // input-group allows us to use input-group-addon for the icon
                $container = $("<div>").addClass("input-group");
                // see http://getbootstrap.com/components/index.html#input-groups-buttons
                // we replace the jquery datepicker icon with one at the end of the bootstrap input
                // which simply sets focus on the input to popup the datepicker
                // (the jquery datepicker handles more - like if $blockUI is used etc)
                var $addOn = $("<div>").addClass("input-group-addon datepicker-addon");
                var $img = $("<div>").addClass("datepicker clickable-image");
                $addOn.click( (event: $.JQueryEventObject) => {
                            $(event.delegateTarget).closest(".input-group").find(".form-control").focus();
                        });
                $addOn.append($img);
                $container = $container
//                    .append($hiddenInput)
                    .append($input).append($addOn);
            }

            super($container);

            // we don't use the datepickers button if we are using bootstrap
            var showOn = bootstrap ? "focus" : "both";

            (<any>$input).datetimepicker({
                autoSize: false,
                dateFormat: 'dd/mm/yy',
//                altField: $hiddenInput, // update this with the selected date
//                altFormat: (<any>$).datepicker.ISO_8601,
                yearRange: 'c-01:c+10', // i.e. currently selected year - 1 : + 10
                changeMonth: true,
                changeYear: true,
                stepMinute: 1,
                showOn: showOn, // button and focus
                showButtonPanel: true, // today/now & done buttons
                buttonImage: environment.imagesBase + "datepicker.png",
                buttonImageOnly: true, // just the image, not a button wrapped around an image
                onSelect: () => this.onChange(),

                // Stupid hack to make datepicker work in conjunction with Bootstrap-modal.
                // See: https://github.com/jschr/bootstrap-modal/issues/239
                beforeShow: () => {
                    // JQuery UI sets the z-index of the datepicker immediately
                    // after this function returns, so we arbitrarily delay for one
                    // more turn of the event loop using setTimeout.
                    setTimeout(() => $(".ui-datepicker").css("z-index", "10000"), 0);
                },
// TODO: Is this needed??
                onClose: (dateText: string, instance: any) => {
                    var trimmed = $.trim(dateText);
                    this.$input.val(trimmed);
                    this.onChange();
                }
            });

            if (date) {
                (<any>$input).datetimepicker("setDate", date.toLocalJsDate());
            }
        }

        $input.change(() => this.onChange());
        this.$input = $input;
        this.changeEventBus = bus<EccoDateTime>();
    }

    public getDate(): EccoDateTime {
        if (nativeDatePicker) {
            var val = this.$input.val();
            val = val.replace(/^\s*|\s*$/g, "");
            return EccoDateTime.parseIso8601(val);
        } else {
            var date = (<any>this.$input).datetimepicker("getDate");
            return date ? EccoDateTime.fromLocalJsDate(date) : null;
        }
    }

    public isValid() {
        return this.getDate() != null;
    }

    public validate(field: string, checks: ValidationChecksBuilder, errors: ValidationErrors) {

        if (checks.isChecked(ValidationCheck.Required)) {
            errors.requireNotNull(field, this.getDate());
        }

        if (this.getDate() != null && checks.isChecked(ValidationCheck.NotFuture)) {
            // add 2 hours grace into the check - to avoid BST issues and devices being slightly out
            if (this.getDate().laterThan(EccoDateTime.nowLocalTime().addHours(2))) {
                errors.addError(field, ValidationCheck.NotFuture);
            }
        }

    }

    public setDate(date: EccoDateTime): void {
        if (nativeDatePicker) {
            if (date) {
                this.$input.val(date.formatIso8601());
            } else {
                this.$input.val("");
            }
        } else {
            if (date) {
                (<any>this.$input).datetimepicker("setDate", date.toLocalJsDate());
            } else {
                (<any>this.$input).datetimepicker("setDate", null);
            }
        }
        this.onChange();
    }

    public change(handler: EventHandler<EccoDateTime>): DateTimeInput {
        this.addChangeEventHandler(handler);
        return this;
    }

    public addChangeEventHandler(handler: EventHandler<EccoDateTime>): void {
        this.changeEventBus.addHandler(handler);
    }

    public removeChangeEventHandler(handler: EventHandler<EccoDateTime>): void {
        this.changeEventBus.removeHandler(handler);
    }

    private onChange() {
        this.changeEventBus.fire(this.getDate());
    }
}

export = DateTimeInput;
