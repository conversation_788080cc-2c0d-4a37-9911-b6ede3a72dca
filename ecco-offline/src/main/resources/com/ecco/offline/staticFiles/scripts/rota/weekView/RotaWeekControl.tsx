import $ = require("jquery");
import BaseControl = require("../../controls/BaseControl");
import TableControl = require("../../controls/TableControl");
import TableRow = require("../../controls/TableRow");

import SessionData = featuresDomain.SessionData;
import StringToObjectMap = types.StringToObjectMap;
import * as types from "@eccosolutions/ecco-common";
import {EccoDate} from "@eccosolutions/ecco-common";
import * as featuresDomain from "ecco-dto";
import {Activity, ActivityAllocatedEvent, ActivityDeallocatedEvent, DemandResource, Rota} from "ecco-rota";
import {EventSnapshotDto} from "ecco-dto";

class WorkerJobTableRow extends TableRow<EccoDate, DemandResource> {

    protected override initAfterConstruct() {
        this.row.addActivityAllocatedEventHandler(
            (event: ActivityAllocatedEvent) => this.render() );
        this.row.addActivityDeallocatedEventHandler(
            (event: ActivityDeallocatedEvent) => this.render() );
    }

    override renderTotal(): $.JQuery {
        // this.row is a workerJob
        const totalHrs = Rota.calculateHours(this.row.getAppointments());
        return $("<td>").text(totalHrs.toFixed(2));
    }
}

class UnassignedActivitiesTableRow extends TableRow<EccoDate, Rota> {

    private activityAllocatedHandler = (event) => { this.onAllocated(event.getActivity()); };

    private initialised = false;

    private init() {
        if (!this.initialised) {
            this.row.getAllActivities().forEach( (appt) => {
                appt.addActivityAllocatedEventHandler( this.activityAllocatedHandler );
            });
            this.initialised = true;
        }
    }

    override renderTotal(): $.JQuery {
        this.init();
        let totalHrsUnallocated = 0;
        let totalHrsDropped = 0;
        this.row.getUnallocatedOrDroppedActivities().forEach( (appt) => {
            if (appt.isDropped()) {
                totalHrsDropped += appt.getEnd().subtractDateTime(appt.getStart()).inHours();
            } else {
                totalHrsUnallocated += appt.getEnd().subtractDateTime(appt.getStart()).inHours();
            }
        });
        const totalUnassignedOrDropped = "unassigned " + totalHrsUnallocated.toFixed(2)
            + " + dropped " + totalHrsDropped.toFixed(2)
            + " = " + (totalHrsUnallocated + totalHrsDropped).toFixed(2);

        const totalAll = Rota.calculateHours(this.row.getAllActivities());
        return $("<td>").append(
            $("<span>").text(totalUnassignedOrDropped)
                .append($("<br/><br/>"))
                .append($("<span>").text(totalAll.toFixed(2)))
        );
    }

    private onAllocated(appt: Activity) {
        appt.removeChangeEventHandler(this.activityAllocatedHandler);
        this.render();
    }
}

/**
 * The badge/labels at the bottom of the rota for hours per appointment type across the whole rota.
 * Keep track of total hours as we render appts
 */
class AppointmentSummary extends BaseControl {
    private totalHrs = 0;

    constructor(private apptType: string, labelClasses: string) {
        super($("<span>"));
        this.element()
            .addClass(labelClasses).text(apptType + ": 0");
    }

    addAppointment(appt: Activity) {
        this.totalHrs += Rota.calculateHours([appt]);
        this.element().text(this.apptType + ": " + this.totalHrs.toFixed(2));
    }
}

const CONTAINER_CLASS = "rota-week top-gap-15";
const ALLOCATED_CLASS = "label label-success";
const UNALLOCATED_CLASS = "label label-warning";
const DROPPED_CLASS = "label label-default";
const AVAILABLE_CLASS = "label label-info";
const NONROTA_CLASS = "label label-default";

function getCssForDate(col: EccoDate) {
    return col.equals(EccoDate.todayLocalTime())
        ? "date-today"
        : col.isWeekend() ? "date-weekend" : "";
}

function createAvailabilitySpan(text: string, labelClasses: string) {
    return $("<span>")
        .addClass(labelClasses)
        .attr("title", "availability")
        .text(text);
}

function createEventSpan(appt: Activity, labelClasses: string) {
    const start = appt.getStart().formatHoursOptionalMinutes();
    const end = appt.getEnd()?.formatHoursOptionalMinutes() || "";
    const allDay = appt.isAllDay();
    const desc = allDay
        ? 'all day ' + (appt.getEvent() ? appt.getEvent() : "? unavailable all day") // TODO: Work out what events cause all day but no event data
        : start + "-" + end + " " + (appt.getEvent() ? appt.getEvent() : "? unavailable");
    return $("<span>")
        .addClass(labelClasses)
        .text(desc)
        .attr("title", appt.getEvent() );
}



export class RotaWeekView extends BaseControl {
    private rota: Rota | null = null;
    private recurringAllocate: boolean;

    private summaries: StringToObjectMap<AppointmentSummary>;
    private $summariesList: $.JQuery;

    constructor(
        private onActivityClick: (activity: Activity) => void,
        private onResourceClick: (resource: DemandResource, showAvailability?: boolean | undefined) => void,
        private sessionData: SessionData,
        // Callback for React to show the modal
        private addAppointment: (workerJob: DemandResource, date: EccoDate) => void
    ) {
        super();
        this.element().addClass(CONTAINER_CLASS);
    }

    getRota() {
        return this.rota;
    }

    /** Update and re-render the rota if it has changed - like React props */
    renderRota(rota: Rota, liveOverlay?: boolean | undefined, liveStatus?: EventSnapshotDto[] | undefined) {
        if (rota !== this.rota) {
            this.rota = rota;
            this.render();
        }
    }

    /** Update and re-render the rota if it has changed - like React props */
    updateRotaDemand(rota: Rota) {
        this.renderRota(this.rota.withAddedDemandFrom(rota));
    }

    updateRecurringAllocation(recurringAllocate: boolean) {
        this.recurringAllocate = recurringAllocate;
    }

    private renderWorkerRowCell(workerJob: DemandResource, col: EccoDate) {
        const css = getCssForDate(col);

        const $td = $("<td>").addClass(css);
        $td.append(
            $("<i>").addClass("add-appt fa fa-plus-circle clickable pull-right")
                .click( () => this.addAppointment(workerJob, col) )
        );

        const apptsToday = workerJob.getAppointments()
            .filter((appt) => appt.appearsOnDate(col))
            .sort((a, b) => a.getStart().compare(b.getStart()));

        let descAvail = workerJob.getAvailability().describeAvailabilityForDate(col);
        if (descAvail) {
            $td.append(
                createAvailabilitySpan("a: " + descAvail, "availability " + AVAILABLE_CLASS)
            );
        }

        apptsToday.forEach( (appt) => {
            if (!appt.isNonRota()) {
                const labelClasses = ALLOCATED_CLASS;
                $td.append(this.createAppointmentSpan(appt, labelClasses));
                this.updateSummary(appt, labelClasses);
            }
            else {
                const $appt = createEventSpan(appt, NONROTA_CLASS);
                $td.append($appt);
            }
        });

        return $td;
    }


    private renderUnassigned(col: EccoDate) {
        const css = getCssForDate(col);
        const $td = $("<td>").addClass(css);

        const apptsToday = this.rota.getUnallocatedOrDroppedActivities()
            .filter((appt) => appt.getStart().toEccoDate().equals(col))
            .sort((a, b) => a.getStart().compare(b.getStart()));

        apptsToday.forEach( (appt) => {
            $td.append(this.createAppointmentSpan(appt, appt.isDropped() ? DROPPED_CLASS : UNALLOCATED_CLASS) );
        });
        return $td;
    }

    // private onLabelClick(workerJob: DemandResource) {
    //     this.onResourceClick(workerJob, true);
    // }

    private createAppointmentSpan(appt: Activity, labelClasses: string) {
        const start = appt.getStart().formatHoursOptionalMinutes();
        const end = appt.getEnd().formatHoursOptionalMinutes();
        return $("<span>")
            .addClass("clickable " + labelClasses).text(start + "-" + end + " " + appt.getServiceRecipientName())
            .attr("title", appt.getEvent() + " [" + appt.getServiceRecipientName() + "]")
            .click( () => this.onActivityClick(appt));
    }

    private updateSummary(appt: Activity, labelClass: string) {
        const apptType = this.sessionData.getAppointmentTypeByName(appt.getEventTypeName());
        if (!this.summaries[apptType.name]) {
            const summary = new AppointmentSummary(apptType.name, labelClass);
            this.summaries[apptType.name] = summary;
            this.$summariesList.append(summary.element());
        }
        this.summaries[apptType.name].addAppointment(appt);
    }

    render() {
        const rota = this.rota;

        this.summaries = {};
        this.$summariesList = $("<div>").css({"padding-bottom": "25px"});
        const weekDates: EccoDate[] = [];
        for (let i = 0; i < 7; i++) {
            weekDates.push(this.rota.getDate().addDays(i));
        }

        const tableControl = new TableControl<EccoDate, DemandResource|Rota>(weekDates,
            (col) => col.formatShortWithDay(), (row)=>row.toString(), null, "total" );
        rota.getResources().forEach( (workerJob) => {
            const row = new WorkerJobTableRow(workerJob.getName(),
                workerJob.getContractedWeeklyHours() && workerJob.getContractedWeeklyHours().toString() + "hrs",
                workerJob,
                (r,c) => this.renderWorkerRowCell(r,c),
                weekDates, true,
                null, 255);
            row.onLabelClick( () => {
                this.onResourceClick(workerJob, true);
                // show avail only for clicked worker
                //tableControl.element().find("td > span.availability").hide();
                //row.element().find("td > span.availability").show();
            });
            tableControl.appendRow(row);
        });

        if (rota.getAllActivities().length > 0) {
            const unassigned = new UnassignedActivitiesTableRow("unassigned & dropped", undefined, rota,
                (r, c) => this.renderUnassigned(c),
                weekDates, true,
                null, 255);
            tableControl.appendRow(unassigned);
        }

        this.element()
            .empty()
            .append( $("<div>").addClass("container-fluid")
                .append(tableControl.element())
                .append(this.$summariesList)
            );
    }
}
