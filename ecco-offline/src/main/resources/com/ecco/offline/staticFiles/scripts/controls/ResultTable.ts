import $ = require("jquery");
import {StringToObjectMap} from "@eccosolutions/ecco-common";

/**
 *   var table = new ResultTable(["r-id", "c-id", "firstName", "lastName", "state"])
 *      .cellRenderer("r-id", referralId => $("<td>").append($("<a>").attr("href", "..").text(referralId)));
 *   table.attach($("#results"));
 *   table.populate([{'r-id': 100, 'c-id': 201, 'firstName': 'Fred', 'lastName': 'Bloggs', 'state': 'new'}]);
 */
class ResultTable {

    /** A useful cell renderer to re-use in numerous places */
    public static referralIdCellRenderer(referralId: number) {
        return $("<td>").append(
            $("<a>")
                .attr("target", "_blank")
                .attr("href", "../referralFlow.html?referralId=" + referralId)
                .text(referralId));
    }

    /** Render cell with link to client page with embedded referrals */
    public static clientLinkCellRenderer(clientId: number) {
        return $("<td>").append(
            $("<a>")
                .attr("target", "_blank")
                .attr("href", "../entity/clients/" + clientId)
                .text(clientId));
    }

    private $container: $.JQuery;
    private $tbody = $("<tbody>");
    private renderers: StringToObjectMap<(val: string|number) => $.JQuery> = {};

    constructor( private fields: string[], private classes?: string) {
    }

    public attach($container: $.JQuery) {
        this.$container = $container;
    }

    public populate(resultSet: StringToObjectMap<any>[]) {
        this.render();
        this.append(resultSet);
    }

    public render() {
        this.$container.empty();
        this.empty()

        var $table = $("<table>").addClass(this.classes);
        this.$container.append($table);
        this.renderHeader();
        $table.append(this.renderHeader());
        $table.append(this.$tbody);
    }

    public cellRenderer(field: string, renderer: (val: string|number) => $.JQuery) {
        this.renderers[field] = renderer;
        return this;
    }

    public append(resultSet: StringToObjectMap<string|number>[]) {
        resultSet.forEach(result => {
            this.$tbody.append(this.renderRow(result));
        });
    }

    public empty() {
        this.$tbody.empty();
    }

    public renderHeader(): $.JQuery {
        var $head = $("<thead>");
        var $tr = $("<tr>");
        $head.append($tr);

        this.fields.forEach(field => {
            $tr.append($("<th>").text(field));
        });
        return $head;
    }

    public renderRow(result: StringToObjectMap<string|number>): $.JQuery {
        var $tr = $("<tr>");

        this.fields.forEach(field => {
            $tr.append(this.renderCell(field, result));
        });
        return $tr;
    }

    private renderCell(field: string, result: StringToObjectMap<string|number>) {
        var renderer = this.renderers[field];
        if (renderer) {
            return renderer(result[field]);
        }
        return $("<td>").text(result[field]);
    }
}

export = ResultTable;
