import $ = require("jquery");
import {Activity} from "ecco-rota";


interface ActivitiesGroupControlOptions {
    numTimeSlots: number;
    activities: Activity[];  // includes assigned activities too
    onActivityClick: (activity: Activity) => void
    labelText: string;
    labelActionText?: string;
    labelAction?: () => void;
    cssClass: string;
    $dragContainment: $.JQuery;
    onActivityDragStart: (activity: Activity, ctrlKey: boolean) => void;
    onActivityDragEnd: (activity: Activity) => void;
}

export = ActivitiesGroupControlOptions;
