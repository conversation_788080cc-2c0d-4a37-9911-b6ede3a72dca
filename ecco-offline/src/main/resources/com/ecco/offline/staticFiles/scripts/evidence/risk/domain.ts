import * as cfgDomain from "ecco-dto";
import commands = require("./commands");
import RiskUpdateCommand = commands.RiskUpdateCommand;
import * as riskDto from "ecco-dto/evidence-risk-dto";
import * as dto from "ecco-dto/evidence-dto";

// match GenTypeThreatOutcome
export class RiskAreaEvidence {

    /** Sparse array of actions by id */
    private riskActionEvidenceByRiskActionInstanceUuid: { [id: string]: riskDto.RiskActionEvidenceDto } = {};

    private latest: riskDto.RiskGroupEvidenceDto;

    private matrixScore = 0;

    constructor(private riskGroup: cfgDomain.RiskArea){
    }

    public addRiskAreaEvidence(evidence: riskDto.RiskGroupEvidenceDto) {
        this.latest = evidence; // May not be the most sane
    }

    /** This receives all RiskActionEvidenceDto history for a RiskArea
     * but is currently ordered by workDate then created (descending order).
     * So we could get different smart steps in the same piece of work,
     * or previous updates to the same smart step.
     * The result picture wants to be the latest for each smart step
     * which involves keeping an index of them.
     * Then we need to retain the maximum score from the latest of the different smart steps
     */
    public addRiskActionEvidence(riskActionEvidence: riskDto.RiskActionEvidenceDto) {

        var alreadyExists = this.riskActionEvidenceByRiskActionInstanceUuid[riskActionEvidence.actionInstanceUuid];
        if (!alreadyExists) {
            this.riskActionEvidenceByRiskActionInstanceUuid[riskActionEvidence.actionInstanceUuid] = riskActionEvidence;
        }

        if (riskActionEvidence.likelihood && riskActionEvidence.severity) {
            if (alreadyExists && alreadyExists.likelihood && alreadyExists.severity) {
                // given the desc ordering, we just take the first value of each smart step, ignoring the rest
                return;
            } else {
                var individualMatrixScore = riskActionEvidence.likelihood * riskActionEvidence.severity;
                // take the latest highest score of any smart step in the current risk area
                if (individualMatrixScore > this.matrixScore) {
                    this.matrixScore = individualMatrixScore;
                }
            }
        }


    }

    isEmpty() {
        return !this.latest;
    }

    getLevelMeasure() {
        return this.latest.levelMeasure; // TODO: when we mutate this, don't refer to teh original item (clone it)
    }

    getLevel() {
        return this.latest.level; // TODO: when we mutate this, don't refer to teh original item (clone it)
    }

    getRiskGroup() {
        return this.riskGroup;
    }

    getMatrixScore() { return this.matrixScore; }

    getRiskActionEvidenceByRiskActionInstanceUuid() {
        return this.riskActionEvidenceByRiskActionInstanceUuid;
    }
}

/** This is a mutable snapshot built from iterating over risk work and commands */
export class RiskEvidenceSnapshot {

    private riskAreaEvidenceByRiskAreaId: { [id: number]: RiskAreaEvidence } = {};

    private flagEvidenceByFlagId: {[id: number]: dto.FlagEvidenceDto} = {};

    /** For iterating over - must be same object as above ByOutcomeId */
    private riskAreaEvidences = new Array<RiskAreaEvidence>();

    private riskAreaEvidenceByOutcomeId: {[id: number]: riskDto.RiskGroupEvidenceDto} = {};

    constructor(private serviceType: cfgDomain.ServiceType) {
        serviceType.getRiskAreas().forEach( (riskArea) => {
            var riskAreaEvidence = new RiskAreaEvidence(riskArea);
            this.riskAreaEvidenceByRiskAreaId[riskArea.getId()] = riskAreaEvidence;
            this.riskAreaEvidences.push(riskAreaEvidence);
        });
    }

    public updateFromWorkEvidence(allWork: riskDto.RiskWorkEvidenceDto[]) {
        allWork.forEach( (work) => {
            work.riskActions.forEach( (action) => this.addRiskActionEvidence(action) );
            work.riskAreas.forEach( (area) => this.addAreaEvidence(area) );
            work.flags.forEach( (flag) => this.addFlagEvidence(flag) );
        });
    }

    public addRiskActionEvidence(action: riskDto.RiskActionEvidenceDto) {
        var riskArea = this.serviceType.getRiskAreaForRiskActionId(action.actionId);
        // check the current service type has the action configured in the outcome - if not, we can't add it
        if (riskArea) {
            var riskAreaId = riskArea.getId();
            this.riskAreaEvidenceByRiskAreaId[riskAreaId].addRiskActionEvidence(action);
        }
    }

    public addFlagEvidence(flagEvidence: dto.FlagEvidenceDto) {
        if (!this.flagEvidenceByFlagId[flagEvidence.flagId]) {
            this.flagEvidenceByFlagId[flagEvidence.flagId] = flagEvidence;
        }
    }

    public addAreaEvidence(areaEvidence: riskDto.RiskGroupEvidenceDto) {
        // check the current service type has the action configured in the outcome - if not, we can't add it
        if (!this.riskAreaEvidenceByOutcomeId[areaEvidence.riskAreaId]) {
            this.riskAreaEvidenceByOutcomeId[areaEvidence.riskAreaId] = areaEvidence;
        }
    }

    public applyCommand(command: RiskUpdateCommand) {
    }

    public addRiskAreaEvidence(evidence: riskDto.RiskGroupEvidenceDto) {
        var areaFromId = this.riskAreaEvidenceByRiskAreaId[evidence.riskAreaId];
        // check the current service type has the action configured in the outcome - if not, we can't add it
        if (areaFromId) {
            areaFromId.addRiskAreaEvidence(evidence);
        }
    }

    getRiskAreaEvidences() {
        return this.riskAreaEvidences;
    }

    /**
     *
     */
    getLatestRiskActionEvidence() {
        var latest = new Array<riskDto.RiskActionEvidenceDto>();
        this.riskAreaEvidences.forEach( (riskAreaEvidence) => {
            var latestActionsByIds = riskAreaEvidence.getRiskActionEvidenceByRiskActionInstanceUuid();
            for (var id in latestActionsByIds) {
                latest.push(latestActionsByIds[id]);
            }
        });
        return latest;
    }

    getLatestFlagEvidenceByFlagId(): {[id: number]: dto.FlagEvidenceDto} {
        return this.flagEvidenceByFlagId;
    }

    /**
     *
     */
    getLatestRiskAreaEvidenceByOutcomeId(): { [id: number]: riskDto.RiskGroupEvidenceDto } {
        return this.riskAreaEvidenceByOutcomeId;
    }
}
