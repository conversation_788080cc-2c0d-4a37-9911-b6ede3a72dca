package com.ecco.dom.managedvoids;

import java.util.UUID;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;

@Entity
@Table(name = "bldg_mvoid_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("update")
public class ManagedVoidCommand extends BaseIntKeyedCommand {

    @Column(nullable=false)
    private Integer managedVoidId;

    public ManagedVoidCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                         long userId, @Nonnull String body, Integer managedVoidId) {
        super(uuid, remoteCreationTime, userId, body);
        this.managedVoidId = managedVoidId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected ManagedVoidCommand() {
        super();
    }

    public Integer getManagedVoidId() {
        return managedVoidId;
    }
}
