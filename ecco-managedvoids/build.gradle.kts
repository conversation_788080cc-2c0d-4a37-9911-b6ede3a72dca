
plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-config"))
    implementation(project(":ecco-service-config"))
    implementation(project(":ecco-security"))
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-evidence"))
    implementation(project(":ecco-buildings"))
    implementation("joda-time:joda-time:2.10.8")

    testImplementation(project(":test-support"))
    testImplementation(project(":test-entities"))
}

description = "ecco-managedvoids"
