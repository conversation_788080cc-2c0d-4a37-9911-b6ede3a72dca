package com.ecco.webApi.calendar;

import com.ecco.calendar.EventDecorator;
import com.ecco.calendar.msgraph.MsEventToEventResource;
import com.ecco.calendar.msgraph.MsGraphCalendarService;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.Individual;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.web.WebApiConfigBase;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.service.EventService;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.webapi.EventResource;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.hamcrest.MockitoHamcrest;
import org.springframework.context.ApplicationEvent;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.ZoneId;
import java.util.Collections;
import java.util.List;

import static java.util.Collections.emptyList;
import static org.hamcrest.Matchers.contains;
import static org.joda.time.DateTimeConstants.DAYS_PER_WEEK;
import static org.joda.time.DateTimeConstants.MONDAY;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * The only test for our EventController and EventService
 */
public class EventControllerTest {
    private static final String WORKER_CALENDAR_ID = "worker-cal-1";
    private static final DateTime START_OF_WEEK = new DateTime().withDayOfWeek(MONDAY).withMillisOfDay(0);
    private static final DateTime END_OF_WEEK = START_OF_WEEK.plusDays(DAYS_PER_WEEK);

    private IndividualRepository individualRepository;
    private EventService eventService;
    private MsGraphCalendarService msGraphService;
    private EventController.SecurityUtilProvider securityProvider;
    private MessageBus<ApplicationEvent> messageBus;
    private EventController controller;
    private User worker;

    private static String msGraphEventStr = "{\"attendees\":[],\"body\":{\"content\":\"\",\"contentType\":\"html\"},\"bodyPreview\":\"\",\"categories\":[],\"changeKey\":\"3dc3bPh5dEGPAmPiw3CgwQAAQSyUDQ==\",\"createdDateTime\":\"2021-08-16T18:37:54.663285400Z\",\"end\":{\"dateTime\":\"2021-08-19T00:00:00\",\"timeZone\":\"UTC\"},\"hasAttachments\":false,\"iCalUId\":\"040000008200E00074C5B7101A82E008000000007C4998D3CD92D70100000000000000001000000042125027CF1AF04E9A9FD13C963FE847\",\"id\":\"AQMkADAwATM0MDAAMS0zMwA5Ny0wMWU4LTAwAi0wMAoARgAAA0pDHJkg1zBLjmKC24tLHu8HAN3XN2z4eXRBjwJj4sNwoMEAAAIBDQAAAN3XN2z4eXRBjwJj4sNwoMEAAABBLoaBAAAA\",\"importance\":\"normal\",\"isAllDay\":true,\"isCancelled\":false,\"isOrganizer\":true,\"isReminderOn\":true,\"lastModifiedDateTime\":\"2021-08-16T18:39:55.918562700Z\",\"location\":{\"address\":{\"city\":null,\"countryOrRegion\":null,\"postalCode\":null,\"state\":null,\"street\":null},\"displayName\":\"\",\"locationEmailAddress\":null},\"onlineMeetingUrl\":null,\"organizer\":{\"emailAddress\":{\"address\":\"<EMAIL>\",\"name\":\"ECCO Client\"}},\"originalEndTimeZone\":\"UTC\",\"originalStart\":null,\"originalStartTimeZone\":\"UTC\",\"recurrence\":null,\"reminderMinutesBeforeStart\":420,\"responseRequested\":true,\"responseStatus\":{\"response\":\"organizer\",\"time\":\"0001-01-01T00:00:00Z\"},\"sensitivity\":\"normal\",\"seriesMasterId\":null,\"showAs\":\"free\",\"start\":{\"dateTime\":\"2021-08-18T00:00:00\",\"timeZone\":\"UTC\"},\"subject\":\"eccoclient-1\",\"type\":\"singleInstance\",\"webLink\":\"https://outlook.live.com/owa/?itemid=AQMkADAwATM0MDAAMS0zMwA5Ny0wMWU4LTAwAi0wMAoARgAAA0pDHJkg1zBLjmKC24tLHu8HAN3XN2z4eXRBjwJj4sNwoMEAAAIBDQAAAN3XN2z4eXRBjwJj4sNwoMEAAABBLoaBAAAA&exvsurl=1&path=/calendar/item\"}";

    @Before
    public void setUp() {
        individualRepository = mock(IndividualRepository.class);
        ReferralRepository referralRepository = mock(ReferralRepository.class);
        eventService = mock(EventService.class);
        msGraphService = mock(MsGraphCalendarService.class);
        securityProvider = mock(EventController.SecurityUtilProvider.class);
        messageBus = mock(MessageBus.class);
        EntityUriMapper entityUriMapper = mock(EntityUriMapper.class);
        ServiceRecipientRepository serviceRecipientRepository = mock(ServiceRecipientRepository.class);

        EventDecorator decorator = new ServiceRecipientEventDecorator(entityUriMapper,
                referralRepository, serviceRecipientRepository, mock(ServiceRecipientSummaryService.class), mock(SoftwareFeatureService.class), mock(ServiceRecipientRotaDecorator.class), mock(ApplicationProperties.class));

        EventResourceAssembler eventResourceAssembler = new EventResourceAssembler(decorator, EventController.class);

        controller = new EventController(eventService, individualRepository, msGraphService, securityProvider, eventResourceAssembler, messageBus);

        MockHttpServletRequest request = new MockHttpServletRequest();
        worker = new User();
        worker.setContact(new Individual());
        worker.getContact().setCalendarId(WORKER_CALENDAR_ID);
        ServletRequestAttributes requestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(requestAttributes);
    }

    @Test
    public void convertFromMsToEcco() throws JsonProcessingException {
        ObjectMapper objectMapper = new WebApiConfigBase(){}.objectMapper();
        var mse = objectMapper.readValue(msGraphEventStr, com.ecco.calendar.msgraph.data.Event.class);
        var r = new MsEventToEventResource(1L, Collections.singletonList("calendar1")).apply(mse);
        assertEquals(mse.iCalUId, r.getEntryId());
        assertEquals("[ms] "+mse.subject, r.getTitle());
        assertEquals(mse.isAllDay, r.isAllDay());
        var startLondon = mse.start.dateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(EccoTimeUtils.LONDON).toLocalDateTime();
        var endLondon = mse.end.dateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(EccoTimeUtils.LONDON).toLocalDateTime();
        assertEquals(JodaToJDKAdapters.localDateTimeToJoda(startLondon), r.getStart());
        assertEquals(JodaToJDKAdapters.localDateTimeToJoda(endLondon), r.getEnd());
    }

    @Test
    public void whenNoContactIdsGivenThenEmptyListReturned() {
        final List<EventResource> resourceList = controller.findCalendarEventsByContactIds(0L, System.currentTimeMillis(), new Long[0], emptyList());
        assertEquals(0, resourceList.size());
    }

    @Test
    public void givenCalendarIsEmptyThenEmptyListReturned() {
        when(eventService.getCalendars((List<String>) MockitoHamcrest.argThat(contains(WORKER_CALENDAR_ID)), eq(START_OF_WEEK), eq(END_OF_WEEK)))
                .thenReturn(Collections.emptySet());

        final List<EventResource> resourceList = controller.findCalendarEventsByContactIds(START_OF_WEEK.getMillis(), END_OF_WEEK.getMillis(), new Long[]{1L}, emptyList());
        assertEquals(0, resourceList.size());

    }

}
