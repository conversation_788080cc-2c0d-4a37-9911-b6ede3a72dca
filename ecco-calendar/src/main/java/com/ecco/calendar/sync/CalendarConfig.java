package com.ecco.calendar.sync;

import com.ecco.calendar.core.CalendarNonRecurringService;
import com.ecco.infrastructure.bus.MessageBus;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CalendarConfig {
    @Bean
    public CalendarUserSyncAgent calendarUserSyncAgent(CalendarNonRecurringService calendarService, MessageBus<ApplicationEvent> messageBus) {
        return new CalendarUserSyncAgent(calendarService, messageBus);
    }
}
