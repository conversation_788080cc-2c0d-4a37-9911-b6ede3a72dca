package com.ecco.webApi.calendar;

import com.ecco.buildings.dom.BuildingServiceRecipient;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.calendar.core.Attendee;
import com.ecco.calendar.core.Entry;
import com.ecco.calendar.core.RecurringEntry;
import com.ecco.calendar.core.webapi.EventAttendee;
import com.ecco.calendar.core.webapi.EventResource;
import com.ecco.dao.DemandSchedulePredicates;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.web.UriUtils;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import lombok.AllArgsConstructor;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.util.UriTemplate;

import java.util.Optional;

import static org.springframework.hateoas.server.core.SpringAffordanceBuilder.DISCOVERER;

/**
 * Decorates the resource hateoas links based on its uri information.
 */
@AllArgsConstructor
public class ServiceRecipientRotaDecorator<T extends RepresentationModel<? extends T>> {
    private static final Logger log = LoggerFactory.getLogger(ServiceRecipientRotaDecorator.class);
    public static final String REL_EDIT = "edit";
    public static final String REL_ROTAVISIT = "rota visit";
    public static final String REL_RISK_FLAGS = "risk flags";
    public static final String REL_RUN_BREAKDOWN = "carerun-breakdown";
    public static final String REL_DEMAND_SCHEDULE = "demand-schedule";
    public static final String REL_DEMAND_SCHEDULE_ADDITIONAL = "demand-schedule-additional";
    public static final String REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS = "demand-schedule-additional-events";
    public static final String REL_DEMAND_SCHEDULE_TASK_HANDLES = "demand-schedule-task-handles";
    public static final String REL_CALENDAR_EVENTS = "calendar-events";

    private final ReferralRepository referralRepository;
    private final FixedContainerRepository fixedContainerRepository;
    private final ServiceRecipientSummaryService serviceRecipientSummaryService;
    private final ServiceRecipientRepository serviceRecipientRepository;
    private final DemandScheduleRepository demandScheduleRepository;
    private final ApplicationProperties appConfig;

    /**
     * NB see attendeeOwner in careSingleVisitDataLoader.tsx
     * We need to find the person for the visit out of the attendees.
     * This approach simply finds the owner of the event because demand is the owner.
     */
    public static Optional<EventAttendee> attendeeOwner(EventResource event) {
        return event.getAttendees().stream().filter(attendee ->
                attendee.calendarId.equals(event.getOwnerCalendarId())
            ).findFirst();
    }
    public static Optional<Attendee> attendeeOwner(Entry event) {
        return event.getAttendees().stream().filter(attendee ->
                attendee.getCalendarId().equals(event.getOwnerCalendarId())
        ).findFirst();
    }
    public void rotaEventLinks(RepresentationModel<T> resource, Long scheduleId, LocalDateTime start, LocalDateTime end) {

//            resource.add(linkToSvcRecSummary(srId).withRel(REL_SVC_REC_SUMMARY));
        resource.add(linkToDemandSchedule(scheduleId).withRel(REL_DEMAND_SCHEDULE));

        Integer srId = serviceRecipientRepository.findServiceRecipientIdForDemandSchedule(scheduleId);
        var sr = serviceRecipientSummaryService.findOne(srId);

        // supply an 'edit' and 'visit' (or 'shift breakdown' for care runs)
        // visit is allowed for referrals, or a building that isn't a run
        final Long referralId = referralRepository.getReferralIdByServiceRecipientId(srId);
        var isAppointmentNotCareRun = false;
        if (referralId != null) {
            resource.add(ServiceRecipientEventDecorator.linkToReferralOverviewBySrId(UriUtils.hostProvidedLinkBuilder(this.appConfig), srId).withRel(REL_EDIT));
            resource.add(linkToRotaVisit(srId).withRel(REL_ROTAVISIT));
            //resource.add(linkToIndividual(referralId).withRel(REL_ROTAVISIT));
            isAppointmentNotCareRun = true;
        } else {
            if (sr.discriminator.equals(BuildingServiceRecipient.DISCRIMINATOR)) {
                // if resourceType=care run (158) then we want as shift breakdown
                var bldg = fixedContainerRepository.findByServiceRecipient_Id(srId).get();
                // the care app loads events, so this is used to get events inside the shift
                if (bldg.getResourceTypeId() == DemandSchedulePredicates.CARERUN_RESOURCE_ID) {
                    resource.add(linkToEventsByDate(sr.calendarId, start, end).withRel(REL_RUN_BREAKDOWN));
                    // not required currently - was used to get the breakdown of a run (added in e13e1541)
                    //resource.add(linkToBuildingOverview(bldg.getParent().getId()).withRel(REL_EDIT));
                } else {
                    // we are not a run, so we assume we are a building demand
                    resource.add(linkToBuildingOverview(bldg.getId()).withRel(REL_EDIT));
                    resource.add(linkToRotaVisit(bldg.getId()).withRel(REL_ROTAVISIT));
                    isAppointmentNotCareRun = true;
                }
            }
        }

        // supply the 'attending with..' information
        // TODO This data may be best on the dto (eg RotaAppointmentViewModel) itself, if we want this information offline
        if (isAppointmentNotCareRun) {

            // link to the client's calendarId (the owner) which is used to filter out for the worker from the attendees
            // this link could be used to find other additionalStaff appointments, but it gets all events so would need to filter
            // 'rota visit' and also look inside breakdowns
            resource.add(linkToEventsByDate(sr.calendarId, start, end).withRel(REL_CALENDAR_EVENTS));

            // direct tasks for the schedule (for the app, and for editing a schedule)
            resource.add(linkToDirectTaskHandles(scheduleId).withRel(REL_DEMAND_SCHEDULE_TASK_HANDLES));

            // We can look at its demandschedule to see the 'additionalStaff' but this only works if we are the parent schedule.
            //      NB editing the schedule in AppointmentSchedulesTable only shows parent schedules, and the number of additional 'staff' are the count of parentScheduleId.
            //      All the schedules contribute to the rota - see RotaScheduleAPITest#schedules_createUpdateWithAdditionalStaff, and javadoc in RotaServiceImpl#addOneAppointmentSchedule.
            //      Therefore, this schedule could be the parent, in which case we get the children, or the child, in which case we get the parent's children.
            // So we can load the parent to get this number, but we can provide links to the related schedules to give the
            // count and the scheduleId which could be compared to the 'managed by' of already loaded rota data to match
            // appointments with other staff.
            // However, it feels safer not to rely on already loaded data given the flexibility of the rota, and therefore
            // we provide the links to the appointments on the schedule itself, at the correct time.

            // get the parentScheduleId without loading the schedule - in an effort to be quick (though the schedule may already be cached)
            var parentScheduleIdOfSchedule = demandScheduleRepository.findParentScheduleIdFromId(scheduleId);
            var parentScheduleIdForRelated = parentScheduleIdOfSchedule != null ? parentScheduleIdOfSchedule : scheduleId;
            var relatedScheduleIds = demandScheduleRepository.findIdsByParentScheduleId(parentScheduleIdForRelated);
            // add the parent with the children, since all are joined
            relatedScheduleIds.add(parentScheduleIdForRelated);

            // if we find additionalStaff, take some time to get the info we need for each one
            relatedScheduleIds.stream().filter(s -> !scheduleId.equals(s)).forEach(s -> {
                // link to the scheduleId can allow for comparisons to 'managed-by' on existing loaded rota data, but this is not utilised
                resource.add(linkToDemandSchedule(s).withRel(REL_DEMAND_SCHEDULE_ADDITIONAL));
                var schedule = demandScheduleRepository.findOne(s);
                // link to the additionalStaff appointments for the same date/time to get the status and worker attendee
                resource.add(linkToEntryByDate(schedule.getRecurringEntryHandle(), start, end).withRel(REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS));
            });
        }

        // Always useful to have risk flags
        resource.add(linkToRiskFlags(srId).withRel(REL_RISK_FLAGS));
    }

    @SuppressWarnings("ConstantConditions")
    public LinkBuilder linkToDemandSchedule(Long scheduleRef) {
        try {
            var type = Class.forName("com.ecco.webApi.rota.AgreementController");
            var method = ReflectionUtils.findMethod(type,"getAppointmentScheduleById", (Class<?>[]) null);
            var mapping = DISCOVERER.getMapping(type, method);
            UriTemplate template = new UriTemplate(mapping);
            return UriUtils.hostRelativeThenProvidedLinkBuilder(this.appConfig).slash("api").slash(template.expand(scheduleRef));
        } catch (ClassNotFoundException e) {
            log.error("Failed to lookup ServiceRecipientController.findOne() class to add 'risk flags' link for Event");
            return null;
        }
    }

    @SuppressWarnings("ConstantConditions")
    public LinkBuilder linkToIndividual(int contactId) {
        try {
            var type = Class.forName("com.ecco.webApi.rota.IndividualController");
            var method = ReflectionUtils.findMethod(type,"getOne", (Class<?>[]) null);
            var mapping = DISCOVERER.getMapping(type, method);
            UriTemplate template = new UriTemplate(mapping);
            return UriUtils.hostProvidedLinkBuilder(this.appConfig).slash("api").slash(template.expand(contactId));
        } catch (ClassNotFoundException e) {
            log.error("Failed to lookup findOn Individual");
            return null;
        }
    }

    @SuppressWarnings("ConstantConditions")
    public LinkBuilder linkToRiskFlags(Integer srId) {
        try {
            var type = Class.forName("com.ecco.webApi.evidence.RiskEvidenceController");
            var method = ReflectionUtils.findMethod(type,"findLatestByServiceRecipientId", (Class<?>[]) null);
            var mapping = DISCOVERER.getMapping(type, method);
            UriTemplate template = new UriTemplate(mapping);
            return UriUtils.hostProvidedLinkBuilder(this.appConfig).slash("api").slash(template.expand(srId));
        } catch (ClassNotFoundException e) {
            log.error("Failed to lookup RiskEvidenceController.findLatestByServiceRecipientId() class to add 'risk flags' link for Event");
            return null;
        }
    }

    @SuppressWarnings("ConstantConditions")
    public LinkBuilder linkToDirectTaskHandles(Long scheduleId) {
        try {
            var type = Class.forName("com.ecco.webApi.rota.AgreementController");
            var method = ReflectionUtils.findMethod(type,"getDemandScheduleDirectTaskHandles", (Class<?>[]) null);
            var mapping = DISCOVERER.getMapping(type, method);
            UriTemplate template = new UriTemplate(mapping);
            return UriUtils.hostProvidedLinkBuilder(this.appConfig).slash("api").slash(template.expand(scheduleId));
        } catch (ClassNotFoundException e) {
            log.error("Failed to lookup AgreementController.getDemandScheduleDirectTaskHandles() class to add 'direct task handles' link for Event");
            return null;
        }
    }

    private LinkBuilder linkToEventsByDate(String calendarId, LocalDateTime start, LocalDateTime end) {
        // eg. http://localhost:8080/ecco-war/api/calendar/event/search??calendarId={sr.calendarId}&start={resource.start}&end={resource.end}
        return new UriUtils.ExplicitLinkBuilder(
                UriUtils.hostRelativeThenProvidedComponentBuilder(this.appConfig).path("/api/calendar/event/search")
                        .queryParam("calendarId", calendarId)
                        .queryParam("startTime", start.toString())
                        .queryParam("endTime", end.toString())
                        .build());
    }

    private LinkBuilder linkToEntryByDate(RecurringEntry.RecurringEntryHandle recurringEntryHandle, LocalDateTime start, LocalDateTime end) {
        // eg. http://localhost:8888/ecco-war/api/rota/-/schedule/{eventRef}/?startDate=...&endDate=...
        return new UriUtils.ExplicitLinkBuilder(
                UriUtils.hostRelativeThenProvidedComponentBuilder(this.appConfig).path("/api/rota/-/schedule/"+recurringEntryHandle+"/")
                    .queryParam("startDateTime", start.toString())
                    .queryParam("endDateTime", end.toString())
                    .build());
    }

    private LinkBuilder linkToRotaVisit(int srId) {
        // eg. http://localhost:8080/ecco-war/r/app/referrals/200005/task/rotaVisit
        return UriUtils.hostRelativeThenProvidedLinkBuilder(this.appConfig)
                .slash("r").slash("app").slash("referrals").slash(srId)
                .slash("task").slash("rotaVisit");
    }

    private LinkBuilder linkToBuildingOverview(int buildingId) {
        return UriUtils.hostRelativeThenProvidedLinkBuilder(this.appConfig)
                .slash("nav/r/buildings")
                .slash(buildingId+"/");
    }

}