package com.ecco.groupsupport.repositories;

import java.util.List;

import org.joda.time.DateTime;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.ecco.dom.groupsupport.GroupSupportAttendance;

public interface GroupSupportAttendanceRepository extends CrudRepository<GroupSupportAttendance, Integer> {

    @Query("SELECT gsa FROM GroupSupportAttendance gsa WHERE"
            + " gsa.parentId.activity.id = ?1 AND gsa.attendedAt >= ?2 AND gsa.attendedAt <= ?3")
	List<GroupSupportAttendance> findAllByActivity(Long activityId, DateTime from, DateTime to);

    @Query("SELECT gsa FROM GroupSupportAttendance gsa WHERE"
            + " gsa.parentId.activity.id = ?1 AND gsa.parentId.referral.id = ?2 AND gsa.attendedAt = ?3")
	GroupSupportAttendance findOne(Long activityId, Long referralId, DateTime attendedAt);
}
