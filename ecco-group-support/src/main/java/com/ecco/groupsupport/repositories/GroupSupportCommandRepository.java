package com.ecco.groupsupport.repositories;

import java.util.List;

import org.jspecify.annotations.NonNull;

import org.springframework.data.domain.Pageable;

import com.ecco.dom.groupsupport.GroupSupportCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;

public interface GroupSupportCommandRepository extends BaseCommandRepository<GroupSupportCommand, Long> {
    @NonNull
    List<? extends GroupSupportCommand> findAll(Pageable pageable);
}