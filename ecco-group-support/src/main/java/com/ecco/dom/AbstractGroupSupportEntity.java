package com.ecco.dom;

import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;

import javax.persistence.*;

/**
 * Abstract Entity that uses {@link AccessType#FIELD} access.
 */
@MappedSuperclass
public abstract class AbstractGroupSupportEntity extends AbstractUnidentifiedVersionedEntity<Integer> implements IntegerKeyedEntity {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator="grp_activitiesTableGenerator")
    @TableGenerator(
            name = "grp_activitiesTableGenerator", initialValue = 1, pkColumnValue = "grp_activities",
            allocationSize = 1, table = "hibernate_sequences")
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private Integer id = null;

    public AbstractGroupSupportEntity() {
    }

    protected AbstractGroupSupportEntity(Integer id) {
        this.id = id;
    }


    @Override
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }
}
