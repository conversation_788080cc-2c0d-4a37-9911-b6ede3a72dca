package com.ecco.dom.groupsupport;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import com.ecco.dom.Referral;
import com.ecco.dom.LongKeyedEntity;
import com.querydsl.core.annotations.QueryInit;

/** Record of a client's involvement in a {@link GroupSupportActivity} */
@Entity
@Table(name="grp_activities_referrals")
@AssociationOverrides({
    @AssociationOverride(name="multiId.activity", joinColumns = @JoinColumn(name="activityId")),
    @AssociationOverride(name="multiId.referral", joinColumns = @JoinColumn(name="referralId"))
})
@Getter
@Setter
public class GroupActivity_Referral implements LongKeyedEntity, Serializable {

    private static final long serialVersionUID = -2853261670260826811L;

    @QueryInit("*.*")
    @EmbeddedId
    private GroupActivity_Referral_MultiId multiId = new GroupActivity_Referral_MultiId();

    @Version
    @Column(name="version")
    private int version = 0;

    @Column(name="referralId", updatable = false, insertable = false)
    private long referralId;

    @Column(name="activityId", updatable = false, insertable = false)
    private long activityId;

    // the supportplanwork id as an id and FK but not mapped in hibernate yet
    // because we have no purpose to link them, except for support/future operations
    @Column(name="supportWorkUuid", nullable=true, columnDefinition="CHAR(36)") // oracle doesn't like using unique=true
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID supportWorkUuid = null;

    // for the purposes of a waiting list, but we could simply use the waiting to the first attendance
    //boolean interested;
    @Column
    private boolean attending = false;
    @Column
    private boolean attended = false;

    /** Return the client's record of attendance at this activity */
    @OneToMany(mappedBy="parentId", fetch=FetchType.LAZY)
    private List<GroupSupportAttendance> attendance;

    @Column
    private BigDecimal cost;
    @Column
    private BigDecimal collected;

    public BigDecimal outstanding() {
        BigDecimal outstanding = cost == null ? new BigDecimal("0.00") : cost;
        if (collected != null) {
            outstanding.subtract(collected);
        }
        return outstanding;
    }

    @Override
    public String toString() {
        return  "groupsupportactivity_client " + multiId.getActivity() + ", " + multiId.getReferral();
    }

    @Transient
    public Referral getReferral() {
        return multiId.getReferral();
    }

    @Transient
    public GroupSupportActivity getGroupSupportActivity() {
        return multiId.getActivity();
    }

    public void setReferral(Referral client) {
        multiId.setReferral(client);
    }
    public void setGroupSupportActivity(GroupSupportActivity activity) {
        multiId.setActivity(activity);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        GroupActivity_Referral that = (GroupActivity_Referral) o;

        if (getMultiId() != null ? !getMultiId().equals(that.getMultiId()) : that.getMultiId() != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return (getMultiId() != null ? getMultiId().hashCode() : 0);
    }

    // FIXME HACK
    // we use Entity interface to simply save the worker_job whilst using retaining the 'chain' mechanism (though unused!)
    // we should really have getId as serializable, since this doesn't work
    @Override
    @Transient
    public Long getId() {
        return null;
    }
    @Override
    @Transient
    public boolean isNewEntity() {
        return getGroupSupportActivity().isNewEntity() && getReferral().isNewEntity();
    }
    @Override
    public void setId(Long id) {
    }
    @Override
    @Transient
    public Integer getCollectionId() {
        return null;
    }
    @Override
    public void setCollectionId(int id) {
    }

}
