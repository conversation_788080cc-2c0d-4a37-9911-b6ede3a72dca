<?xml version="1.0" encoding="UTF-8"?><wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="QLWCFService" targetNamespace="http://tempuri.org/">
    <wsdl:types>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
            <xs:import namespace="http://schemas.datacontract.org/2004/07/QLWCFService"/>
            <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/"/>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:element name="UpdateClientDetails">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q197="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q197:LoginToDB"/>
                        <xs:element xmlns:q198="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q198:iHSGUpdateClientDetails"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="UpdateClientDetailsResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="UpdateClientDetailsResult" type="xs:boolean"/>
                        <xs:element xmlns:q199="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q199:iHSGUpdateClientDetails"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            
            
            
            
            
            
            <xs:element name="GetClientDetails">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q213="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q213:LoginToDB"/>
                        <xs:element xmlns:q214="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetClientDetail" nillable="true" type="q214:iHSGGetClientDetails"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="GetClientDetailsResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q215="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetClientDetailsResult" nillable="true" type="q215:iHSGClientDetails"/>
                        <xs:element xmlns:q216="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetClientDetail" nillable="true" type="q216:iHSGGetClientDetails"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:element name="GetClientImpairments">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q318="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q318:LoginToDB"/>
                        <xs:element xmlns:q319="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetRequest" nillable="true" type="q319:GetClientImpairmentsRequest"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="GetClientImpairmentsResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q320="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetClientImpairmentsResult" nillable="true" type="q320:ArrayOfGetClientImpairmentsResponse"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:element name="CreateClient">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q678="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q678:LoginToDB"/>
                        <xs:element xmlns:q679="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q679:Client"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="CreateClientResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="CreateClientResult" type="xs:boolean"/>
                        <xs:element xmlns:q680="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q680:Client"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            <xs:element name="CreateComplaintNotes">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q684="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q684:LoginToDB"/>
                        <xs:element xmlns:q685="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q685:ArrayOfComplaintNotes"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="CreateComplaintNotesResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="CreateComplaintNotesResult" type="xs:boolean"/>
                        <xs:element xmlns:q686="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q686:ArrayOfComplaintNotes"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="CreateContact">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q687="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q687:LoginToDB"/>
                        <xs:element xmlns:q688="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q688:Contact"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="CreateContactResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="CreateContactResult" type="xs:boolean"/>
                        <xs:element xmlns:q689="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q689:Contact"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:element name="CreateClientImpairments">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q736="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q736:LoginToDB"/>
                        <xs:element xmlns:q737="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="ImportData" nillable="true" type="q737:GetClientImpairmentsResponse"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="CreateClientImpairmentsResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="CreateClientImpairmentsResult" type="xs:boolean"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            <xs:element name="DeleteClientImpair">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element xmlns:q741="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="LoginData" nillable="true" type="q741:LoginToDB"/>
                        <xs:element xmlns:q742="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetRequest" nillable="true" type="q742:GetClientImpairmentsResponse"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="DeleteClientImpairResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element minOccurs="0" name="DeleteClientImpairResult" type="xs:boolean"/>
                        <xs:element xmlns:q743="http://schemas.datacontract.org/2004/07/QLWCFService" minOccurs="0" name="GetRequest" nillable="true" type="q743:GetClientImpairmentsResponse"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/">
            <xs:element name="anyType" nillable="true" type="xs:anyType"/>
            <xs:element name="anyURI" nillable="true" type="xs:anyURI"/>
            <xs:element name="base64Binary" nillable="true" type="xs:base64Binary"/>
            <xs:element name="boolean" nillable="true" type="xs:boolean"/>
            <xs:element name="byte" nillable="true" type="xs:byte"/>
            <xs:element name="dateTime" nillable="true" type="xs:dateTime"/>
            <xs:element name="decimal" nillable="true" type="xs:decimal"/>
            <xs:element name="double" nillable="true" type="xs:double"/>
            <xs:element name="float" nillable="true" type="xs:float"/>
            <xs:element name="int" nillable="true" type="xs:int"/>
            <xs:element name="long" nillable="true" type="xs:long"/>
            <xs:element name="QName" nillable="true" type="xs:QName"/>
            <xs:element name="short" nillable="true" type="xs:short"/>
            <xs:element name="string" nillable="true" type="xs:string"/>
            <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte"/>
            <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt"/>
            <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong"/>
            <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort"/>
            <xs:element name="char" nillable="true" type="tns:char"/>
            <xs:simpleType name="char">
                <xs:restriction base="xs:int"/>
            </xs:simpleType>
            <xs:element name="duration" nillable="true" type="tns:duration"/>
            <xs:simpleType name="duration">
                <xs:restriction base="xs:duration">
                    <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?"/>
                    <xs:minInclusive value="-P10675199DT2H48M5.4775808S"/>
                    <xs:maxInclusive value="P10675199DT2H48M5.4775807S"/>
                </xs:restriction>
            </xs:simpleType>
            <xs:element name="guid" nillable="true" type="tns:guid"/>
            <xs:simpleType name="guid">
                <xs:restriction base="xs:string">
                    <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
                </xs:restriction>
            </xs:simpleType>
            <xs:attribute name="FactoryType" type="xs:QName"/>
            <xs:attribute name="Id" type="xs:ID"/>
            <xs:attribute name="Ref" type="xs:IDREF"/>
        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/QLWCFService" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/QLWCFService">
            <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
            <xs:complexType name="LoginToDB">
                <xs:sequence>
                    <xs:element name="ConnectionCode" nillable="true" type="xs:string"/>
                    <xs:element name="UserCode" nillable="true" type="xs:string"/>
                    <xs:element name="UserPassword" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:complexType name="iHSGUpdateClientDetails">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element name="client_no" type="xs:int"/>
                    <xs:element name="comp_id" nillable="true" type="xs:string"/>
                    <xs:element name="date_entered" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="field_data" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="field_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="table_name" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:complexType name="iHSGGetClientDetails">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element name="client_no" type="xs:int"/>
                </xs:sequence>
            </xs:complexType>
            
            <xs:complexType name="iHSGClientDetails">
                <xs:sequence>
                    <xs:element minOccurs="0" name="EXTRA8_T001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EXTRA8_T002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EXTRA8_T003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="EXTRA8_T004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="LastUpdated" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="age" type="xs:int"/>
                    <xs:element minOccurs="0" name="aka_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="aka_sname" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="alert1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert4" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert5" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert6" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert7" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert8" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert9" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="area_off" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="cl_sname" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="cl_type" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_add" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_no" type="xs:int"/>
                    <xs:element minOccurs="0" name="client_ref" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref6" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref7" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref8" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref9" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_status" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="clnt_corr_add" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="clnt_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="clnt_prop_add" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comm_times" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comment_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comment_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comment_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="corr_add_fr_dt" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="corr_add_to_dt" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="cust_inv_yn" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="da_rep_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="date_cr" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="date_of_death" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="dob" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="e_mail" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_add" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="employer" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="eth_org" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="excl_client_crm" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ext_client_id" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10a_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10a_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10a_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10a_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10a_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_c002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_c003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10a_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10a_d003" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10a_d004" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10a_d005" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10a_d006" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10a_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra10a_n002" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra10a_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10a_t008" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10b_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10b_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10b_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10b_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10b_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10b_d003" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10b_d004" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10b_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10b_t008" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10c_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10c_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10c_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra10c_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_c002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10c_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10c_d003" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10c_dd001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_dd002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_dd003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_dd004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_dd005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_dd006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10c_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra10c_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_c002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10d_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra10d_dd001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd008" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd009" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_dd010" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra10d_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra10d_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_a001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_a002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_a003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra1_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra1_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra1_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra1_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra1_d003" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra1_d004" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra1_d005" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra1_d006" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra1_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_t003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra1_t004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_a001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra2_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra2_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra2_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra2_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra2_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra2_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra2_n002" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra2_n003" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra2_n004" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra2_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_t003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_t004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra2_t005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra3_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra3_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra3_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra3_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra3_d003" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra3_d004" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra3_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra3_n002" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra3_n003" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra3_n004" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra3_n005" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra3_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra4_a001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra4_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra4_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra4_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra4_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra4_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra4_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra4_d002" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra4_d003" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra4_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra4_n002" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra4_n003" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra4_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra4_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra4_t003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra4_t004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra5_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra5_n001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra5_n002" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra5_n003" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra5_n004" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra5_n005" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra5_n006" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra5_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra5_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra6_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra6_c002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra6_c003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra6_dd001" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd002" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd003" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd004" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd005" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd006" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd007" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd008" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd009" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd010" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd011" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd012" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd013" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd014" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd015" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd017" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd018" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd019" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd020" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd021" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd022" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd023" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_dd16" type="xs:int"/>
                    <xs:element minOccurs="0" name="extra6_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_a001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_a002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_a003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_a004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra7_c001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c008" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_c009" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_t002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_t003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_t004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra7_t005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b023" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b024" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b025" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b026" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b027" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b028" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b029" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b030" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b031" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b032" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b033" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_b034" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8a_d001" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="extra8a_dd001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_dd002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_dd003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_dd004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_dd005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_dd006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_dd007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8a_t001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra8b_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b023" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b024" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b025" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b026" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b027" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b028" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b029" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b030" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b031" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b032" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b033" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b034" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b035" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b036" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b037" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b038" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b039" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b040" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b041" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b042" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b043" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b044" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b045" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b046" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b047" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b048" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8b_b049" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b023" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b024" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b025" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b026" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b027" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b028" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b029" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b030" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b031" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b032" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b033" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b034" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b035" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b036" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b037" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b038" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b039" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b040" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b041" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_b042" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8c_n001" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="extra8d_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra8d_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b023" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b024" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b025" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b026" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b027" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b028" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b029" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b030" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b031" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b032" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b033" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b034" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b035" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b036" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b037" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b038" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b039" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b040" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b041" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b042" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b043" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b044" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b045" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b046" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b047" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b048" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b049" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b050" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b051" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b052" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b053" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b054" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b055" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b056" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b057" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b058" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9a_b059" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b023" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b024" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b025" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b026" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b027" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b028" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b029" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b030" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b031" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_b032" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9b_n001" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="extra9b_n002" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="extra9b_n003" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="extra9c_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9c_dd001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd008" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd009" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd010" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd011" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd012" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd013" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd014" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd015" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd016" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd017" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd018" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd019" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd020" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd021" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9c_dd022" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_b001" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b002" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b003" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b004" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b005" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b006" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b007" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b008" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b009" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b010" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b011" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b012" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b013" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b014" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b015" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b016" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b017" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b018" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b019" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b020" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b021" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b022" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b023" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_b024" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="extra9d_dd001" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd002" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd003" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd004" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd005" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd006" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd007" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd008" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd009" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd010" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd011" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd012" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd013" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd014" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd015" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd016" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd017" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd018" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd019" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd020" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd021" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd022" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd023" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="extra9d_dd024" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="fmr_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="fmr_sname" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="forename" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="gender" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ha_rep_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="impair" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="initials" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_4" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_5" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_6" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_subj_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_6" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="la_area" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="la_rep_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="legalstatus1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="legalstatus2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="literacy" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="mar_stat" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="member" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="meth_pay" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nationality" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ni_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nirn" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="nirn_check1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="nirn_check2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="nirn_check3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="nok_address" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_mobile_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_other_dets" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_relationship" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_tel_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="note_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="note_yn" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="occupation" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_add" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_check_1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_check_2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_check_3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_check_4" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_code_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_6" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_comment_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_comment_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="photo" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="pref_lang" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="print_form" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="rac_grp" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="region_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="rej_ref" type="xs:int"/>
                    <xs:element minOccurs="0" name="religion" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sex_orient" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sp_num1" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="sp_text1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sp_text2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="spec_req" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="student_password" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sun_comp" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="support" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="surname" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tel_no1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tel_no2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tel_no3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="title" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="void_cleint" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="void_property" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="warning" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:complexType name="GetClientImpairmentsRequest">
                <xs:sequence>
                    <xs:element name="client_no" type="xs:int"/>
                    <xs:element name="comp_id" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element name="GetClientImpairmentsRequest" nillable="true" type="tns:GetClientImpairmentsRequest"/>
            <xs:complexType name="ArrayOfGetClientImpairmentsResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" name="GetClientImpairmentsResponse" nillable="true" type="tns:GetClientImpairmentsResponse"/>
                </xs:sequence>
            </xs:complexType>
            
            <xs:complexType name="GetClientImpairmentsResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_no" type="xs:int"/>
                    <xs:element minOccurs="0" name="comp_id" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="impair" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sp_bool" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="sp_text" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="u_version" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element name="GetClientImpairmentsResponse" nillable="true" type="tns:GetClientImpairmentsResponse"/>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            <xs:complexType name="Client">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="action" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="add_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="add_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="add_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="add_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="add_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="age" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="aka_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="alert1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert4" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert5" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert6" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert7" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert8" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="alert9" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="area_off" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="cl_type" nillable="true" type="xs:string"/>
                    <xs:element name="client_no" type="xs:int"/>
                    <xs:element minOccurs="0" name="client_ref" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref6" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref7" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref8" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_ref9" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="client_type" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="clnt_corr_add" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comm_times" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comment_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comment_3" nillable="true" type="xs:string"/>
                    <xs:element name="comp_id" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="corr_add_fr_dt" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="corr_add_to_dt" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="cust_inv_yn" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="da_rep_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="date_cr" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="date_of_death" nillable="true" type="xs:dateTime"/>
                    <xs:element name="date_time" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="dob" nillable="true" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="economic_status" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="email" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_add_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_add_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_add_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_add_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_add_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="emp_postcode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="employer" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="eth_org" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="excl_client_crm" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="ext_client_id" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="fmr_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="fmr_sname" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="forename" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="gender" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ha_rep_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="impair" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="initials" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_4" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_5" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_meth_yn_6" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="inv_subj_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="inv_subj_6" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="la_area" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="la_rep_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="legalstatus1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="legalstatus2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="literacy" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="mar_stat" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="member" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="meth_pay" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nationality" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="ni_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nirn" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="nirn_check1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="nirn_check2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="nirn_check3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="nok_add_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_add_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_add_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_add_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_add_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_mobile_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_other_dets" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_post_code" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_relationship" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="nok_tel_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="note_no" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="note_yn" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="occupation" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="oth_add_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="oth_add_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="oth_add_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="oth_add_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="oth_add_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="oth_postcode" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_check_1" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_check_2" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_check_3" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_check_4" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="other_code_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_5" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_code_6" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_comment_1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_comment_2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="other_name" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="photo" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="post_code" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="pref_lang" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="prim_clnt_yn" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="print_form" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="rac_grp" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="region_cd" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="rej_ref" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="relationship" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="religion" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="rent_acc_no" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="rent_action" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sex_orient" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sp_num1" nillable="true" type="xs:double"/>
                    <xs:element minOccurs="0" name="sp_text1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sp_text2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="spec_req" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="student_password" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="sun_comp" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="support" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="surname" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tel_no1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tel_no2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tel_no3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="title" nillable="true" type="xs:string"/>
                    <xs:element name="unique_id" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="void_client" nillable="true" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="void_property" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="warning" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            
            
            
            <xs:complexType name="ArrayOfComplaintNotes">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" name="ComplaintNotes" nillable="true" type="tns:ComplaintNotes"/>
                </xs:sequence>
            </xs:complexType>
            
            <xs:complexType name="ComplaintNotes">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="NoteBody" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="NoteTitle" nillable="true" type="xs:string"/>
                    <xs:element name="complaint_id" type="xs:int"/>
                    <xs:element name="operative_id" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            
            <xs:complexType name="Contact">
                <xs:sequence>
                    <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="action" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="brief_desc" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="class_level1" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="class_level2" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="class_level3" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="class_level4" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="class_level5" nillable="true" type="xs:string"/>
                    <xs:element name="client_no" type="xs:int"/>
                    <xs:element minOccurs="0" name="cntr_ref" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="comp_date" nillable="true" type="xs:dateTime"/>
                    <xs:element name="comp_id" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="contact_class" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="contact_no" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="contact_source" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="contact_type" nillable="true" type="xs:string"/>
                    <xs:element name="date_time" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="full_desc" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="new_contact_no" nillable="true" type="xs:int"/>
                    <xs:element minOccurs="0" name="oc_url" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="taken_by" nillable="true" type="xs:string"/>
                    <xs:element minOccurs="0" name="tency_seq_no" nillable="true" type="xs:int"/>
                    <xs:element name="unique_id" nillable="true" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
            
            
            
            
        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/System.Data" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Data">
            
        </xs:schema>
    </wsdl:types>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <wsdl:message name="IQLWCFService_UpdateClientDetails_InputMessage">
        <wsdl:part name="parameters" element="tns:UpdateClientDetails"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_UpdateClientDetails_OutputMessage">
        <wsdl:part name="parameters" element="tns:UpdateClientDetailsResponse"/>
    </wsdl:message>
    
    
    
    
    
    
    
    
    <wsdl:message name="IQLWCFService_GetClientDetails_InputMessage">
        <wsdl:part name="parameters" element="tns:GetClientDetails"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_GetClientDetails_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetClientDetailsResponse"/>
    </wsdl:message>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <wsdl:message name="IQLWCFService_GetClientImpairments_InputMessage">
        <wsdl:part name="parameters" element="tns:GetClientImpairments"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_GetClientImpairments_OutputMessage">
        <wsdl:part name="parameters" element="tns:GetClientImpairmentsResponse"/>
    </wsdl:message>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <wsdl:message name="IQLWCFService_CreateClient_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateClient"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_CreateClient_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateClientResponse"/>
    </wsdl:message>
    
    
    <wsdl:message name="IQLWCFService_CreateComplaintNotes_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateComplaintNotes"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_CreateComplaintNotes_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateComplaintNotesResponse"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_CreateContact_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateContact"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_CreateContact_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateContactResponse"/>
    </wsdl:message>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <wsdl:message name="IQLWCFService_CreateClientImpairments_InputMessage">
        <wsdl:part name="parameters" element="tns:CreateClientImpairments"/>
    </wsdl:message>
    <wsdl:message name="IQLWCFService_CreateClientImpairments_OutputMessage">
        <wsdl:part name="parameters" element="tns:CreateClientImpairmentsResponse"/>
    </wsdl:message>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    <wsdl:portType name="IQLWCFService">
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="UpdateClientDetails">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/UpdateClientDetails" message="tns:IQLWCFService_UpdateClientDetails_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/UpdateClientDetailsResponse" message="tns:IQLWCFService_UpdateClientDetails_OutputMessage"/>
        </wsdl:operation>
        
        
        
        
        <wsdl:operation name="GetClientDetails">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/GetClientDetails" message="tns:IQLWCFService_GetClientDetails_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/GetClientDetailsResponse" message="tns:IQLWCFService_GetClientDetails_OutputMessage"/>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="GetClientImpairments">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/GetClientImpairments" message="tns:IQLWCFService_GetClientImpairments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/GetClientImpairmentsResponse" message="tns:IQLWCFService_GetClientImpairments_OutputMessage"/>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="CreateClient">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/CreateClient" message="tns:IQLWCFService_CreateClient_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/CreateClientResponse" message="tns:IQLWCFService_CreateClient_OutputMessage"/>
        </wsdl:operation>
        
        <wsdl:operation name="CreateComplaintNotes">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/CreateComplaintNotes" message="tns:IQLWCFService_CreateComplaintNotes_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/CreateComplaintNotesResponse" message="tns:IQLWCFService_CreateComplaintNotes_OutputMessage"/>
        </wsdl:operation>
        <wsdl:operation name="CreateContact">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/CreateContact" message="tns:IQLWCFService_CreateContact_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/CreateContactResponse" message="tns:IQLWCFService_CreateContact_OutputMessage"/>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="CreateClientImpairments">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/CreateClientImpairments" message="tns:IQLWCFService_CreateClientImpairments_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/CreateClientImpairmentsResponse" message="tns:IQLWCFService_CreateClientImpairments_OutputMessage"/>
        </wsdl:operation>
        
        <wsdl:operation name="DeleteClientImpair">
            <wsdl:input wsaw:Action="http://tempuri.org/IQLWCFService/DeleteClientImpair" message="tns:IQLWCFService_DeleteClientImpair_InputMessage"/>
            <wsdl:output wsaw:Action="http://tempuri.org/IQLWCFService/DeleteClientImpairResponse" message="tns:IQLWCFService_DeleteClientImpair_OutputMessage"/>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    </wsdl:portType>
    <wsdl:binding name="QLWCFServiceEndpointHTTP" type="tns:IQLWCFService">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="UpdateClientDetails">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/UpdateClientDetails" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        
        
        
        <wsdl:operation name="GetClientDetails">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/GetClientDetails" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="GetClientImpairments">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/GetClientImpairments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="CreateClient">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/CreateClient" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        <wsdl:operation name="CreateComplaintNotes">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/CreateComplaintNotes" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="CreateContact">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/CreateContact" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <wsdl:operation name="CreateClientImpairments">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/CreateClientImpairments" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        <wsdl:operation name="DeleteClientImpair">
            <soap:operation soapAction="http://tempuri.org/IQLWCFService/DeleteClientImpair" style="document"/>
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    </wsdl:binding>
    <wsdl:service name="QLWCFService">
        <wsdl:port name="QLWCFServiceEndpointHTTP" binding="tns:QLWCFServiceEndpointHTTP">
            <soap:address location="http://ql-appt2:81/QLWCFService.svc"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
