package com.ecco.integration.ql;

import com.ecco.dto.ClientDefinition;
import com.ecco.dto.ClientDefinitionCommandDto;
import com.ecco.dto.ClientEvent;
import com.ecco.integration.api.OperationFailedException;
import com.ecco.integration.core.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.datacontract.schemas._2004._07.qlwcfservice.*;
import org.datacontract.schemas._2004._07.qlwcfservice.GetClientImpairmentsResponse;
import org.datacontract.schemas._2004._07.qlwcfservice.ObjectFactory;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.ws.client.WebServiceTransportException;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.tempuri.*;

import javax.xml.datatype.DatatypeFactory;
import java.util.*;

import static com.ecco.integration.ql.QLClientQueryAdapter.*;
import static java.time.ZoneOffset.UTC;

@RequiredArgsConstructor
@Slf4j
public class QLWebServiceClientConnector {

    private final QLSettings qlSettings;

    private final WebServiceTemplate webServiceTemplate;

    private final org.tempuri.ObjectFactory qlOperationFactory;

    private final ObjectFactory qlDataFactory;

    private final DatatypeFactory datatypeFactory;

    private final Properties referenceDataMapping;

    private String getMappedReferenceData(String key) {
        return Utils.getMappedReferenceData(referenceDataMapping, key);
    }

    @NonNull
    private LoginToDB getLogin() {
        LoginToDB login = new LoginToDB();
        login.setUserCode(qlSettings.getUserCode());
        login.setUserPassword(qlSettings.getUserPassword());
        login.setConnectionCode(qlSettings.getConnectionCode());
        return login;
    }

    @NonNull
    public Client createOrUpdateClient(ClientDefinition clientDefinition) {

        String externalClientRef = clientDefinition.getExternalClientRef();
        boolean newClient = !StringUtils.hasText(externalClientRef);
        log.info("Integration: " + (newClient ? "creating" : "updating") + " with: {}", clientDefinition);

        LoginToDB login = getLogin();

        Client client = new Client();

        int clientNo = newClient ? -1 : Integer.parseInt(externalClientRef);
        client.setClientNo(clientNo);
        client.setUniqueId(UUID.randomUUID().toString());
        client.setDateTime(datatypeFactory
                .newXMLGregorianCalendar(new GregorianCalendar()));

        client.setCompId(qlSettings.getCompId());

        // NB ideally we do if not null for each property... but not sure it matters
        client.setForename(qlDataFactory.createClientForename(clientDefinition.getFirstName()));
        client.setSurname(qlDataFactory.createClientSurname(clientDefinition.getLastName()));
        client.setGender(qlDataFactory.createClientGender(getMappedReferenceData(ECCO_DOMAIN + "." + GENDER_DOMAIN + "." + clientDefinition.getGenderKey())));
        // NB 'genderAtBirth' is not currently loaded from QL, nor updated by updateClient - nor is it configured by default in client details screen
        // NB 'genderAtBirth' doesn't actually exist in the API
        if (clientDefinition.getBirthDate() != null) {
            client.setDob(qlDataFactory.createClientDob(datatypeFactory.newXMLGregorianCalendar(clientDefinition.getBirthDate().toDateTimeAtStartOfDay().toGregorianCalendar())));
        }
        // NB 'code' is not currently loaded from QL, nor updated by updateClient
        // NB 'code' isn't something we've considered syncing as its typically an ECCO internal representation - just noted for completeness
        client.setNiNo(qlDataFactory.createClientNiNo(clientDefinition.getNi()));
        client.setPrefLang(qlDataFactory.createClientPrefLang(getMappedReferenceData(ECCO_DOMAIN + "." + LANGUAGE_DOMAIN + "." + clientDefinition.getFirstLanguageKey())));
        client.setEthOrg(qlDataFactory.createClientEthOrg(getMappedReferenceData(ECCO_DOMAIN + "." + ETHNICITY_DOMAIN + "." + clientDefinition.getEthnicOriginKey())));
        // NB nationality is not currently loaded from QL, nor updated by updateClient - nor is it configured by default in client details screen
        //client.setNationality(qlDataFactory.createClientNationality(getMappedReferenceData(ECCO_DOMAIN + "." + NATIONALITY_DOMAIN + "." + clientDefinition.getNationalityKey())));
        // NB marital status is not currently loaded from QL, nor updated by updateClient - nor is it configured by default in client details screen
        //client.setMarStat(qlDataFactory.createClientMarStat(getMappedReferenceData(ECCO_DOMAIN + "." + MARITALSTATUS + "." + clientDefinition.getMaritalStatusKey())));
        client.setReligion(qlDataFactory.createClientReligion(getMappedReferenceData(ECCO_DOMAIN + "." + RELIGION_DOMAIN + "." + clientDefinition.getReligionKey())));
        client.setImpair(qlDataFactory.createClientImpair(getMappedReferenceData(ECCO_DOMAIN + "." + DISABILITY_DOMAIN + "." + clientDefinition.getDisabilityKey())));
        client.setSexOrient(qlDataFactory.createClientSexOrient(getMappedReferenceData(ECCO_DOMAIN + "." + SEXUALITY_DOMAIN + "." + clientDefinition.getSexualOrientationKey())));
        // we can't guarantee address is initialised - see ClientDetail.toClientDefinition
        // and when it is, we can't be sure the whole array is there - see ClientDetail.toClientDefinition
        if (clientDefinition.getAddress() != null) {
            int maxAdr = clientDefinition.getAddress().length;
            client.setAdd1(qlDataFactory.createClientAdd1(maxAdr > 0
                    ? clientDefinition.getAddress()[0] == null ? DodgyDefaultNullString : clientDefinition.getAddress()[0]
                    : DodgyDefaultNullString
            ));
            client.setAdd2(qlDataFactory.createClientAdd2(maxAdr > 1
                    ? clientDefinition.getAddress()[1] == null ? DodgyDefaultNullString : clientDefinition.getAddress()[1]
                    : DodgyDefaultNullString
            ));
            client.setAdd3(qlDataFactory.createClientAdd3(maxAdr > 2
                    ? clientDefinition.getAddress()[2] == null ? DodgyDefaultNullString : clientDefinition.getAddress()[2]
                    : DodgyDefaultNullString
            ));
            client.setAdd4(qlDataFactory.createClientAdd4(maxAdr > 3
                    ? clientDefinition.getAddress()[3] == null ? DodgyDefaultNullString : clientDefinition.getAddress()[3]
                    : DodgyDefaultNullString
            ));
            client.setAdd5(qlDataFactory.createClientAdd5(maxAdr > 4
                    ? clientDefinition.getAddress()[4] == null ? DodgyDefaultNullString : clientDefinition.getAddress()[4]
                    : DodgyDefaultNullString
            ));
        }
        client.setPostCode(qlDataFactory.createClientPostCode(clientDefinition.getPostCode()));
        // NB 'town' and 'county' are not currently loaded from QL, nor updated by updateClient - see constructAddressArray
        client.setTelNo1(qlDataFactory.createClientTelNo1(clientDefinition.getPhoneNumber()));
        client.setTelNo2(qlDataFactory.createClientTelNo2(clientDefinition.getMobileNumber()));
        client.setEmail(qlDataFactory.createClientEmail(clientDefinition.getEmail()));

        CreateClient createClient = qlOperationFactory.createCreateClient();
        createClient.setLoginData(qlOperationFactory.createCreateClientLoginData(login));
        createClient.setImportData(qlOperationFactory.createCreateClientImportData(client));

        CreateClientResponse createClientResponse = null;
        try {
            createClientResponse = (CreateClientResponse) webServiceTemplate
                    .marshalSendAndReceive(createClient,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/CreateClient"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyCreateClient(createClientResponse);
        }

        // Client was successfully created/updated.
        // The Client can be found in QL at
        // QL > QL Housing > Maintenance > HGM General Module > HGM Client Management.

        Client responseClient = createClientResponse.getImportData().getValue();
        log.info("Integration: creating client success #{} ID={}", responseClient.getClientNo(), responseClient.getUniqueId());
        return responseClient;
    }

    private void verifyCreateClient(CreateClientResponse createClientResponse) {
        if (createClientResponse == null) {
            throw new OperationFailedException("Failed to create/update client/flags: unsuccessful request/response");
        } else if (!createClientResponse.isCreateClientResult()) {
            String reason = createClientResponse.getImportData().getValue().getErrorMessage().getValue();
            throw new OperationFailedException("Failed to create/update client/flags: " + reason);
        }
    }

    // null is ignored using wsdl's CreateClient
    private static final LocalDate DodgyDefaultNullDate = new LocalDate(0); // epoch
    private static final String DodgyDefaultNullString = "-";
    private static final String DodgyDefaultNullPostCode = "SW1A 2AA"; // 10 Downing Street
    private static final String DodgyDefaultNullEmail = "<EMAIL>"; // hopefully no spamming, or try email generators but then don't look fake

    /**
     * Transform an update from ECCO into a ClientDefinition which wsdl CreateClient uses to update
     * the client - only for the fields specified. Hence, we also set 'dodgy data' when something
     * needs to be removed, because specifying null or empty does nothing.
     * NB The cmd has already been converted to appropriate business keys - see ReferralTaskClientDetailCommandViewModel.java.
     */
    public String updateClient(@NonNull ClientDefinitionCommandDto cmd) {

        ClientDefinition.Builder b = ClientDefinition.BuilderFactory.create();
        b.externalClientRef(cmd.externalClientRef);

        if (cmd.firstName != null) {
            b.firstName(cmd.firstName.to == null ? DodgyDefaultNullString : cmd.firstName.to);
        }
        if (cmd.lastName != null) {
            b.lastName(cmd.lastName.to == null ? DodgyDefaultNullString : cmd.lastName.to);
        }
        if (cmd.genderKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.genderKey(cmd.genderKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + GENDER_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.genderKey.to
            );
        }
        /*if (cmd.genderAtBirthKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.genderAtBirthKey(cmd.genderAtBirthKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + GENDER_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.genderAtBirthKey.to
            );
        }*/
        // TODO if wanted
        //  b.code()
        if (cmd.birthDate != null) {
            // NB DEFAULT is EPOCH not null, as we can't do null
            b.birthDate(cmd.birthDate.to == null ? DodgyDefaultNullDate : cmd.birthDate.to);
        }
        if (cmd.ni != null) {
            b.ni(cmd.ni.to == null ? DodgyDefaultNullString : cmd.ni.to);
        }
        if (cmd.firstLanguageKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.firstLanguageKey(cmd.firstLanguageKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + LANGUAGE_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.firstLanguageKey.to
            );
        }
        if (cmd.ethnicOriginKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.ethnicOriginKey(cmd.ethnicOriginKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + ETHNICITY_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.ethnicOriginKey.to
            );
        }
        // TODO if wanted, these will also need to be loaded from ql (QL has mar_stat and nationality (API) and likely the same SQL fields)
        /*if (cmd.nationalityKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.nationalityKey(cmd.nationalityKey.to == null
                    ? getMappedReferenceData(NATIONALITY_DOMAIN + "." + DEFAULT_DOMAIN)
                    : cmd.nationalityKey.to
            );
        }
        if (cmd.maritalStatusKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.maritalStatusKey(cmd.maritalStatusKey.to == null
                    ? getMappedReferenceData(MARITALSTATUS_DOMAIN + "." + DEFAULT_DOMAIN)
                    : cmd.maritalStatusKey.to
            );
        }*/
        if (cmd.religionKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.religionKey(cmd.religionKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + RELIGION_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.religionKey.to
            );
        }
        if (cmd.disabilityKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.disabilityKey(cmd.disabilityKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + DISABILITY_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.disabilityKey.to
            );
        }
        if (cmd.sexualOrientationKey != null) {
            // incoming business key is passed to createOrUpdate, allowing for null lookup value with ECCO.DOMAIN.DEFAULT
            b.sexualOrientationKey(cmd.sexualOrientationKey.to == null
                    ? getMappedReferenceData(ECCO_DOMAIN + "." + SEXUALITY_DOMAIN + "." + DEFAULT_BUSINESS_KEY)
                    : cmd.sexualOrientationKey.to
            );
        }
        if (cmd.address != null) {
            // try to avoid a messy mix of addresses by overriding all 5 spaces
            var newAddress = new String[5];
            if (cmd.address.to == null) {
                newAddress[0] = DodgyDefaultNullString;
                newAddress[1] = DodgyDefaultNullString;
                newAddress[2] = DodgyDefaultNullString;
                newAddress[3] = DodgyDefaultNullString;
                newAddress[4] = DodgyDefaultNullString;
            } else {
                newAddress[0] = cmd.address.to[0] == null ? DodgyDefaultNullString : cmd.address.to[0];
                newAddress[1] = cmd.address.to[1] == null ? DodgyDefaultNullString : cmd.address.to[1];
                newAddress[2] = cmd.address.to[2] == null ? DodgyDefaultNullString : cmd.address.to[2];
                newAddress[3] = DodgyDefaultNullString;
                newAddress[4] = DodgyDefaultNullString;
            }
            b.address(newAddress);
        }
        /*if (cmd.town != null) {
            b.town(cmd.town.to == null ? DodgyDefaultNullString : cmd.town.to);
        }*/
        if (cmd.postCode != null) {
            b.postCode(cmd.postCode.to == null ? DodgyDefaultNullPostCode : cmd.postCode.to);
        }
        /*if (cmd.county != null) {
            b.county(cmd.county.to == null ? DodgyDefaultNullString : cmd.county.to);
        }*/

        if (cmd.phoneNumber != null) {
            b.phoneNumber(cmd.phoneNumber.to == null ? DodgyDefaultNullString : cmd.phoneNumber.to);
        }
        if (cmd.mobileNumber != null) {
            b.mobileNumber(cmd.mobileNumber.to == null ? DodgyDefaultNullString : cmd.mobileNumber.to);
        }
        if (cmd.email != null) {
            b.email(cmd.email.to == null ? DodgyDefaultNullEmail : cmd.email.to);
        }

        return createOrUpdateClient(b.build()).getUniqueId();
    }

    /**
     * Update the holding table which requires manual approval.
     * NB This is not our preferred approach.
     */
    public void updateClientHoldingTable(@NonNull String externalClientRef, @NonNull String phoneNumber) {
        updateClientItemHoldingTable(externalClientRef,"forename", phoneNumber);
    }

    public void updateClientItemHoldingTable(String externalClientRef, String field, String data) {

        LoginToDB login = getLogin();

        int clientNo = Integer.parseInt(externalClientRef);
        IHSGUpdateClientDetails client = new IHSGUpdateClientDetails();
        client.setCompId(qlSettings.getCompId());
        client.setClientNo(clientNo);
        client.setDateEntered(datatypeFactory
                .newXMLGregorianCalendar(new GregorianCalendar()));
        client.setTableName(qlDataFactory.createIHSGUpdateClientDetailsTableName("HGMCLENT"));
        client.setFieldName(qlDataFactory.createIHSGUpdateClientDetailsFieldName(field));
        client.setFieldData(qlDataFactory.createIHSGUpdateClientDetailsFieldData(data));

        UpdateClientDetails updateClient = qlOperationFactory.createUpdateClientDetails();
        updateClient.setLoginData(qlOperationFactory.createCreateClientLoginData(login));
        updateClient.setImportData(qlOperationFactory.createUpdateClientDetailsImportData(client));

        log.info("Integration: updating client");

        UpdateClientDetailsResponse clientResponse = null;
        try {
            clientResponse = (UpdateClientDetailsResponse) webServiceTemplate
                    .marshalSendAndReceive(updateClient,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/UpdateClientDetails"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyUpdateClient(clientResponse);
        }

        // Client was successfully updated.
        // The Client can be found in QL at
        // QL > QL Housing > Maintenance > HGM General Module > HGM Client Management.
        // BUT only after an approval of the holding table data has been made.

        IHSGUpdateClientDetails responseClient = clientResponse.getImportData().getValue();
        log.info("Integration: updating client success #{} field={}", responseClient.getClientNo(), responseClient.getFieldName());
    }

    private void verifyUpdateClient(UpdateClientDetailsResponse clientResponse) {
        if (clientResponse == null) {
            throw new OperationFailedException("Failed to update client: unsuccessful request/response");
        } else if (!clientResponse.isUpdateClientDetailsResult()) {
            String reason = clientResponse.getImportData().getValue().getErrorMessage().getValue();
            throw new OperationFailedException("Failed to update client: " + reason);
        }
    }

    /**
     * Return the Client from the WebService API, rather than ClientQuery queryClientFlags
     */
    @NonNull
    public IHSGClientDetails getClient(String externalClientRef) {
        LoginToDB login = getLogin();

        int clientNo = Integer.parseInt(externalClientRef);
        var args = qlDataFactory.createIHSGGetClientDetails();
        args.setClientNo(clientNo);

        GetClientDetails getClient = qlOperationFactory.createGetClientDetails();
        getClient.setLoginData(qlOperationFactory.createGetClientDetailsLoginData(login));
        getClient.setGetClientDetail(qlOperationFactory.createGetClientDetailsGetClientDetail(args));

        log.info("Integration: get client details for {}", externalClientRef);

        GetClientDetailsResponse getClientDetailsResponse = null;
        try {
            getClientDetailsResponse = (GetClientDetailsResponse) webServiceTemplate
                    .marshalSendAndReceive(getClient,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/GetClientDetails"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyGetClient(getClientDetailsResponse);
        }

        // Client was successfully obtained.
        // The Client can be found in QL at
        // QL > QL Housing > Maintenance > HGM General Module > HGM Client Management.

        // as per CreateClient, it seems to send back what we sent
        var responseClient = getClientDetailsResponse.getGetClientDetail().getValue();
        log.info("Integration: get client success #{}", responseClient.getClientNo());
        // except now we also get the client details we need
        return getClientDetailsResponse.getGetClientDetailsResult().getValue();
    }

    private void verifyGetClient(GetClientDetailsResponse getClientResponse) {
        if (getClientResponse == null) {
            throw new OperationFailedException("Failed to get client: unsuccessful request/response");
        }
    }

    @NonNull
    public Client createOrUpdateFlagAlert(FlagAlertDefinition alertDefinition) {

        //log.debug("Integration: entering createOrUpdateFlagAlert");

        LoginToDB login = getLogin();

        Client client = new Client();
        String externalClientRef = alertDefinition.getExternalClientRef();
        if (externalClientRef == null) {
            throw new IllegalArgumentException("externalClientRef is null");
        }
        int clientNo = Integer.parseInt(externalClientRef);
        client.setClientNo(clientNo);
        client.setUniqueId(UUID.randomUUID().toString());
        client.setDateTime(datatypeFactory
                .newXMLGregorianCalendar(new GregorianCalendar()));
        client.setCompId(qlSettings.getCompId());

        // NB ideally we do if not null for each property... but not sure it matters
        client.setAlert1(qlDataFactory.createClientAlert1(alertDefinition.getAlert1()));
        client.setAlert2(qlDataFactory.createClientAlert2(alertDefinition.getAlert2()));
        client.setAlert3(qlDataFactory.createClientAlert3(alertDefinition.getAlert3()));
        client.setAlert4(qlDataFactory.createClientAlert4(alertDefinition.getAlert4()));
        client.setAlert5(qlDataFactory.createClientAlert5(alertDefinition.getAlert5()));
        client.setAlert6(qlDataFactory.createClientAlert6(alertDefinition.getAlert6()));
        client.setAlert7(qlDataFactory.createClientAlert7(alertDefinition.getAlert7()));
        client.setAlert8(qlDataFactory.createClientAlert8(alertDefinition.getAlert8()));
        client.setAlert9(qlDataFactory.createClientAlert9(alertDefinition.getAlert9()));

        CreateClient createClient = qlOperationFactory.createCreateClient();
        createClient.setLoginData(qlOperationFactory.createCreateClientLoginData(login));
        createClient.setImportData(qlOperationFactory.createCreateClientImportData(client));

        log.info("Integration: updating client alert flags");

        CreateClientResponse createClientResponse = null;
        try {
            createClientResponse = (CreateClientResponse) webServiceTemplate
                    .marshalSendAndReceive(createClient,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/CreateClient"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyCreateClient(createClientResponse);
        }

        // Client was successfully created/updated.
        // The Client can be found in QL at
        // QL > QL Housing > Maintenance > HGM General Module > HGM Client Management.

        Client responseClient = createClientResponse.getImportData().getValue();
        log.info("Integration: updating client alert flags success #{} ID={}", responseClient.getClientNo(), responseClient.getUniqueId());
        return responseClient;
    }

    /**
     * NB The sp_bool property seems to do nothing, at least from observed data.
     * @return An array of business keys which represent enabled flags.
     */
    public List<String> getFlagImpairmentsOn(String externalClientRef) {
        List<GetClientImpairmentsResponse> impairmentsRaw = getFlagImpairments(externalClientRef);

        return impairmentsRaw.stream()
            //log.debug("Integration: get client impairments... err {} name {} matched ref '{}'", i.getErrorMessage().getValue(), i.getImpair().getValue(), ref);
            .map(i -> getMappedReferenceData(FLAG_IMPAIR_DOMAIN + "." + i.getImpair().getValue()))
            .filter(Objects::nonNull)
            .toList();
    }

    @NonNull
    public List<GetClientImpairmentsResponse> getFlagImpairments(String externalClientRef) {
        LoginToDB login = getLogin();

        int clientNo = Integer.parseInt(externalClientRef);
        var args = qlDataFactory.createGetClientImpairmentsRequest();
        args.setClientNo(clientNo);
        args.setCompId(qlSettings.getCompId());

        GetClientImpairments getClientImpairments = qlOperationFactory.createGetClientImpairments();
        getClientImpairments.setLoginData(qlOperationFactory.createGetClientDetailsLoginData(login));
        getClientImpairments.setGetRequest(qlOperationFactory.createGetClientImpairmentsGetRequest(args));

        log.info("Integration: get client impairments for {}", externalClientRef);

        org.tempuri.GetClientImpairmentsResponse getClientImpairmentsResponse = null;
        try {
            getClientImpairmentsResponse = (org.tempuri.GetClientImpairmentsResponse) webServiceTemplate
                    .marshalSendAndReceive(getClientImpairments,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/GetClientImpairments"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyGetClientImpairments(getClientImpairmentsResponse);
        }

        // Client impairments were successfully obtained.
        // The impairments can be found in QL at
        // ???

        // as per CreateClient, it seems to send back what we sent
        var responseClient = getClientImpairmentsResponse.getGetClientImpairmentsResult().getValue();
        log.info("Integration: get client impairments success #{}", externalClientRef);
        // except now we also get the client impairments we need
        return responseClient.getGetClientImpairmentsResponse();
    }

    private void verifyGetClientImpairments(org.tempuri.GetClientImpairmentsResponse getClientResponse) {
        if (getClientResponse == null) {
            throw new OperationFailedException("Failed to get client impairments: unsuccessful request/response");
        }
    }

    public boolean createFlagImpairment(String externalClientRef, String impairment) {

        LoginToDB login = getLogin();

        int clientNo = Integer.parseInt(externalClientRef);
        GetClientImpairmentsResponse impair = new GetClientImpairmentsResponse();
        impair.setClientNo(clientNo);
        impair.setCompId(qlDataFactory.createGetClientImpairmentsResponseCompId(qlSettings.getCompId()));
        impair.setImpair(qlDataFactory.createClientImpair(impairment));

        //impair.setSpBool();
        //impair.setSpText();
        //impair.setUVersion();

        CreateClientImpairments createClientImpairments = qlOperationFactory.createCreateClientImpairments();
        createClientImpairments.setLoginData(qlOperationFactory.createCreateClientLoginData(login));
        createClientImpairments.setImportData(qlOperationFactory.createCreateClientImpairmentsImportData(impair));

        log.info("Integration: creating client impairment");

        CreateClientImpairmentsResponse clientResponse = null;
        try {
            clientResponse = (CreateClientImpairmentsResponse) webServiceTemplate
                    .marshalSendAndReceive(createClientImpairments,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/CreateClientImpairments"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyCreateClientImpairment(clientResponse);
        }

        // Client impairment was successfully updated.
        // The impairment can be found in QL at
        // ???

        log.info("Integration: create client impairment success #{} field={}", externalClientRef, impairment);
        return clientResponse.isCreateClientImpairmentsResult();
    }

    private void verifyCreateClientImpairment(CreateClientImpairmentsResponse clientResponse) {
        if (clientResponse == null) {
            throw new OperationFailedException("Failed to create client impairment: unsuccessful request/response");
        } else if (!clientResponse.isCreateClientImpairmentsResult()) {
            throw new OperationFailedException("Failed to create client impairment");
        }
    }

    public boolean deleteFlagImpairment(String externalClientRef, String impairment) {

        LoginToDB login = getLogin();

        int clientNo = Integer.parseInt(externalClientRef);
        GetClientImpairmentsResponse impair = new GetClientImpairmentsResponse();
        impair.setClientNo(clientNo);
        impair.setCompId(qlDataFactory.createGetClientImpairmentsResponseCompId(qlSettings.getCompId()));
        impair.setImpair(qlDataFactory.createClientImpair(impairment));

        //impair.setSpBool();
        //impair.setSpText();
        //impair.setUVersion();

        DeleteClientImpair deleteClientImpair = qlOperationFactory.createDeleteClientImpair();
        deleteClientImpair.setLoginData(qlOperationFactory.createCreateClientLoginData(login));
        deleteClientImpair.setGetRequest(qlOperationFactory.createDeleteClientImpairGetRequest(impair));

        log.info("Integration: deleting client impairment");

        DeleteClientImpairResponse clientResponse = null;
        try {
            clientResponse = (DeleteClientImpairResponse) webServiceTemplate
                    .marshalSendAndReceive(deleteClientImpair,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/DeleteClientImpair"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyDeleteClientImpairment(clientResponse);
        }

        // Client impairment was successfully deleted.

        log.info("Integration: delete client impairment success #{} field={}", externalClientRef, impairment);
        return clientResponse.isDeleteClientImpairResult();
    }

    private void verifyDeleteClientImpairment(DeleteClientImpairResponse clientResponse) {
        if (clientResponse == null) {
            throw new OperationFailedException("Failed to delete client impairment: unsuccessful request/response");
        } else if (!clientResponse.isDeleteClientImpairResult()) {
            throw new OperationFailedException("Failed to delete client impairment");
        }
    }

    @NonNull
    public String createContact(ClientEvent event) {
        int clientNo = Integer.parseInt(event.getExternalClientRef());

        LoginToDB login = getLogin();

        Contact contact = new Contact();
        contact.setClientNo(clientNo);
        contact.setCompId(qlSettings.getCompId());
        contact.setUniqueId(event.getUuid().toString());
        contact.setDateTime(datatypeFactory
                .newXMLGregorianCalendar(GregorianCalendar.from(event.getStartInstant().atZone(UTC)))); // This is from ecco's supportplanwork workDate which is a localdatetime so startInstant cannot be known
        contact.setBriefDesc(qlDataFactory.createContactBriefDesc(event.getSubject()));
        contact.setFullDesc(qlDataFactory.createContactFullDesc(event.getBody()));

        CreateContact createContact = qlOperationFactory.createCreateContact();
        createContact.setLoginData(qlOperationFactory.createCreateContactLoginData(login));
        createContact.setImportData(qlOperationFactory.createCreateContactImportData(contact));

        log.info("Integration: creating contact note");

        CreateContactResponse createContactResponse = null;
        try {
            createContactResponse = (CreateContactResponse) webServiceTemplate
                    .marshalSendAndReceive(createContact,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/CreateContact"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyCreateContactNote(createContactResponse);
        }

        // Contact was successfully created.
        // The Contact can be found in QL at
        // QL > QL Housing > Enquiries > HGM Contact Management > HGM Contact Management

        Contact responseContact = createContactResponse.getImportData().getValue();

        log.info("Integration: creating contact note success ID={}", responseContact.getUniqueId());
        return responseContact.getUniqueId();
    }


    @NonNull
    public String createComplaint(ClientEvent event) {
        int clientNo = Integer.parseInt(event.getExternalClientRef());

        LoginToDB login = getLogin();

        ComplaintNotes complaintNotes = new ComplaintNotes();
        complaintNotes.setComplaintId(clientNo);
        complaintNotes.setNoteBody(qlDataFactory.createComplaintNotesNoteBody(event.getBody()));
        //complaintNotes.setNoteTitle
        //complaintNotes.setErrorMessage
        //complaintNotes.setOperativeId

        CreateComplaintNotes createComplaints = qlOperationFactory.createCreateComplaintNotes();
        createComplaints.setLoginData(qlOperationFactory.createCreateComplaintNotesLoginData(login));
        ArrayOfComplaintNotes arr = new ArrayOfComplaintNotes();
        arr.getComplaintNotes().add(complaintNotes);
        createComplaints.setImportData(qlOperationFactory.createCreateComplaintNotesImportData(arr));

        log.info("Integration: creating complaint note");

        CreateComplaintNotesResponse createComplaintNotesResponse = null;
        try {
            createComplaintNotesResponse = (CreateComplaintNotesResponse) webServiceTemplate
                    .marshalSendAndReceive(createComplaints,
                            new SoapActionCallback("http://tempuri.org/IQLWCFService/CreateComplaintNotes"));
        } catch (WebServiceTransportException e) {
            log.error("WebServiceTransportException: " + e);
        } finally {
            verifyCreateComplaintNotes(createComplaintNotesResponse);
        }

        // Complaint was successfully created.
        // The Complaint can be found in QL at
        // QL > ??

        ArrayOfComplaintNotes responseComplaints = createComplaintNotesResponse.getImportData().getValue();

        int id = responseComplaints.getComplaintNotes().get(0).getComplaintId();
        log.info("Integration: creating complaint note success ID={}", id);
        return String.valueOf(id);
    }

    private void verifyCreateContactNote(CreateContactResponse createContactResponse) {
        if (createContactResponse == null) {
            throw new OperationFailedException("Failed to create contact: unsuccessful request/response");
        } else if (!createContactResponse.isCreateContactResult()) {
            String reason = createContactResponse.getImportData().getValue().getErrorMessage().getValue();
            throw new OperationFailedException("Failed to create contact: " + reason);
        }
    }

    private void verifyCreateComplaintNotes(CreateComplaintNotesResponse createComplaintNotesResponse) {
        if (createComplaintNotesResponse == null) {
            throw new OperationFailedException("Failed to create complaint: unsuccessful request/response");
        } else if (!createComplaintNotesResponse.isCreateComplaintNotesResult()) {
            List<ComplaintNotes> arr = createComplaintNotesResponse.getImportData().getValue().getComplaintNotes();
            String reason = arr.size() == 1
                    ? arr.get(0).getErrorMessage().getValue()
                    : "expecting one note, but found: " + arr.size();
            throw new OperationFailedException("Failed to create complaint: " + reason);
        }
    }
}
