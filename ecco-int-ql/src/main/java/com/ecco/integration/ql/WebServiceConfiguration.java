package com.ecco.integration.ql;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;

import org.datacontract.schemas._2004._07.qlwcfservice.ObjectFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.Marshaller;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;

import java.util.Properties;

@Configuration
public class WebServiceConfiguration {
    @Bean
    public Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setPackagesToScan(
                "com.microsoft.schemas",
                "org.datacontract.schemas",
                "org.tempuri"
        );
        return marshaller;
    }

    @Bean
    public WebServiceTemplate webServiceTemplate(QLSettings qlSettings, Marshaller marshaller) {
        WebServiceTemplate webServiceTemplate = new WebServiceTemplate(marshaller);
        webServiceTemplate.setDefaultUri(qlSettings.getUrl());
        return webServiceTemplate;
    }

    @Bean
    public org.tempuri.ObjectFactory qlOperationFactory() {
        return new org.tempuri.ObjectFactory();
    }

    @Bean
    public org.datacontract.schemas._2004._07.qlwcfservice.ObjectFactory qlDataFactory() {
        return new org.datacontract.schemas._2004._07.qlwcfservice.ObjectFactory();
    }

    @Bean
    public DatatypeFactory datatypeFactory() throws DatatypeConfigurationException {
        // TODO Is this correct?
        // The resulting DatatypeFactory is configured by some unknown
        // external configuration. Should we hardcode the correct
        // configuration?
        return DatatypeFactory.newInstance();
    }

    @Bean
    public QLWebServiceClientConnector qlClient(
            QLSettings settings,
            WebServiceTemplate webServiceTemplate,
            org.tempuri.ObjectFactory qlOperationFactory,
            ObjectFactory qlDataFactory,
            DatatypeFactory datatypeFactory,
            @Qualifier("referenceDataMapping") Properties referenceDataMapping) {
        return new QLWebServiceClientConnector(
                settings, webServiceTemplate, qlOperationFactory, qlDataFactory, datatypeFactory, referenceDataMapping);
    }
}