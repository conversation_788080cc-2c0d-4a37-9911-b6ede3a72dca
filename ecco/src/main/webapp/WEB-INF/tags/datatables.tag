<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"
        description="tag that outputs dataTable script" %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@attribute name="bootstrap" type="java.lang.Boolean" required="false"
        description="Whether bootstrap is enabled on the page."%>

<%@attribute name="selector" type="java.lang.String" required="false"
        description="The selector to kick-start the datatable. Can be null to mean nothing started."%>

<%--*****************************************
    ** THIS FILE IS NOT USED BY REPORTS LIST - see tables.ts
    *****************************************--%>

<%-- example 'doBody' from reportsReferralDetails
    <%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

    <%@ taglib prefix="ecco" tagdir="/WEB-INF/tags" %>

    <ecco:datatables-initVar/>

    <ecco:datatables selector="table.view">

        // trick to resize the filter boxes (and at the bottom too)
        // as found on http://datatables.net/forums/discussion/887/table-width/p1
        dataTable.find('input,select').css('width', '5px');
        dataTable.columnFilter({
            sPlaceHolder: "head:after",
            sRangeFormat: "from {from} to {to}",
            aoColumns:
                [
                    { type: "text" },
                    { type: "text" },
                    { type: "number-range" }, // as per http://jquery-datatables-column-filter.googlecode.com/svn/trunk/numberRange.html
                    { type: "text" },
                    { type: "text" },
                    { type: "text" },
                    { type: "date-range" },
                    { type: "text" },
                    { type: "date-range" },
                    { type: "date-range" },
                    { type: "text" },
                    { type: "text" },
                    { type: "text" },
                    { type: "text" },
                    { type: "text" },
                    { type: "date-range" }
                ]
        });
        // trick as above
        dataTable.find('input,select').css('width', '100%');

    </ecco:datatables>
--%>

<c:if test="${!bootstrap}">
    <link rel="stylesheet" type="text/css" href="${scriptsBase}/datatables/media/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" type="text/css" href="${scriptsBase}/datatables/extensions/Buttons/css/buttons.dataTables.min.css" />
</c:if>

<c:if test="${bootstrap}">
    <%-- datatables bootstrap --%>
    <link rel="stylesheet" type="text/css" href="${scriptsBase}/datatables/media/css/dataTables.bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="${scriptsBase}/datatables/extensions/Buttons/css/buttons.bootstrap.min.css" />
</c:if>


<script type="text/javascript">
require(["jquery", "jquery-ui-datepicker", "datatables.net-moment", <c:if test="${!bootstrap}">, "datatables.net-buttons-html5"</c:if><c:if test="${bootstrap}">, "datatables.net-buttons-bs"</c:if>], function(jQuery) {

    jQuery(function() {

        // columnFilter is not used now
        // columnFilter date picker regional changes (look global, but probably ok)
        // jQuery.datepicker.regional[""].dateFormat = 'dd-mm-yy';
        // jQuery.datepicker.setDefaults(jQuery.datepicker.regional['']);

        jQuery.fn.dataTable.moment('DD/MM/YYYY');

        <c:if test="${not empty selector}">
            var dataTable = jQuery('<c:out value="${selector}"/>').dataTable(dataTableInit);
            <jsp:doBody/>
        </c:if>

    });

});

</script>
