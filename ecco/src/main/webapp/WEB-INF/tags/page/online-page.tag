<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>
<!-- online-page.tag -->

<%@attribute name="classes" type="java.lang.String" required="false"
        description="Additional classes to add to the &lt;html&gt; element." %>
<%@attribute name="title" type="java.lang.String" required="true"
        description="The title of the page." %>
<%@attribute name="importRequireJsModules" type="java.lang.String" required="false"
        description="A space-separated list of require.js modules to import (bootstrap and timeout (if online) are imported by default)." %>
<%@attribute name="timeoutEnabled" required="false" type="java.lang.Boolean" %>
<%@attribute name="disableBootstrapPatch" type="java.lang.Boolean" required="false"
             description="If the bootstrap patch css should be linked." %>

<%@attribute name="devMode" type="java.lang.Boolean" required="true"
             description="True if this page should use dev resources." %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>
<%@taglib prefix="security" uri="http://www.springframework.org/security/tags"  %>

<c:if test="${empty timeoutEnabled}">
    <c:set var="timeoutEnabled" value="${true}"/>
</c:if>

<security:authorize var="roleUser" access="hasAnyRole('ROLE_USER')"/>

<page:rich-client-page classes="${classes}" isTransitionalUI="${true}" title="${title}"
                       disableBootstrapPatch="${disableBootstrapPatch}"
                       importRequireJsModules="bootstrap ${importRequireJsModules} ${(roleUser && timeoutEnabled) ? 'online/timeout' : ''}"
                       devMode="${devMode}">

    <!-- timoutEnabled: ${timeoutEnabled} -->

    <page:main_head_jsbase/>

    <page:page-banner>
        <page:page-menus-bs noCollapse="true"/>
    </page:page-banner>

    <jsp:doBody/>

</page:rich-client-page>