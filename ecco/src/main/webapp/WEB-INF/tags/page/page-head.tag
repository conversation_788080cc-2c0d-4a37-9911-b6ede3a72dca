<%@tag body-content="empty" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@attribute name="applicationProperties" required="true" type="com.ecco.infrastructure.config.ApplicationProperties"%>
<%@attribute name="titleRaw" type="java.lang.String" required="false"%>
<%@attribute name="titleMessageKey" type="java.lang.String" required="false"%>
<%@attribute name="noBootstrap" type="java.lang.Boolean" required="false" description="set true to disable bootstrap"%>

<%@include file="/WEB-INF/views/includes.jsp"%>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page" %>


<!--
Java Version             <c:out value="${applicationProperties.javaVersion}"/>
Build CommitId:          <c:out value="${applicationProperties.gitCommitId}"/>
Build Branch:            <c:out value="${applicationProperties.gitBranch}"/>
Build CommitTime:        <c:out value="${applicationProperties.gitCommitTime}"/>
-->

<page:page-title title="${titleRaw}" titleMessageKey="${titleMessageKey}"/>

<link rel="icon" href="${applicationProperties.resourceRootPath}themes/ecco/images/favicon.ico">
<link rel="shortcut icon" href="${applicationProperties.resourceRootPath}themes/ecco/images/favicon.ico">
<c:if test="${!noBootstrap}">
    <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap-modal-bs3patch.css" rel="stylesheet">
    <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap-modal.css" rel="stylesheet">
    <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap-theme.min.css" rel="stylesheet">
    <link href="${applicationProperties.resourceRootPath}css/typeahead.css" rel="stylesheet">
</c:if>
<link href="${applicationProperties.resourceRootPath}font-awesome/css/font-awesome.min.css" rel="stylesheet">
<c:if test="${noBootstrap}">
    <link href="${applicationProperties.resourceRootPath}css/common/reset.css" rel="stylesheet">
</c:if>
<link href="${applicationProperties.resourceRootPath}css/editable/editable.css" rel="stylesheet"/>
<link href="${applicationProperties.resourceRootPath}css/common/common.css" rel="stylesheet">
<link href="${applicationProperties.resourceRootPath}css/common/evidence.css" rel="stylesheet">
<link href="${applicationProperties.resourceRootPath}css/select2/select2.min.css" rel="stylesheet">

<link rel="stylesheet" href="${applicationProperties.resourceRootPath}themes/standard/housestyle_themestandard.css">
