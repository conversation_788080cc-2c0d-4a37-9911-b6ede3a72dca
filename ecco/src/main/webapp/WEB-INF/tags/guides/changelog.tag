<%@ tag body-content="empty" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="ecco" tagdir="/WEB-INF/tags" %>
<%@ taglib prefix="guides" tagdir="/WEB-INF/tags/guides" %>
<%@ taglib prefix="security" uri="http://www.springframework.org/security/tags" %>

<ecco:quick-guide-snippet order="1">
    <script>
        function popUrl(url, title) {
            require(["controls/Modal"], function(Modal) {
                var modal = new Modal();
                modal.popWithUrl(url);
                modal.title(title);
                modal.closeButtonText("close");
            });
        }
    </script>
    <div class="row">
        <div class="col-sm-6">
            <h3>guidance</h3>
            <ul>
                <c:url var="secUrl" value="/dynamic/help/security-guide-popup"/>
                <li><a href="#" onclick="popUrl('${secUrl}', 'security guide')">security guide</a></li>
                <c:url var="browsersUrl" value="/dynamic/help/supported-browsers-popup"/>
                <li><a href="#" onclick="popUrl('${browsersUrl}', 'browsers')">supported browsers and devices</a></li>
            </ul>
        </div>
        <div class="col-sm-6">
            <h3>module features</h3>
            <ul>
                <c:url var="rotaUrl" value="/dynamic/help/rota-features-popup"/>
                <li><a href="#" onclick="popUrl('${rotaUrl}', 'rota features')">rota</a></li>
                <c:url var="bldgsUrl" value="/dynamic/help/buildings-features-popup"/>
                <li><a href="#" onclick="popUrl('${bldgsUrl}', 'buildings features')">buildings</a></li>
            </ul>
        </div>
    </div>

    <h3>past, present and future development</h3>

    <%--
    <guides:changelog-release version="planned development">
        <guides:changelog-day date="17.07">
            <guides:changelog-entry type="improvement">
            </guides:changelog-entry>
        </guides:changelog-day>
    </guides:changelog-release>
--%>

    <guides:changelog-release version="in progress">
        <guides:changelog-day date="client access">
            <guides:changelog-entry type="improvement">
                Major update to user experience for service users/clients to allow them to login straight to their account
            </guides:changelog-entry>
        </guides:changelog-day>
    </guides:changelog-release>

    <p>(year.month versions)</p>
    <guides:changelog-release version="19.05">
        <!-- feature, change, improvement, love, wip, fix -->
        <guides:changelog-day date="audit first GDPR">
            <guides:changelog-entry type="improvement">
                Major update to ensure all changes to a client file are fully audited in fine detail.
            </guides:changelog-entry>
        </guides:changelog-day>
        <guides:changelog-day date="support worker">
            <guides:changelog-entry type="improvement">
                Major update to user experience for support workers to enable you to get straight to their appointments
                and clients.
            </guides:changelog-entry>
        </guides:changelog-day>
        <guides:changelog-day date="offline">
            <guides:changelog-entry type="improvement">
                Major update to offline to integrate it into the normal screens and allow switching to work offline without
                going to a separate part of ECCO.
            </guides:changelog-entry>
        </guides:changelog-day>
        <guides:changelog-day date="calendar-driven">
            <guides:changelog-entry type="improvement">
                Calendar appointments on a client file (non-rota) generate a schedule of visits/calls.
            </guides:changelog-entry>
        </guides:changelog-day>
    </guides:changelog-release>

    <guides:changelog-release version="18.11">
        <!-- feature, change, improvement, love, wip, fix -->
        <guides:changelog-day date="November">
            <guides:changelog-entry type="love">
                Admin improvements
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Gender, sexuality, etc are all editable lists
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Custom forms can include general text
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Audit report better categories
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Questionnaires offline
            </guides:changelog-entry>
        </guides:changelog-day>
    </guides:changelog-release>

    <guides:changelog-release version="18.10">
        <guides:changelog-day date="October">
            <guides:changelog-entry type="feature">
                Welcome page shows (optional) the 'appts/tasks' and 'activity' to represent future things todo and past audits by staff
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Questionnaires validation on 'money' values - good for 'movement reports' showing money gained/lost
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                QL API integration error on differing environments
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                'quick log' lists all live clients in one page to quickly record notes
            </guides:changelog-entry>
        </guides:changelog-day>
        <guides:changelog-day date="September">
            <guides:changelog-entry type="feature">
                'housing benefit ref' optional client details field (and shows under avatar on main file)
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Evidence page can appear directly as a tab on the referral page
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Report 'client status' to include date/time the status was noted
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Calendar - remove disabled users from the overlay list
            </guides:changelog-entry>
        </guides:changelog-day>
        <guides:changelog-day date="August">
            <guides:changelog-entry type="feature">
                Group support note can include comment 'type'
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Gender is an editable list
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Fix chart downloading
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Allow support history to generate a support plan at any point in time (no ui as yet)
            </guides:changelog-entry>
        </guides:changelog-day>
        <guides:changelog-day date="July">
            <guides:changelog-entry type="feature">
                Questionnaires now mimic other evidence pages - so benefits from draft/relogin save and mobile-first
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Dedicated evidence security group
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                'new referral' wizard to obey 'start'-ing a referral automatically on creation
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Allow service/project security to be prepared before enabling, and 'give all access option'
            </guides:changelog-entry>
        </guides:changelog-day>

    </guides:changelog-release>

    <guides:changelog-release version="18.07">
        <guides:changelog-day date="June">
            <guides:changelog-entry type="feature">
                Migrating to audit-first 'tasks' means its currently unavailable for offline
            </guides:changelog-entry>
        </guides:changelog-day>
            <guides:changelog-entry type="fix">
                Offline appointments visible to staff
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Audited editing by staff if unsigned (managers otherwise)
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Audited evidence deletions: request -> task for manager -> delete
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                GDPR: Anonymise test systems properly
            </guides:changelog-entry>
        <guides:changelog-day date="May">
            <guides:changelog-entry type="love">
                More standard reports for audits, reviews and risk flags outstanding
            </guides:changelog-entry>
        </guides:changelog-day>
            <guides:changelog-entry type="feature">
                Offline chain/related/associated actions fixed
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Group support permissions
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Upload size restored
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Email service manager based on new evidence entry (eg safeguarding) or close-off
            </guides:changelog-entry>
    </guides:changelog-release>

    <guides:changelog-release version="18.05">
            <guides:changelog-entry type="feature">
                Sync many offline referrals from calendar entries
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Group support security restrictions
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Report pie charts multi-select segments
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Emergency info to audited page
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Forward plan tab on client files
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Custom form: 'details of referral' (and data migration)
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Audit report can be ran per user
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Calendar end dates rounded
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                External referrals
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Custom forms
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Delete request/delete of evidence
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Multiple/repeating smart steps
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                SLA due dates in referrals list
            </guides:changelog-entry>
    </guides:changelog-release>

    <guides:changelog-release version="17.11">
            <guides:changelog-entry type="fix">
                Report: audit report can filter by user
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Admin access for group support
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                New Referral v2: allow accept without ticking d.p. signature box
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                New Referral v2: fix address without post code
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Prioritise correct client address
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Calendar: fix event durations - end time out by 1 minute
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Calendar: ad-hoc entries managed by user who created them (and managers)
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Custom blurb at end of evidence pages
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Calendar: 'overlay' staff performance [Oracle]
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Calendar: 'overlay' staff list only those relevant to 'my permissions'
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                IE issue on global session data
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Printing: printing tweaks 'reset printable'
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Access: 'overseer' and 'overseer-history' for different referral permission
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Calendar: next review dates on client file shows those due today
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Autocomplete off for new referral v2
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Adding of projects to a service in admin -> service -> admin mode
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Performance of services/serviceTypes got from already received data
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Calendar: reviews & setup meeting produce one date with attendees
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Fix occasional error on new 'audit history' tab
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Out of area wasn't saving when ticked for new agencies during new referral process.
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Fix Internet Explorer failing to display uploaded client photos
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Allow reporting to be done on complex service/project heirarchies as part company units and
                service groups.  Please contact us if you think this would be useful to your organisation.
            </guides:changelog-entry>
    </guides:changelog-release>
    <guides:changelog-release version="17.10">
            <guides:changelog-entry type="love">
                Notifications are now Google Material Design styled 'toasts' when confirming that changes have been
                saved.  Previously on small screens, the saved notification could be missed.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Update new referral flow to incorporate required and extra fields such as requiring date of birth,
                and allowing NI number to be entered.
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Mobile user performance: Improvements made to reduce data sizes and cache more data resulting in
                faster page loads, particularly benefitting mobile users.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Reports: Performance improvements (including resolving IIS issue for self-hosted ECCO customers).
            </guides:changelog-entry>
            <guides:changelog-entry type="change">
                New referrals: Simpler, faster and generally more friendly screens for creating a new referral
                are now the default.
            </guides:changelog-entry>
    </guides:changelog-release>
    <guides:changelog-release version="17.09">
            <guides:changelog-entry type="love">
                What! Nothing? How dare you!<br>
                Actually. Be reassured.  We got a chance to catch our breath and do some of the important but
                not urgent things that'll help with what we've got coming for 2018.
            </guides:changelog-entry>
    </guides:changelog-release>
    <guides:changelog-release version="17.08">
            <guides:changelog-entry type="improvement">
                Printing screens: evidence screens can have 'printable' when 'show all text' feature is on.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Each 'support history' entry now shows the page name that it was saved in
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Difference questionnaire report to show numbers (eg money) ups and downs across all clients for the period.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Printing screens: consent/data protection/support history (signatures now show underneath work item).
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Replace 'support history' filtering with dedicated search panel including searches on work date, targets,
                source page.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                'new referral' wizard v2 improvements
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                HACT 'beyond year 1' calculations and reports.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                NI number always under the client details on a referral home page.
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Accessibility tints - set on login page
            </guides:changelog-entry>
            <guides:changelog-entry type="wip">
                Experimental feature to show all text on support plan 'boxes' (eg smart step plan), also allows printing.
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Login page new look to allow scope for new authentication mechanisms.
            </guides:changelog-entry>
    </guides:changelog-release>

    <guides:changelog-release version="17.07">
            <guides:changelog-entry type="feature">
                Wellbeing questionnaire movement report matrix showing the changes between two dates of individual client
                answers aggregated up into a grid of all client movements for each question, therefore showing the movement
                of client wellbeing with breakdown of the underlying data.
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Admin pages for handling a service's pathway now displays with meaningful names.
                Guidance notes have been added on 'add task definition' and features. Also added 'type' as an editable list.
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Visual support plan gains repeating/multiple smart steps
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Outcome Star&trade; support for customers who are using Outcome Star paperwork. A more basic version
                for those that don't.
            </guides:changelog-entry>
    </guides:changelog-release>

    <guides:changelog-release version="17.06">
            <guides:changelog-entry type="feature">
                Allow clients from other services to be manually added to a group support activity (feature toggled)
            </guides:changelog-entry>
            <security:authorize access="hasRole('ROLE_ADMINREFERRAL')">
            <guides:changelog-entry type="improvement">
                    Audit history now shows timestamp of the change separately from the relevant date (e.g. work date)
                    that the data relates to.  This allows there to be no ambiguity if there is an incident where
                    where authorities are called in.
                </guides:changelog-entry>
            </security:authorize>
            <guides:changelog-entry type="love">
                Add auto-save feature for comments on dated case notes pages such as needs assessment,
                support plan, risk management, manager notes.  This retains drafts for 4 days so you can interrupt your
                work, go to other pages and go back.
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Allow background colour to be overridden from the
                <i class="fa fa-lightbulb-o" style="color: inherit; padding: 2px"></i> action on the login
                page.  Please let us know if you have colour requirements that this doesn't meet.
            </guides:changelog-entry>
    </guides:changelog-release>

    <guides:changelog-release version="17.03">
            <guides:changelog-entry type="feature">
                Allow evidence to be edited via context menu in the support and risk history
            </guides:changelog-entry>
        <security:authorize access="hasRole('ROLE_SOFTDELETE')">
                <guides:changelog-entry type="improvement">
                    Those with access to 'referral deletion requests' will now be able to 'request delete', 'cancel request'
                    and 'delete' - also with reasons. Furthermore, this is done with audits (so there is no need to use zendesk).
                    Since the deletion request is just that (a request) it would be good to get feedback on which permission(s)
                    this should be given to.
                </guides:changelog-entry>
        </security:authorize>
            <guides:changelog-entry type="fix">
                ECCO will now take you to the page you were heading for when you got redirected to login.
                This was not working for users who had offline activated.
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Much love has been given to reports. The benefit on screen so far is editing the criteria being
                much clearer and accurate for the report being used. However, the real work brings us closer to
                allowing users to change some of the reports elements.
            </guides:changelog-entry>
            <guides:changelog-entry type="change">
                ECCO will now prevent needs/support/risk pages from accepting a work date that is more than 28 days
                in the past. This has been done in part to remove the frequent New Year issues of people entering the
                previous year (which we've all done back in the days we had to write cheques), as well as potential
                issues where an up/down arrow key can change the date while navigating between input boxes.
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Identify and fix a few scenarios where reviews might not show the correct audit information
                or prevent a review from completing.
            </guides:changelog-entry>
    </guides:changelog-release>

    <guides:changelog-release version="16.12">
            <guides:changelog-entry type="feature">
                New work done while offline will be visible in the history immediately and highlighted to show it is not yet synchronised.
                This includes the signing of work while offline.
            </guides:changelog-entry>
            <security:authorize access="hasRole('ROLE_SWITCHUSER')">
                <guides:changelog-entry type="improvement">
                    If you're impersonating a user you now don't need to logout and back in to get back, you can just
                    select <kbd><i class="fa fa-user-secret" style="color:white;"></i> stop impersonating</kbd> from the user dropdown menu at the top right.
                </guides:changelog-entry>
            </security:authorize>
            <guides:changelog-entry type="love">
                A search box is now available (we can switch this on for you) on the welcome page, which makes finding
                a client and opening their file notably simpler.
            </guides:changelog-entry>
            <guides:changelog-entry type="fix">
                Ensure that on the client calendar, the client, not the user, is the default attendee, and the
                user (i.e. you) can be ticked to also be part of that appointment.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Support history is now initially limited to 20 entries with "more..." shown at bottom to click on
                for full support history.
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                First location based feature: option to record GPS location when work is saved
            </guides:changelog-entry>
    </guides:changelog-release>
    <guides:changelog-release version="16.09">
            <guides:changelog-entry type="improvement">
                Improve usability of filters on support history.
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Allow action details to be hidden on support history
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Improve login so that users get to their target page after login prompt<br>
                [Although not for users with offline enabled]
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Created "hybrid cloud" option for integration with on-premises Northgate and QL housing systems
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Synchronise and show appointments for offline users
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Allow siblings to be linked on relationships tab when both are already a client
            </guides:changelog-entry>
            <guides:changelog-entry type="feature">
                Add option to review and sign work (needs assessment, support plan, rota visit) before saving it.
                Includes offline.
            </guides:changelog-entry>
            <guides:changelog-entry type="love">
                Add "today" quick link to set work date to today when inputting support work.
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Users will now be prompted to re-login if the system times out their session and they submit support
                work or are working with reports.
            </guides:changelog-entry>
    </guides:changelog-release>


    <guides:changelog-release version="16.06">
            <guides:changelog-entry type="improvement">
                Show smart step group name in support history
            </guides:changelog-entry>
            <guides:changelog-entry type="improvement">
                Implement collapsible smart step groups as from legacy evidence screens
            </guides:changelog-entry>
            <guides:changelog-entry module="reports" type="feature">
                ability to report on questionnaires (upon request)
            </guides:changelog-entry>
            <guides:changelog-entry module="security" type="improvement">
                ecco added 'service admin' role - first addition is group support admin for their services
            </guides:changelog-entry>
            <guides:changelog-entry module="referrals" type="feature">
                ecco learned to to search by postcode on the 'find referrals' page.
            </guides:changelog-entry>
            <guides:changelog-entry module="contacts" type="improvement">
                ecco now automatically ensures entered postcodes are formatted uppercase and with a space, if they
                look like a full valid UK postcode.
            </guides:changelog-entry>
            <guides:changelog-entry module="contacts" type="love">
                ecco has automatically cleaned up all historically postcodes to ensure they are formatted as
                a normal valid UK postcode.
            </guides:changelog-entry>
            <guides:changelog-entry module="reports" type="feature">
                ecco learned to give system administrators the ability to give reports a friendly name for
                showing in the reports list.
            </guides:changelog-entry>
            <guides:changelog-entry module="evidence" type="feature">
                Complete implementation of modern architecture evidence screen features to match features
                on legacy implementation
            </guides:changelog-entry>
            <guides:changelog-entry module="monitoring" type="improvement">
                [technical] Enhance server monitoring to give overall 'health' status including a check of available
                connections to serve new users.
            </guides:changelog-entry>
            <guides:changelog-entry module="group support" type="feature">
                Allow administrators to delete unused group activities
            </guides:changelog-entry>
            <guides:changelog-entry module="evidence" type="feature">
                Allow items to sign to be selected when signing support work
            </guides:changelog-entry>
    </guides:changelog-release>

</ecco:quick-guide-snippet>
