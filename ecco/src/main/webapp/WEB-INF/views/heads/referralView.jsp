<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>

<%@taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs"%>
<requireJs:import modules="editable/editableInit"/>

<c:url var="apiPath" value="/api/"/>

<c:if test="${actionbar_uitabs && multipleReferralsAllowed}">
    <c:import url="/WEB-INF/views/heads/tabs.jsp"/>
    <%-- override a setting brought in with tabs.jsp which is assumes that tabs are being used in the main body of the page  --%>
    <%-- which is picked up in page-content, where as we have actionbar_uitabs to indicate tabs are there - not in main body --%>
    <%-- the tag definition should not be in tabs.jsp --%>
    <c:set var="uitabs" value="false" scope="request"/>
</c:if>

<script type="text/javascript">

    <c:if test="${useNewClientFile}">
        // user audit
        require(['jquery', 'controls/layout/Sidebar', 'referral/ReferralOverviewControl'],
            function ($, Sidebar, ReferralOverviewControl) {
                $(function() {
                    var sidebar = new Sidebar();
                    sidebar.attach($("#sidebar"));
                    var ctl = new ReferralOverviewControl(${referralId}, sidebar, ${true});
                    ctl.load();
                });
            });
    </c:if>

</script>
