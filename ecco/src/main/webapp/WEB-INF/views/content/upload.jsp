<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<div class="rounded">
<div id="stillUploading" style="display: none;" title="">
    <p>there are files still uploading - do you want to cancel them and carry on moving pages?</p>
</div>

<div class="entityForm bottom-gap-15">

<c:if test="${!disableUpload}">
    <c:set var="statusMessage" value="to upload click the button, or drag files into it" scope="request"/>
    <%@ include file="/WEB-INF/views/content/statusMessage.jsp" %>
</c:if>


<c:choose>
    <c:when test="${not empty param.backToUrl}">
        <div class="clearfix">
            <div class="back-float">
                <a href="${param.backToUrl}">back</a>
            </div>
        </div>
    </c:when>
    <c:when test="${source  == 'outcomeRisk'}">
        <div class="clearfix">
            <div class="back-float">
                <spring:url var="url" value="/dynamic/secure/supportPlanFlow.html"><spring:param name="referralId" value="${param.referralId}"/><spring:param name="clientId" value="${param.clientId}"/></spring:url>
                <a href="${url}">back to support plan</a>
            </div>
        </div>
        </c:when>
</c:choose>

    <br/>
    <c:if test="${disableUpload}">
        <div>
            <h1>uploaded files</h1>
        <c:if test="${empty uploads}">
            <p class="center">no files have been attached here</p>
        </c:if>
        </div>
    </c:if>
    <c:if test="${!disableUpload}">

        <div style="margin: 0 auto; width: 200px; text-align: left;">
            <form:form id="file_upload" cssClass="jsDontDirty" modelAttribute="upload" action="uploadHidden.html?parentId=${parentId}" enctype="multipart/form-data" >
                <input type="file" name="file" multiple>
                <button>upload files</button>
                <div class="jsShow" style="text-align: center;">upload files</div>
            </form:form>
        </div>
    </c:if>

    <div style="display:none;" id="template_error">
        <p class="ui-state-error ui-corner-all" style="padding: 0 .7em;">
            <span class="ui-icon ui-icon-alert" style="float: left; margin-right: .3em;"></span>
        </p>
    </div>

    <%-- see http://forums.asp.net/t/1002313.aspx/1 --%>
    <table class="view" id="files" style="margin-left: auto; margin-right: auto; border-collapse: separate; width: auto;" cellpadding="0" cellspacing="10">
        <tr id="template_upload" style="display:none;">
            <td class="file_upload_name" style="border-collapse: separate; border: 0;"></td>
            <td class="file_upload_size" style="border-collapse: separate; border: 0;"></td>
            <td class="file_upload_progress" style="border-collapse: separate; border: 0;"><div></div></td>
            <td class="file_upload_cancel" style="border-collapse: separate; border: 0;">
                <button class="ui-button ui-widget ui-state-default ui-corner-all" title="cancel">
                    <span class="ui-icon ui-icon-cancel">cancel</span>
                </button>
            </td>
        </tr>
        <tr id="template_download" style="display:none;">
            <td class="file_upload_name" style="border-collapse: separate; border: 0;"><a></a></td>
            <td class="file_upload_size" style="border-collapse: separate; border: 0;"></td>
            <!-- we need a td still, otherwise uploadReferral won't show the avatar -->
            <security:authorize access="!hasRole('ROLE_ADMINREFERRAL')">
                <td style="display: none;"></td>
            </security:authorize>
            <security:authorize access="hasAnyRole('ROLE_ADMINREFERRAL')">
                <td class="file_upload_delete" colspan="3" style="display: none; border-collapse: separate; border: 0;">
                    <button class="ui-button ui-widget ui-state-default ui-corner-all" title="delete">
                        <span class="ui-icon ui-icon-trash">delete</span>
                    </button>
                </td>
            </security:authorize>
            <td class="file_upload_extra" style="border-collapse: separate; border: 0; padding-left: 24px;"></td>
        </tr>

    </table>

</div>
</div>