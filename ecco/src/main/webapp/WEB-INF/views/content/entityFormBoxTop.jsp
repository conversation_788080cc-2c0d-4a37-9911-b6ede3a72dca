<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<%@ include file="/WEB-INF/views/content/statusMessage.jsp" %>

    <%-- find any errors --%>
    <c:set var="errorCount" value="0"/>
    <c:if test="${empty entityErrorPath}">
        <c:set var="emptyErrorPath" value="true"/>
        <c:set var="entityErrorPath" value="entity"/>
    </c:if>
    <c:if test="${not empty entityErrorPath}">
        <c:set var="emptyErrorPath" value="false"/>
    </c:if>
    <spring:hasBindErrors name="${entityErrorPath}">
        <c:set var="errorCount" value="${errors.errorCount}"/>
    </spring:hasBindErrors>

    <c:if test="${errorCount > 0}">

        <c:if test="${not empty statusMessage}">
            <br/>
        </c:if>

        <c:if test="${errorCount == 1}">
            <spring:message var="errorHdr" code="problem.message"/>
        </c:if>
        <c:if test="${errorCount >= 2}">
            <spring:message var="tmp" code="problems.message"/>
            <c:set var="errorHdr" value="${errors.errorCount} ${tmp}"/>
        </c:if>

        <div class="error ui-state-error ui-corner-all" style="padding: 0 .7em; width: 40%; margin: 0 auto;">
        <p>
            <span class="ui-icon ui-icon-alert" style="float: left; margin-right: .3em;"></span>
            <span class="errortext">

                <strong>${errorHdr}</strong>
                <br>

                <c:if test="${emptyErrorPath == 'false'}">
                    <c:set var="entityErrorPathDetail" value="${entityErrorPath}.*"/>
                </c:if>
                <c:if test="${emptyErrorPath == 'true'}">
                    <c:set var="entityErrorPathDetail" value="entity.*"/>
                </c:if>
                <spring:hasBindErrors name="${entityErrorPath}">
                    <spring:bind path="${entityErrorPathDetail}">
                        <c:forEach items="${status.errorMessages}" var="errorMessage">
                            - <c:out value="${errorMessage}"/>
                            <br>
                        </c:forEach>
                    </spring:bind>
                </spring:hasBindErrors>

            </span>
        </p>
        </div>
    </c:if>

<%-- this was the entity name - now its just entity! ${entityNameVar} --%>
<%-- this lets us see all the errors in one place (global and fields) --%>
<%--
taxExpiryEvent.reminderEmail.daysAdvance
<c:if test="${status.error}">
    <c:out value="${status.errorMessage}"/>
</c:if>
--%>
<%-- this was the entity name - now its just entity! ${entityNameVar} --%>
<%-- we allow print... and send... to not be entity --%>
<%--
    entityErrorPath: <c:out value="${entityFormName}"/><br>
--%>
