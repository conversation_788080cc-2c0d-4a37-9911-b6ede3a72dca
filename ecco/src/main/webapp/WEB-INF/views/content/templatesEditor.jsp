<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@include file="/WEB-INF/views/reference-data.jsp" %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="bs" tagdir="/WEB-INF/tags/bootstrap" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page" %>

<page:rich-client-page title="edit templates" importRequireJsModules="bootstrap" devMode="${devMode}">
    <page:page-banner>
        <bs:navbar>
            <ul class="nav navbar-nav navbar-right">
                <bs:user-dropdown>
                    <li><a href="#">(no actions)</a></li>
                </bs:user-dropdown>
            </ul>
        </bs:navbar>
    </page:page-banner>
        <script>
        require(['jquery'], function($) {
            $(function ($) {
                var apiRoot = "<c:out value="${applicationProperties.applicationRootPath}"/>api/";

                var $nameInput = $("#template-name");
                var $getButton = $("#get-process");
                var $saveButton = $("#save-process");
                var $status = $("#status");
                var $textarea = $("#process-xml");

                $getButton.click(function () {
                    $nameInput.prop("disabled", true);
                    $getButton.prop("disabled", true);
                    $saveButton.prop("disabled", true);
                    $status.text("loading");
                    $textarea.text("")
                            .prop("disabled", true);

                    $.ajax({
                        type: "GET",
                        url: apiRoot + "templates/" + $nameInput.val() + "/", /* TODO: encodeURIComponent when it is components */
                        dataType: "text",
                        complete: function () {
                            $nameInput.prop("disabled", false);
                            $getButton.prop("disabled", false);
                            $saveButton.prop("disabled", false);
                            $textarea.prop("disabled", false);
                        },
                        error: function () {
                            $status.text("load failed");
                        },
                        success: function (data) {
                            $textarea.val(data)
                            $status.text("");
                        }
                    });
                });

                $saveButton.click(function () {
                    $nameInput.prop("disabled", true);
                    $getButton.prop("disabled", true);
                    $saveButton.prop("disabled", true);
                    $status.text("saving");
                    $textarea.prop("disabled", true);

                    $.ajax({
                        type: "POST",
                        url: apiRoot + "templates/" + $nameInput.val() + "/",
                        contentType: "application/xml; charset=UTF-8",
                        data: $textarea.val(),
                        complete: function () {
                            $nameInput.prop("disabled", false);
                            $getButton.prop("disabled", false);
                            $saveButton.prop("disabled", false);
                            $textarea.prop("disabled", false);
                        },
                        error: function () {
                            $status.text("save failed");
                        },
                        success: function () {
                            $status.text("saved successfully");
                        }
                    })
                })
            });
        });
        </script>

        <div id="content" class="container-fluid" style="margin:2%;">
            <div class="e-row">
                <div class="input-group input-group-sm">
                    <label>Template:</label>
                    <select id="template-name" name="contact">
                        <option value="-1">-</option>
                        <optgroup label="Markdown">
                            <option value="WORKER_ASSIGNED_EVENT/markdown/WorkerAssigned">Email - WorkerAssigned</option>
                            <option value="REFERRAL/markdown/DataProtection">Referral - DataProtection</option>
                            <option value="REFERRAL/markdown/ReferralSummary">Referral - ReferralSummary</option>
                            <option value="REFERRAL/markdown/ReferralCreated">Referral - ReferralCreated</option>
                        </optgroup>
                        <optgroup label="HTML">
                            <option value="REFERRAL/html/ocs">Referral - OCS</option>
                        </optgroup>
                    </select>
                </div>
                <div class="btn-group">
                    <button id="get-process" class="btn btn-sm btn-default">get</button>
                    <button id="save-process" class="btn btn-sm btn-primary">save</button>
                </div>
                <span id="status"></span>
            </div>
            <div class="e-row">
                <textarea id="process-xml" style="font-family: monospace; width:100%;" rows="40"></textarea>
            </div>
        </div>
</page:rich-client-page>
