<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<style type="text/css">
    th.u {
        background-color: #999999;
    }

    th.d {
        background-color: #cccccc;
    }
</style>

<div style="text-align: center; font-size: 1.1em;">
    system time for date range: ${from} - ${to}
    <br/>
</div>

<div class="entityForm">

<div style="margin-left: 80px;">total: ${fn:length(entities)}</div>

<table class="view">
    <thead>
        <tr class="view-header">
            <th class="u">id</th>
            <th class="u">username</th>
            <th class="d">first request</th>
            <th class="d">last request</th>
            <th class="d">time (mins)</th>
            <th class="d">clicks</th>
        </tr>
    </thead>
    <tbody>
        <c:forEach var="click" items="${entities}" varStatus="loopIndex">
            <c:set var="first" value="${medFn:convertUtcToUsersDateTime(click.firstRequest)}"/>
            <c:set var="last" value="${medFn:convertUtcToUsersDateTime(click.lastRequest)}"/>
            <tr>
                <td>${click.id}</td>
                <td><c:out value="${click.username}"/></td>
                <td>${not empty first ? first.toString("yyyy-MM-dd HH:mm:ss") : ""}</td>
                <td>${not empty last ? last.toString("yyyy-MM-dd HH:mm:ss") : ""}</td>
                <td>${medFn:timeBetween(click.firstRequest, click.lastRequest, "minutes")}</td>
                <td>${click.totalRequests}</td>
            </tr>
        </c:forEach>
    </tbody>
</table>

</div>
