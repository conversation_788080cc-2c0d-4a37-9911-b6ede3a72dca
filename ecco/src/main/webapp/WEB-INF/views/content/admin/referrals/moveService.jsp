<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@ taglib prefix="page" tagdir="/WEB-INF/tags/page" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ include file="/WEB-INF/views/pathvars.jsp" %>


<page:rich-client-page title="move referrals"
    importRequireJsModules="environment admin/referrals/deletionsInit admin/referrals/moveServiceInit controls/banner-titlebar bootstrap" devMode="${devMode}">
        <page:page-banner>
            <page:page-menus-bs quickGuideId="quickGuide" noCollapse="true"/>
        </page:page-banner>
        <page:page-titlebar>
            <div id="status"></div>
            <div id="controls"></div>
            <div id="properties">
                <page:modal title="Guide" id="quickGuide">
                    <p>Moves a referral to another service
                    </p>
                    <ul>
                        <li>Any project will be matched if it exists on the new service, otherwise it will be empty - but it can be set again from the client file.</li>
                    </ul>
                </page:modal>
                <div style="text-align: center;">
                    move referrals
                </div>
            </div>
        </page:page-titlebar>
        <div class="content">

            <spring:message var="lastRidEntry" code="lastReferralID"/>
            <c:if test="${(not empty lastRidEntry) && (lastRidEntry > 0)}">
                <div class="error ui-state-error ui-corner-all" style="padding: 0 .7em; width: 40%; margin: 0 auto;">
                <p>
                    <span class="ui-icon ui-icon-alert" style="float: left; margin-right: .3em;"></span>
                    <span class="errortext">
                        WARNING: This site is configured with 'lastReferralID' - an indication to progress r-id from an existing numbering system. This means the r-id shown on the screens is not the system r-id expected here. Try using the referral code.
                        <br>
                    </span>
                </p>
                </div>
            </c:if>

            <div class="row">
                <div class="col-md-offset-1 col-md-10" style="text-align: center;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <strong>1 - Find referral by id or code to check you have the right one</strong>
                        </div>
                        <div class="panel-body">
                            <p style="font-style: italic;">N.B. please see the quick guide above.</p>
                            <p>referral id: <span id="referralSearch"></span>
                            </p>
                            <p>referral code: <span id="referralSearchCode"></span>
                            </p>
                            <p>find referral: <span id='referralSearchHidden'></span></p>
                            <p><strong>matching referral</strong></p>
                            <div id="results" class="table-responsive center" style="display: inline-block"></div>
                            <p><strong>relationships</strong></p>
                            <p>(if family service)</p>
                            <div id="resultsRelationships" class="table-responsive center" style="display: inline-block"></div>
                        </div>
                    </div>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <strong>2 - Enter service id to move the referral</strong>
                        </div>
                        <div class="panel-body">
                            <p style="font-style: italic;">N.B. this is obtainable in the 'services' admin page.</p>
                            service id: <span id="moveToService"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</page:rich-client-page>
