<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@ taglib prefix="page" tagdir="/WEB-INF/tags/page" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ include file="/WEB-INF/views/pathvars.jsp" %>

<page:rich-client-page title="delete evidence snapshots"
    importRequireJsModules="environment admin/evidence/deletionsInit controls/banner-titlebar bootstrap" devMode="${devMode}">
        <page:page-banner>
            <page:page-menus-bs quickGuideId="quickGuide" noCollapse="true"/>
        </page:page-banner>
        <page:page-titlebar>
            <div id="status"></div>
            <div id="controls"></div>
            <div id="properties">
                <page:modal title="Guide" id="quickGuide">
                    <p>The system provides safety checks to ensure the right information is deleted through a typical process:
                    </p>
                    <ul>
                        <li>Fill in the referral id (you may need to find it using the referral code first) and click <strong>find evidence</strong></li>
                        <li>To delete the entry, complete the work uuid then click <strong>delete workUuid</strong>
                    </ul>
                    <p>Exceptions to this are as follows:
                    <ul>
                        <li>?The evidence is signed? - will fail?</li>
                    </ul>
                </page:modal>
                <div style="text-align: center;">
                    delete evidence snapshots
                </div>
            </div>
        </page:page-titlebar>
        <div class="content">

            <div class="row">
                <div class="col-md-offset-1 col-md-10" style="text-align: center;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <strong>find evidence from referral id and enter the uuid to delete</strong>
                        </div>
                        <div class="panel-body">
                            <p>
                                <label class="radio-inline"><input type="radio" name="repoType" value="support">support</label>
                                <label class="radio-inline"><input type="radio" name="repoType" value="risk">risk</label>
                                <label class="radio-inline"><input type="radio" name="repoType" value="form">form</label>
                            </p>
                            <p>referral code: <span id="referralSearchCode"></span>
                            </p>
                            <p>referral id: <span id="referralSearch"></span>
                            </p>
                            <p>workUuid: <span id="deleteWorkUuid"></span>
                            </p>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <strong>evidence</strong>
                        </div>
                        <div class="panel-body">
                            <strong>WARNING: the rendering of data here is not as accurate as the main pages</strong>
                            <div id="results">
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
</page:rich-client-page>

