package com.ecco.security.ldap.web;

import com.ecco.security.ldap.service.LdapGroupAggregate;
import com.ecco.security.ldap.service.LdapGroupMappingService;
import com.ecco.web.BaseController;
import com.ecco.exceptions.UnexpectedResultSizeException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.provisioning.GroupManager;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Controller
@RequestMapping("/secure")
@PreAuthorize("hasRole('ROLE_ADMINLOGINLDAP')")
public class LdapGroupMappingEditController extends BaseController {

    @Autowired
    private LdapGroupMappingService service;

    @Resource(name = "customSpringJdbcUserDetailsManager")
    private GroupManager groupManager;
    private static final String VIEW_NAME = "ldapGroupMappingDef";
    private static final String LIST_VIEW = "redirect:/nav/secure/settings/ldapGroupMapping/list.html";

    @ModelAttribute("eccoSecurityGroups")
    public List<String> getEccoSecurityGroups() {
        return groupManager.findAllGroups();
    }

    @RequestMapping(value ="/settings/ldapGroupMapping/edit.html", method = RequestMethod.GET)
    protected ModelAndView showForm(@RequestParam(required = false) String ldapGroup) throws Exception {
        ModelAndView mav = new ModelAndView(VIEW_NAME);
        try {
            mav.addObject("ldapGroupAggregate", service.find(ldapGroup));

        } catch (UnexpectedResultSizeException e) {
            mav.addObject("ldapGroupAggregate", new LdapGroupAggregate());
        }
        return mav;
    }

    @RequestMapping(value ="/settings/ldapGroupMapping/edit.html", method = RequestMethod.POST)
    protected ModelAndView handleFormSubmit(@Valid LdapGroupAggregate ldapGroupAggregate, BindingResult result) throws Exception {
        ModelAndView mav = new ModelAndView();
        if (result.hasErrors()) {
            mav.setViewName(VIEW_NAME);
        } else {
            service.save(ldapGroupAggregate);
            mav.setViewName(LIST_VIEW);
        }
        return mav;
    }

    @RequestMapping(value ="/settings/ldapGroupMapping/delete.html", method = RequestMethod.GET)
    protected ModelAndView showDeleteConfirm(@RequestParam String ldapGroup) throws Exception {
        ModelAndView mav = showForm(ldapGroup);
        mav.addObject("showDeleteConfirm", true);
        return mav;
    }

    @RequestMapping(value ="/settings/ldapGroupMapping/delete.html", method = RequestMethod.POST)
    protected ModelAndView handleDeleteFormSubmit(@RequestParam String ldapGroup) throws Exception {
        ModelAndView mav = new ModelAndView();
        service.delete(ldapGroup);
        mav.setViewName(LIST_VIEW);
        return mav;
    }


}
