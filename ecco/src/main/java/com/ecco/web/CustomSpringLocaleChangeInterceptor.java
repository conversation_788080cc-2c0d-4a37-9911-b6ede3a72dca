package com.ecco.web;

import com.ecco.security.SecurityUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.propertyeditors.LocaleEditor;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import org.springframework.web.servlet.support.RequestContextUtils;
import org.springframework.web.util.WebUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

// the locale coming from the web cannot be relied upon - often just the langauge
// http://www.w3.org/International/questions/qa-accept-lang-locales
// traditionally only for language - so don't rely on it
// its constructed using 2 or 3 codes - but we just take the locale object

// ideally we would want the locale to be set when they are logged in,
// however it would need some thread local stuff on the success event, as the locale is stored in the request/session
// so instead we set the locale on the interceptor - here
// which therefore includes all the logic on resolving the locale

// there we need 3 ways to determine locale
// the user saved values - user could be in different country or a different domain, still want their own language
// the domain - if no user value is set, the user probably wants the country of the domain, eg could be abroad looking at site - want domain language
// the browsers attempt - if no user value us set, this is useful if the domain provides a large scope (eg america with spanish speakers)

// the logic to setting the locale is:
/*
- 1 if a user is logged in use that, unless...
    a - if a 'locale' paramter exists, use that
    b - else use the users locale
- 2 if no user is logged in...
    a - if a 'locale' paramter exists, use that
    b - if no 'locale' paramter is set...
        c - use a domain locale - if exists
        d - else use the header (standard interceptor usage, since sessionresolver uses header by default)
*/

public class CustomSpringLocaleChangeInterceptor extends HandlerInterceptorAdapter {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    public static String LOCALE_PARAM_NAME = "locale";
    public void setParamName(String paramName) {
        LOCALE_PARAM_NAME = paramName;
    }

    // **************
    // the 3 LOCALES
    // we use userLocale directly
    /*
    public Locale getUserLocale(HttpServletRequest request) {
        if (currentUserDao.isUserLoggedIn())
            // we assume this can't be null
            return currentUserDao.getUserLocale();
        return null;
    }
    */
    // getDomainLocale exists in WebUtils
    // request header locale is not used, since this is defaulted in sessionlocaleresolver
    /*
    public Locale getRequestHeaderLocale(HttpServletRequest request) {
        // returns accept-language header or server default locale
        // this is as per SessionLocaleResolver
        return request.getLocale();
    }
    */
    // **************

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws ServletException {

        // 1 - a user is logged in
        if (SecurityUtil.authenticatedUserExists()) {

            // a
            // TODO we need to determine here if we change the locale based on a 'locale' request parameter
            // TODO we also need to avoid resetting the locale to the users as what happens currently, see below

            // b
            // if we call localResolver.resolveLocale - it will not be null since it will delegate to request.getLocale etc
            // but if we simply get the attribute name, we can see if a specific locale has been set
            Locale locale = (Locale) WebUtils.getSessionAttribute(request, SessionLocaleResolver.class.getName() + ".LOCALE");

            // if its null or different then the user's locale then its not been set yet or its changed, so we set it to the users
            // if its not changed, we don't want to bother setting it again
            // test changing the locale with:
            // ?locale=en_GB
            if ((locale == null) || (!(locale.equals(SecurityUtil.getUserLocale())))) {
                LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
                // currently, upon any change we set to the users locale regardless
                localeResolver.setLocale(request, response, SecurityUtil.getUserLocale());
            }

            // proceed with chain
            return true;
        }

        // 2 - user not logged in
        String newLocale = ServletRequestUtils.getStringParameter(request, LOCALE_PARAM_NAME);
        // domain locale is now irrelevant with .com. if .co.uk is still in use, then our logic is still sound
        // in that we determine the site from the closest locale, or the registered locale
        // we also don't want to rely on a domain parameter which could be duplicated which may confuse med
        String domainLocale = null;//WebUtils.getDomainLocale(request);

        // if we don't have any locale parameters, then we are .com and so use the default header behaviour (provided by sessionresolver)
        if ((newLocale == null) && (domainLocale == null)) {
            return true;
        }

        // a - locale has been set
        // if we do have a locale parameter, then we could be any non-user requesting a change
        if (newLocale != null) {
            LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
            LocaleEditor localeEditor = new LocaleEditor();
            localeEditor.setAsText(newLocale);
            localeResolver.setLocale(request, response, (Locale) localeEditor.getValue());
            return true;
        }
        // b - no locale param is set

        // c - medLocale exists
        // if we are from a domain with no other requirements (ie not user, not specified a user locale...)
        if (domainLocale != null) {

            // set the locale the first time but avoid doing any calcs otherwise
            // we can't go through locale resolver since we want to check the session directly
            Locale locale = (Locale) WebUtils.getSessionAttribute(request, SessionLocaleResolver.class.getName() + ".LOCALE");
            if (locale == null) {
                LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
                LocaleEditor localeEditor = new LocaleEditor();
                localeEditor.setAsText(domainLocale);
                localeResolver.setLocale(request, response, (Locale) localeEditor.getValue());
            }
            return true;
        }

        // d - use the default header behaviour (provided by sessionresolver)
        // proceed with the chain
        return true;
    }

}
