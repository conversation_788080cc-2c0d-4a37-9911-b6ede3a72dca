package com.ecco.web;

import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.ecco.web.admin.BaseAdminPageController;

@Controller
// ROLES: sysadmin / 'site sysadmin' / 'service admin'
@Secured({"ROLE_SYSADMIN", "ROLE_ADMIN", "ROLE_ADMINGROUPSUPPORT"})
public class AdminIndexController extends BaseAdminPageController {

    @RequestMapping(method=RequestMethod.GET, value="/secure/admin/")
    public String getIndex() {
        return "admin/adminIndex";
    }
}
