-- >> for SUPPORT/THREAT

-- find the work item from the overview, and note the url from the 'link' which has the editWorkId
-- this is the workId
<workId>
-- use the text given to delete so we can check its the right one
<sometext>
-- find the signature to delete (it hides the workId of the edit chain 'link')
<sigId>
-- see if we should also delete the signature (check there isn't other work using it - if there are don't run the delete from signature: the deleted work will be unlinked from the signature through its deletion)
select count(*) from supportplanwork where signatureId=<sigId>;
select count(*) from supportthreatwork where signatureId=<sigId>;

-- for support comment ONLY
-- check a record exists
select * from supportplanwork where id in (<workId>);
select * from supportplancomments where workId in (<workId>);
select * from supportplancomments where bc_comment like '%<sometext>%';
delete from supportplancomments where workId in (<workId>);
delete from supportplanwork_actions where supportplanworkId in (<workId>);
delete from supportplananswers where workid in (<workId>);
delete from supportplanactions where workid in (<workId>);
delete from supportplanoutcomes where workid in (<workId>);
delete from supportplanwork where id in (<workId>);
-- delete from signature where id=<sigId>

-- for threat comment ONLY
-- check a record exists
select * from supportthreatwork where id=<workId>;
select * from supportthreatcomments where workId=<workId>;
select * from supportthreatcomments where bc_comment like '%<sometext>%';
delete from supportthreatoutcomes where workId=<workId>;
delete from supportthreatactions where workId=<workId>;
delete from supportthreatcomments where workId=<workId>;
delete from supportthreatwork_actions where supportthreatworkId=<workId>;
delete from supportthreatflags where workId=<workId>;
delete from supportthreatwork where id=<workId>;
-- delete from signature where id=<sigId>;
