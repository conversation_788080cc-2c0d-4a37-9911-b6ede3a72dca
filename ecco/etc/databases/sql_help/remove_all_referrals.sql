update referrals set signpostedCommentId=null, exitCommentId=null;
delete from referralcomments;
delete from svcrec_attachments;
delete from uploadfile;
update contacts set avatarId = null;
delete from uploadbytes;

-- events
delete from contacts_events;
delete from events;
delete from eventsrecurring;
-- we need to delete the cosmo events if not removing the client
-- but its not possible going from cosmo, since its referral-agnostic
-- so we use the events.cal_uid - refer to remove_calendarEntries.sql

delete from cal_eventstatus;

-- support info
delete from evdnc_attachments;
delete from evdnc_form_comments;
delete from evdnc_form_snapshots;
delete from evdnc_form_work;
delete from evdnc_guidance;
delete from evdnc_supportactions;
delete from evdnc_supportanswers;
delete from evdnc_supportcomments;
delete from evdnc_supportflags;
delete from evdnc_supportoutcomes;
delete from evdnc_supportwork;
delete from evdnc_supportwork_actions;
delete from evdnc_threatactions;
delete from evdnc_threatcomments;
delete from evdnc_threatflags;
delete from evdnc_threatoutcomes;
delete from evdnc_threatwork;
delete from evdnc_threatwork_actions;


delete from referralactivities;
delete from grp_activities_referrals;
delete from grp_activities_workers;
delete from grp_activities;
delete from grp_attendance;
delete from grp_commands;

-- signatures
delete from signature;

delete from reviews;

delete from svcrec_contacts;
delete from svcrec_contacts_assocs;
-- delete from svcrec_commands;
delete from svcrec_commands_archive;
delete from svcrec_commands where serviceRecipientId in (select id from servicerecipients where discriminator_orm= 'rfrl');
delete from referral_to_gsat;
update referrals set primaryReferralId=null;
update referrals set parentReferralId=null;
delete from referrals;

delete from svcrec_taskstatus;

update appointmentschedules set parentScheduleId=null;
update appointmentschedules set previousScheduleId=null;
delete from appointmentscheduledirecttasks;
delete from appointmentschedules;
delete from appointmentagreements;

delete from addresshistory;
delete from svccat_contacts;

-- delete from servicerecipients;
delete from servicerecipients where discriminator_orm= 'rfrl';

-- AWOOGA BLOW UP WARNING: activiti clearing not throught through, but this got me past what I needed
delete from ACT_RU_IDENTITYLINK;
delete from ACT_RU_TASK;
update ACT_RU_EXECUTION set PARENT_ID_=null;
delete from ACT_RU_VARIABLE;
delete from ACT_RU_EXECUTION;
