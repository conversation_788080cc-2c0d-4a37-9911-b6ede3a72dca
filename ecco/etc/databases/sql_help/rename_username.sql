
-- check user exists
select id as usersId from users where username="<from>";
-- check new user doesn't exists
select id as usersId from users where username="<to>";
-- cosmo is linked by uid
-- select group_concat(id SEPARATOR ',') as cosmouid from cosmo_users where username="<from>";

-- update
start transaction;
update users set username="<to>" where username="<from>" limit 1;
-- update contacts set email='<to>@email.co.uk' where email='<from>@email.co.uk';
update acl_sid set sid="<to>" where sid="<from>" limit 1;
commit;
-- unused: update authorities set username="<to>" where username="<from>";
-- no 'username' in cosmo_users - only link between ecco and cosmo users is contacts.calendarId -> cosmo_item.cal_uid
--      and the username is generated in Individual.buildCalendarOwner
--      update cosmo_users set username="<to>" where username="<from>" limit 1;
-- no 'username' column, id=users.id: update users_AUD (based on id, not username) where id = <usersId>;
-- no 'username' column: group_members
-- no 'username' column: passwordhistory
