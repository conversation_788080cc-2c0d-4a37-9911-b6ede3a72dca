package com.ecco.service.security;

import com.ecco.dao.security.QueuedCommandRepository;
import com.ecco.dao.security.UserDeviceRepository;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.security.dom.QueuedCommand;
import com.ecco.security.dom.UserDevice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.net.URI;
import java.util.Optional;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CommandQueueManagerServiceImplTest {

    @Mock private EncryptionManagerService encryptionManagerService;
    @Mock private UserDeviceRepository userDeviceRepository;
    @Mock private QueuedCommandRepository queuedCommandRepository;
    @Mock private CommandRequestExecutor executor;

    private CommandQueueManagerServiceImpl commandQueueManagerService;

    @Before
    public void setUp() {
        commandQueueManagerService = new CommandQueueManagerServiceImpl(encryptionManagerService, userDeviceRepository, queuedCommandRepository);
    }

    @Test
    public void givenDeviceIsValidWhenCommandQueuedThenQueuedCommandSavedAndReturned() throws JsonProcessingException {
        final UUID guid = UUID.randomUUID();
        byte[] data = "It was seven o'clock of a very warm evening in the Seeonee hills".getBytes(Charsets.UTF_8);
        final Long commandId = RandomUtils.nextLong();
        UserDevice userDevice = mock(UserDevice.class);
        when(userDevice.isValid()).thenReturn(true);
        URI requestUrl = URI.create("http://www.gutenberg.org/files/236/236-h/236-h.htm");
        String requestMethod = HttpMethod.POST.name();
        String requestContentType = MediaType.APPLICATION_JSON_VALUE;
        String requestAcceptType = MediaType.APPLICATION_JSON_VALUE;
        String requestBody = "{\"when\": \"Father Wolf woke up from his day's rest\" }";

        String commandRequest = new ObjectMapper().writeValueAsString(ImmutableMap.of(
                "url", requestUrl.toString(),
                "method", requestMethod,
                "contentType", requestContentType,
                "acceptType", requestAcceptType,
                "body", requestBody
        ));

        when(userDeviceRepository.findOneByGuid(guid)).thenReturn(userDevice);
        when(encryptionManagerService.decryptData(same(userDevice), same(data))).thenReturn(commandRequest.getBytes(Charsets.UTF_8));
        when(queuedCommandRepository.save(any(QueuedCommand.class))).thenAnswer(persistedEntity(commandId));

        final QueuedCommand queuedCommand = commandQueueManagerService.queueCommand(guid, data);

        assertEquals(commandId, queuedCommand.getId());
        assertEquals(userDevice, queuedCommand.getUserDevice());
        assertArrayEquals(data, queuedCommand.getEncryptedCommand());
        assertNotNull(queuedCommand.getQueueTime());
        assertTrue(queuedCommand.isValid());
        assertFalse(queuedCommand.isDismissed());
        assertFalse(queuedCommand.isExecuted());
        assertNull(queuedCommand.getResult());
        assertNull(queuedCommand.getExecuteTime());
        assertEquals(requestUrl, queuedCommand.getCommandRequest().getUrl());
        assertEquals(requestMethod, queuedCommand.getCommandRequest().getMethod());
        assertEquals(requestContentType, queuedCommand.getCommandRequest().getContentType());
        assertEquals(requestAcceptType, queuedCommand.getCommandRequest().getAcceptType());
        assertEquals(requestBody, queuedCommand.getCommandRequest().getBody());
    }

    @Test
    public void givenDeviceIsInvalidWhenCommandQueuedThenQueuedCommandSavedAndReturned() throws JsonProcessingException {
        final UUID guid = UUID.randomUUID();
        byte[] data = "scratched himself, yawned, and spread out his paws".getBytes(Charsets.UTF_8);
        final Long commandId = RandomUtils.nextLong();
        UserDevice userDevice = mock(UserDevice.class);
        when(userDevice.isValid()).thenReturn(false);
        URI requestUrl = URI.create("http://www.gutenberg.org/files/236/236-h/236-h.htm");
        String requestMethod = HttpMethod.POST.name();
        String requestContentType = MediaType.APPLICATION_JSON_VALUE;
        String requestAcceptType = MediaType.APPLICATION_JSON_VALUE;
        String requestBody = "{\"one\": \"after the other to get rid of the sleepy feeling in their tips.\" }";

        String commandRequest = new ObjectMapper().writeValueAsString(ImmutableMap.of(
                "url", requestUrl.toString(),
                "method", requestMethod,
                "contentType", requestContentType,
                "acceptType", requestAcceptType,
                "body", requestBody
        ));

        when(userDeviceRepository.findOneByGuid(guid)).thenReturn(userDevice);
        when(encryptionManagerService.decryptData(same(userDevice), same(data))).thenReturn(commandRequest.getBytes(Charsets.UTF_8));
        when(queuedCommandRepository.save(any(QueuedCommand.class))).thenAnswer(persistedEntity(commandId));

        final QueuedCommand queuedCommand = commandQueueManagerService.queueCommand(guid, data);

        assertEquals(commandId, queuedCommand.getId());
        assertEquals(userDevice, queuedCommand.getUserDevice());
        assertArrayEquals(data, queuedCommand.getEncryptedCommand());
        assertNotNull(queuedCommand.getQueueTime());
        assertFalse(queuedCommand.isValid());
        assertFalse(queuedCommand.isDismissed());
        assertFalse(queuedCommand.isExecuted());
        assertNull(queuedCommand.getResult());
        assertNull(queuedCommand.getExecuteTime());
        assertEquals(requestUrl, queuedCommand.getCommandRequest().getUrl());
        assertEquals(requestMethod, queuedCommand.getCommandRequest().getMethod());
        assertEquals(requestContentType, queuedCommand.getCommandRequest().getContentType());
        assertEquals(requestAcceptType, queuedCommand.getCommandRequest().getAcceptType());
        assertEquals(requestBody, queuedCommand.getCommandRequest().getBody());
    }

    @Test
    public void givenDecryptionFailedWhenCommandQueuedThenEncryptedQueuedCommandSavedAndReturned() {
        final UUID guid = UUID.randomUUID();
        byte[] data = "Mother Wolf lay with her big gray nose dropped across".getBytes(Charsets.UTF_8);
        final Long commandId = RandomUtils.nextLong();
        UserDevice userDevice = mock(UserDevice.class);
        when(userDevice.isValid()).thenReturn(true);
        URI requestUrl = URI.create("http://www.gutenberg.org/files/236/236-h/236-h.htm");
        String requestMethod = HttpMethod.POST.name();
        String requestContentType = MediaType.APPLICATION_JSON_VALUE;
        String requestAcceptType = MediaType.APPLICATION_JSON_VALUE;
        String requestBody = "{\"her\": \"four tumbling, squealing cubs\" }";

        when(userDeviceRepository.findOneByGuid(guid)).thenReturn(userDevice);
        when(encryptionManagerService.decryptData(same(userDevice), same(data))).thenThrow(IllegalArgumentException.class);
        when(queuedCommandRepository.save(any(QueuedCommand.class))).thenAnswer(persistedEntity(commandId));

        final QueuedCommand queuedCommand = commandQueueManagerService.queueCommand(guid, data);

        assertEquals(commandId, queuedCommand.getId());
        assertEquals(userDevice, queuedCommand.getUserDevice());
        assertArrayEquals(data, queuedCommand.getEncryptedCommand());
        assertNotNull(queuedCommand.getQueueTime());
        assertTrue(queuedCommand.isValid());
        assertFalse(queuedCommand.isDismissed());
        assertFalse(queuedCommand.isExecuted());
        assertNull(queuedCommand.getResult());
        assertNull(queuedCommand.getExecuteTime());
        // But couldn't decrypt to get this stuff.
        assertNotEquals(requestUrl, queuedCommand.getCommandRequest().getUrl());
        assertNotEquals(requestMethod, queuedCommand.getCommandRequest().getMethod());
        assertNotEquals(requestContentType, queuedCommand.getCommandRequest().getContentType());
        assertNotEquals(requestAcceptType, queuedCommand.getCommandRequest().getAcceptType());
        assertNotEquals(requestBody, queuedCommand.getCommandRequest().getBody());
    }

    @Test
    public void givenJsonDecodingFailedWhenCommandQueuedThenEncryptedQueuedCommandSavedAndReturned() {
        final UUID guid = UUID.randomUUID();
        byte[] data = "Mother Wolf lay with her big gray nose dropped across".getBytes(Charsets.UTF_8);
        final Long commandId = RandomUtils.nextLong();
        UserDevice userDevice = mock(UserDevice.class);
        when(userDevice.isValid()).thenReturn(true);
        URI requestUrl = URI.create("http://www.gutenberg.org/files/236/236-h/236-h.htm");
        String requestMethod = HttpMethod.POST.name();
        String requestContentType = MediaType.APPLICATION_JSON_VALUE;
        String requestAcceptType = MediaType.APPLICATION_JSON_VALUE;
        String requestBody = "{\"her\": \"four tumbling, squealing cubs\" }";

        String commandRequest = "-- invalid JSON -- and the moon shone into the mouth of the cave where they all lived.";

        when(userDeviceRepository.findOneByGuid(guid)).thenReturn(userDevice);
        when(encryptionManagerService.decryptData(same(userDevice), same(data))).thenReturn(commandRequest.getBytes(Charsets.UTF_8));
        when(queuedCommandRepository.save(any(QueuedCommand.class))).thenAnswer(persistedEntity(commandId));

        final QueuedCommand queuedCommand = commandQueueManagerService.queueCommand(guid, data);

        assertEquals(commandId, queuedCommand.getId());
        assertEquals(userDevice, queuedCommand.getUserDevice());
        assertArrayEquals(data, queuedCommand.getEncryptedCommand());
        assertNotNull(queuedCommand.getQueueTime());
        assertTrue(queuedCommand.isValid());
        assertFalse(queuedCommand.isDismissed());
        assertFalse(queuedCommand.isExecuted());
        assertNull(queuedCommand.getResult());
        assertNull(queuedCommand.getExecuteTime());
        // But couldn't decode JSON to get this stuff.
        assertNotEquals(requestUrl, queuedCommand.getCommandRequest().getUrl());
        assertNotEquals(requestMethod, queuedCommand.getCommandRequest().getMethod());
        assertNotEquals(requestContentType, queuedCommand.getCommandRequest().getContentType());
        assertNotEquals(requestAcceptType, queuedCommand.getCommandRequest().getAcceptType());
        assertNotEquals(requestBody, queuedCommand.getCommandRequest().getBody());
    }

    @Test(expected = IllegalStateException.class)
    public void givenCommandIsDismissedWhenExecutedThenThrows() {
        Long id = RandomUtils.nextLong();
        final QueuedCommand command = new QueuedCommand(mock(UserDevice.class), true, new byte[0]);
        when(queuedCommandRepository.findById(id)).thenReturn(Optional.of(command));

        commandQueueManagerService.executeCommand(id, executor);
    }

    @Test(expected = IllegalStateException.class)
    public void givenCommandIsAlreadyExecutedWhenExecutedThenThrows() {
        Long id = RandomUtils.nextLong();
        final QueuedCommand command = mock(QueuedCommand.class);
        when(queuedCommandRepository.findById(id)).thenReturn(Optional.of(command));

        commandQueueManagerService.executeCommand(id, executor);
    }

    @Test(expected = RuntimeException.class)
    public void whenExecutedAndExecutorFailsThenThrows() {
        Long id = RandomUtils.nextLong();
        final UserDevice userDevice = mock(UserDevice.class, RETURNS_DEEP_STUBS);
        final QueuedCommand command = new QueuedCommand(userDevice, false, new byte[0]);
        when(userDevice.getUser().getUsername()).thenReturn("Augrh");
        when(queuedCommandRepository.findById(id)).thenReturn(Optional.of(command));

        commandQueueManagerService.executeCommand(id, executor);
    }

    private static <T extends AbstractLongKeyedEntity> Answer<T> persistedEntity(final Long id) {
        return invocation -> {
            final T entity = invocation.getArgument(0);
            entity.setId(id);
            return entity;
        };
    }
}
