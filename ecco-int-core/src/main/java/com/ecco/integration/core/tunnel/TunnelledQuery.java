package com.ecco.integration.core.tunnel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.FromStringDeserializer;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.net.URI;
import java.util.UUID;

/**
 * Serialized (JSON) instances of this are received by the {@link HttpTunnelSocketClient}.
 *
 * @since 24/08/2016
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class TunnelledQuery {
    public UUID correlationId;
    public HttpMethod method;
    public URI url;
    @JsonDeserialize(using = MediaTypeDeserializer.class)
    public MediaType contentType;
    @JsonDeserialize(using = MediaTypeDeserializer.class)
    public MediaType acceptType;
    public String body;

    static class MediaTypeDeserializer extends FromStringDeserializer<MediaType> {
        public MediaTypeDeserializer() {
            super(MediaType.class);
        }

        @Override
        protected MediaType _deserialize(String value, DeserializationContext ctxt) {
            return MediaType.valueOf(value);
        }
    }

    public static class Response {
        public UUID correlationId;
        public int statusCode;
        public String contentType;
        public String body;
    }
}
