package com.ecco.integration.core.tunnel;

import com.ecco.integration.core.EccoSettings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.websocket.DeploymentException;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;
import javax.websocket.WebSocketContainer;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

import static com.ecco.integration.core.tunnel.WebSocketConnectionInstigator.ATTEMPTS_PER_TICK;
import static org.mockito.Mockito.*;

/**
 * @since 07/09/2016
 */
@ExtendWith(MockitoExtension.class)
public class WebSocketConnectionInstigatorTest {

    @Mock
    private WebSocketContainer webSocketContainer;

    @Mock
    private Session initialSession;

    private WebSocketConnectionInstigator webSocketConnectionInstigator;

    @BeforeEach
    public void setUp() throws URISyntaxException {
        EccoSettings.InstanceSettings.TunnelSettings tunnelSettings = new EccoSettings.InstanceSettings.TunnelSettings();
        EccoSettings.InstanceSettings instanceSettings = new EccoSettings.InstanceSettings();
        EccoSettings eccoSettings = new EccoSettings();
        eccoSettings.setInstance(instanceSettings);
        instanceSettings.setTunnel(tunnelSettings);

        final URI baseUrl = URI.create("http://fish.jumper/safe/");
        instanceSettings.setBaseUrl(baseUrl);
        tunnelSettings.setEnabled(true);
        final URI path = URI.create("house.arrest");
        tunnelSettings.setWebSocketPath(path);

        webSocketConnectionInstigator = new WebSocketConnectionInstigator(eccoSettings, webSocketContainer);
    }

    /**
     * Make an initial connection so that initialSession is associated with the connection instigator, and reset the
     * mocks so that any further interaction has no behaviour defined.
     */
    private void initialiseConnection() {
        try {
            when(webSocketContainer.connectToServer(any(Class.class), eq(URI.create("ws://fish.jumper/safe/house.arrest"))))
                    .thenReturn(initialSession);
            when(initialSession.isOpen()).thenReturn(true);
            webSocketConnectionInstigator.connectAtStartup();
            reset(webSocketContainer, initialSession);
        } catch (DeploymentException | IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testCheckSessionWithActiveSession() {
        initialiseConnection();
        when(initialSession.isOpen()).thenReturn(true);
        when(initialSession.getAsyncRemote()).thenReturn(mock(RemoteEndpoint.Async.class));
        webSocketConnectionInstigator.checkSession();
        verifyNoInteractions(webSocketContainer);
    }

    @Test
    public void testCheckInstigatorMessageSize() throws IOException, DeploymentException {
        verify(webSocketContainer).setDefaultMaxSessionIdleTimeout(eq(0L));

        verify(webSocketContainer).setDefaultMaxBinaryMessageBufferSize(eq(WebSocketConnectionInstigator.BUFFER_SIZE));
        verify(webSocketContainer).setDefaultMaxTextMessageBufferSize(eq(WebSocketConnectionInstigator.BUFFER_SIZE));
    }

    @Test
    public void testCheckSessionWithClosedSession() throws IOException, DeploymentException {
        initialiseConnection();
        when(initialSession.isOpen()).thenReturn(false);
        Session newSession = mock(Session.class);
        when(webSocketContainer.connectToServer(any(Class.class), any(URI.class)))
                .thenReturn(newSession);
        when(newSession.isOpen()).thenReturn(true);
        when(newSession.getAsyncRemote()).thenReturn(mock(RemoteEndpoint.Async.class));

        webSocketConnectionInstigator.checkSession();
        verify(webSocketContainer).connectToServer(any(Class.class),
                eq(URI.create("ws://fish.jumper/safe/house.arrest")));
    }

    @Test
    public void testCheckSessionWithNoSession() throws IOException, DeploymentException {
        Session newSession = mock(Session.class);
        when(webSocketContainer.connectToServer(any(Class.class), any(URI.class)))
                .thenReturn(newSession);
        when(newSession.isOpen()).thenReturn(true);
        when(newSession.getAsyncRemote()).thenReturn(mock(RemoteEndpoint.Async.class));

        webSocketConnectionInstigator.checkSession();
        verify(webSocketContainer).connectToServer(any(Class.class),
                eq(URI.create("ws://fish.jumper/safe/house.arrest")));
    }

    @Test
    public void testReconnectRetriesAFewTimes() throws IOException, DeploymentException {
        when(webSocketContainer.connectToServer(any(Class.class), any(URI.class)))
                .thenThrow(DeploymentException.class);
        webSocketConnectionInstigator.reconnect();
        verify(webSocketContainer, times(ATTEMPTS_PER_TICK)).connectToServer(any(Class.class),
                eq(URI.create("ws://fish.jumper/safe/house.arrest")));
    }

    @Test
    public void testFailedReconnectWithCurrentOpenSessionUnsetsSession() throws IOException, DeploymentException {
        initialiseConnection();
        when(initialSession.isOpen()).thenReturn(true);
        webSocketConnectionInstigator.reconnect(); // This failed...

        // ... so calling checkSession will actually try to connect
        Session newSession = mock(Session.class);
        when(webSocketContainer.connectToServer(any(Class.class), any(URI.class)))
                .thenReturn(newSession);
        when(newSession.isOpen()).thenReturn(true);
        when(newSession.getAsyncRemote()).thenReturn(mock(RemoteEndpoint.Async.class));

        webSocketConnectionInstigator.checkSession();
        verify(webSocketContainer, times(ATTEMPTS_PER_TICK + 1)).connectToServer(any(Class.class),
                eq(URI.create("ws://fish.jumper/safe/house.arrest")));
    }
}
