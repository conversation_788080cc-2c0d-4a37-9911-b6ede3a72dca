/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-buildings"))
    implementation(project(":ecco-config"))
    api(project(":ecco-evidence"))
    implementation(project(":ecco-contracts"))
    implementation(project(":ecco-service-config"))
    implementation(project(":ecco-contacts"))
    api(project(":ecco-security"))
    implementation(project(":ecco-upload-dom"))
    implementation(project(":ecco-calendar-core"))
    implementation(project(":ecco-int-api-default"))
    implementation(project(":ecco-servicerecipient"))
    testImplementation(project(":test-support"))

    implementation("joda-time:joda-time:2.10.8")
    implementation("org.springframework:spring-aspects")
    implementation("com.google.guava:guava")
}

description = "ecco-dom"
