package com.ecco.dom;

import org.springframework.security.core.Authentication;

import javax.persistence.DiscriminatorValue;

import static com.ecco.security.SecurityUtil.getUser;

@javax.persistence.Entity
@DiscriminatorValue("exit")
public class ExitComment extends ReferralBaseComment {

    public static class Builder {
        private final ExitComment c;

        public Builder(ExitComment c) {
            this.c = c;
        }

        public Builder withAuthor(Authentication auth) {
            withAuthor(getUser(auth).getContact());
            return this;
        }

        public Builder withAuthor(Individual author) {
            c.setAuthor(author);
            return this;
        }

        public Builder withComment(String comment) {
            c.setComment(comment);
            return this;
        }

        public ExitComment build() {
            return c;
        }
    }

    public static Builder builder(Referral referral) {
        ExitComment c = new ExitComment();
        c.setReferral(referral);
        return new Builder(c);
    }

}
