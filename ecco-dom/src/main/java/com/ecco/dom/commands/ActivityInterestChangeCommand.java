package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("goalActivityChange")  // TODO: Change to activityInterestChange incl Liquibase
public class ActivityInterestChangeCommand extends ServiceRecipientTaskCommand {

    public ActivityInterestChangeCommand(@Nullable UUID uuid,
                                         @NonNull Instant remoteCreationTime,
                                         long userId,
                                         @NonNull String body,
                                         int serviceRecipientId,
                                         String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected ActivityInterestChangeCommand() {
        super();
    }

}
