package com.ecco.dom.commands;

import lombok.NoArgsConstructor;
import org.joda.time.Instant;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Calendar command which holds any new/update to an event in the body of the command.
 */
@Entity
@DiscriminatorValue("message")
@NoArgsConstructor
public class ContactMessageCommand extends ContactCommand {

    public ContactMessageCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                 long userId, @NonNull String body, int contactId) {
        super(uuid, remoteCreationTime, userId, body, contactId);
    }

}
