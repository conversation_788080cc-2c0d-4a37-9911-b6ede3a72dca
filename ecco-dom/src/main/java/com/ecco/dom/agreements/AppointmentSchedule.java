package com.ecco.dom.agreements;

import com.ecco.config.dom.ListDefinitionEntry;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.Set;

/**
 * A demand for an appointment for an srId.
 */
@Entity
@DiscriminatorValue(AppointmentSchedule.DISCRIMINATOR_VALUE)
// FIXME: This doesn't bloomin work
//@SecondaryTable(name="appointmenttypes",
//        pkJoinColumns=@PrimaryKeyJoinColumn(name="id", referencedColumnName = "appointmentTypeId"))
public class AppointmentSchedule extends DemandSchedule {
    // TODO DISCRIMINATOR_VALUE simply distinguishes between 'appointmentSchedules' and 'resourceSchedules' in AgreementOfAppointments
    // TODO it should probably be 'appointment' to match 'resource' in ResourceSchedule (although 'worker' is a consistent resource for referral and building demand until careruns come in)
    static final String DISCRIMINATOR_VALUE = "worker"; // demandedResource required for this demand_type
    public static final String DISCRIMINATOR_CLAUSE = DISCRIMINATOR_COLUMN +"='" + DISCRIMINATOR_VALUE + "'";

    @Autowired
    transient AppointmentTypeRepository appointmentTypeRepository;

    @Getter
    @Setter
    @Column
    public Long appointmentTypeId;

//    @Column(table = "appointmenttypes", name="name")
//    @NotNull
//    String appointmentTypeName; // eg 'one-to-one'

    @Override
    public <T extends DemandSchedule> T instantiateCopy() {
        AppointmentSchedule shallowCopy = new AppointmentSchedule();
        super.copy(shallowCopy);
        shallowCopy.setAppointmentTypeId(getAppointmentTypeId());
        return (T) shallowCopy;
    }

    @Override
    public String getTitle() {
        return appointmentTypeRepository.findById(appointmentTypeId).orElseThrow().getName();
    }

    @Override
    public Integer getCategoryId() {
        return appointmentTypeId != null ? appointmentTypeId.intValue() : null;
    }

    @Override
    public Set<ListDefinitionEntry> getRequiredAttributes() {
        return appointmentTypeRepository.findOne(appointmentTypeId).getRequirements();
    }

    @Override
    public DemandParameters getDemandParameters() {
        String preferences = getAgreement().getServiceRecipient().textMap.get("appointment preferences");
        String tasks = (getParameters() != null && getParameters().get("tasks") != null) ? getParameters().get("tasks").toString() : null;
        return new DemandParameters(preferences, tasks);
    }

}
