package com.ecco.dom.agreements;

import com.ecco.infrastructure.entity.AbstractUUIDKeyedEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "fin_invoicelines")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "discriminator_orm", discriminatorType = DiscriminatorType.STRING)
@Getter
@Setter
public abstract class ClientSalesInvoiceLine extends AbstractUUIDKeyedEntity {

    @ManyToOne
    @JoinColumn(name="invoiceId", nullable=false)
    ClientSalesInvoice invoice;

    @Column(name = "line_order")
    protected Integer lineOrder;

    @Column(name = "type")
    protected String type;

    @Column(name = "description", nullable = false)
    protected String description;

    @Column
    protected Integer rateCardId;

    @Column(name = "amount", nullable = false)
    protected BigDecimal netAmount;

    @Column(name = "taxRate")
    protected BigDecimal taxRate;

}
