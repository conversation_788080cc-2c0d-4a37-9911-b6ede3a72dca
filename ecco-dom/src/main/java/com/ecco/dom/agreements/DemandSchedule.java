package com.ecco.dom.agreements;

import com.ecco.calendar.core.CalendarRecurringService;
import com.ecco.calendar.core.RecurringEntry;
import com.ecco.calendar.core.RecurringEntryDefinition;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.UnitOfMeasurement;
import com.ecco.infrastructure.entity.ConfigurableLongKeyedEntity;
import com.ecco.infrastructure.hibernate.AntiProxyUtils;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.google.common.collect.Range;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.joda.time.*;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.dateTimeToJdk;
import static java.util.Collections.emptyList;

@Slf4j
@Data
@Entity
@Table(name="appointmentschedules")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name= DemandSchedule.DISCRIMINATOR_COLUMN, discriminatorType=DiscriminatorType.STRING)
public abstract class DemandSchedule extends ConfigurableLongKeyedEntity {

    private static final long serialVersionUID = 1L;
    static final String DISCRIMINATOR_COLUMN = "demand_type";

    @ManyToOne(fetch= FetchType.LAZY)
    @JoinColumn(name="agreementId", nullable=false)
    ServiceAgreement agreement;

    /**
     * A link between this child schedule and its parent. Used to keep track of duplicated schedules when more than one
     * worker is required for an schedule.
     */
    @Getter
    @Setter
    @Nullable
    @Column
    Long parentScheduleId;

    /**
     * Simply a reference to let us easily work out what was the schedule before a split.
     */
    @Getter
    @Setter
    @Nullable
    @Column
    Long previousScheduleId;

    /**
     * Agreed duration in units - see agreedDurationUnitMeasurement.
     */
    @Column(name = "duration", nullable = false)
    private int agreedDurationUnits; // NB could be nullable

    /**
     * Agreed duration unit measurement. A UnitsOfMeasurement can be minutes/hour/day/night, which avoids DST issues.
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="agreedDurationUnitId") // NOTE: This defaults to 1 = MINUTE in the database.
    UnitOfMeasurement agreedDurationUnitMeasurement;

    /**
     * Variable RateCard that allows the schedule to pick the correct RateCard by name.
     * NB purposefully non-relational to a specific RateCard to allow the name to pick up the relevant RateCard according
     * to the applicable date and criteria.
     * One of rateCardName or rateCard is null.
     */
    @Nullable
    String rateCardName;

    /**
     * Fixed RateCard that is not to change for the duration of the agreement.
     * One of rateCardName or rateCard is null.
     */
    @Nullable
    @ManyToOne(fetch= FetchType.LAZY)
    @JoinColumn(name = "rateCardId")
    RateCard rateCard;

    public List<RateCard> allowableRateCards() {
        if (rateCard != null) {
            return Collections.singletonList(rateCard);
        }

        if (getAgreement().getContract() != null) {
            List<RateCard> contractRateCards = getAgreement().getContract().getRateCards();
            return StringUtils.isNotBlank(rateCardName)
                ? contractRateCards.stream().filter(rc -> rc.getName().equalsIgnoreCase(rateCardName)).collect(Collectors.toList())
                : contractRateCards;
        }
        return emptyList();
    }

    /**
     * Some notion of setting up this across any number of times/days/weeks/months/years
     * which is used by some generator to create appointments for the next x weeks
     * therefore this can be developed to be more complex, but until then its simple enough to create many schedules
     * is this trying to do what an ical repeating calendar can already do (repeating events with exceptions etc)?
     */
    @Embedded
    private DaysOfWeek days = new DaysOfWeek();

    /**
     * The repeating interval type.
     * One of "WK" | "MTH" | "QTR" | "BI" | "YR".
     */
    @NotNull
    private String intervalType;

    /**
     * The repeating interval for the 'days' above. By default 'days' is weekly,
     * but the interval allows this to change:
     *    1 = every week / qtr etc
     *    2 = every other week
     *    ...
     * See CosmoConverter.java
     */
    @NotNull
    private Integer intervalFrequency;

    @Column(name="timeForDays")
    private LocalTime time;

    /**
     * Indicate when this activity should start
     */
    @Column(name="startDate", nullable = false)
    @NotNull
    private LocalDate start;

    /**
     * Indicates when this activity should end - it can still happen on this date, but is not scheduled afterwards.
     */
    @Column(name="endDate")
//    @Nullable // FIXME: This will cause some Kotlin to fail to compile and show some potential real issues or code paths that are no longer used
    private LocalDate end;

    // TODO change String to RecurringEntry.RecurringEntryHandle
    @Setter(AccessLevel.PUBLIC)
    @Getter(AccessLevel.PACKAGE)
    @Column(name = "calendar_entry_handle", length = 50, nullable = false)
    private String entryHandleAsString;

    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, Object> parameters = new HashMap<>();

    @Autowired
    @Transient
    protected transient CalendarRecurringService calendarRecurringService;

    @PersistenceContext
    @Transient
    @Getter(AccessLevel.NONE)
    protected transient EntityManager em;

    @Autowired
    @Transient
    private transient EntityUriMapper entityUriMapper;

    @Transient
    private transient boolean newEntityValue = false;

    @Transient
    private transient LocalTime persistedTime;
    @Transient
    private transient int persistedDurationInMinutes;
    @Transient
    private transient boolean preUpdateCalled = false;
    @Transient
    private transient DaysOfWeek persistedDaysOfWeek;
    @Transient
    private transient String persistedIntervalType;

    /**
     * Get the appointmentType name or the resourceType name
     */
    @Transient
    public abstract String getTitle();

    /**
     * Get the appointmentType id or the resourceType id
     */
    public abstract Integer getCategoryId();

    /**
     * Get a new instance of the
     */
    public abstract <T extends DemandSchedule> T instantiateCopy();

    public Set<ListDefinitionEntry> getRequiredAttributes() {
        return Collections.emptySet();
    }

    /** get required skills, double-ups etc */
    @Transient
    public abstract DemandParameters getDemandParameters();

    @PostLoad
    void retainPersistedValues() {
        this.persistedDurationInMinutes = getAgreedDurationInMinutes();
        this.persistedTime = time;
        this.persistedIntervalType = intervalType;
        this.persistedDaysOfWeek = days.clone();
    }
    @PostPersist
    void postPersistActions() {
        retainPersistedValues();
        entryHandleManagedBy();
    }

    @PrePersist
    void entryHandle() { // was createOrUpdateRecurringCalendarEvent

        // assert end is not before start
        if (end != null && end.isBefore(start)) {
            throw new IllegalArgumentException("End date cannot be before start date");
        }

        agreement = AntiProxyUtils.ensureManaged(em, agreement);
        //entityManager.flush();
        // the calendarId is the collectionItem of the user
        final String calendarId = agreement.getServiceRecipient().calendarId;
        // DEV-14 NB the recurringEntry.getManagedByUri() is blank, but we update this later
        // NB The underlying contentDao.createContent uses flush()
        final RecurringEntry recurringEntry = calendarRecurringService.createRecurringEntry(calendarId, new DemandScheduleRecurringEntryDefinitionAdapter(this, entityUriMapper));
        setEntryHandleAsString(recurringEntry.getHandle().toString());
    }
    void entryHandleManagedBy() {
        calendarRecurringService.updateRecurringEntryManagedBy(RecurringEntryHandle.fromString(this.entryHandleAsString), entityUriMapper.uriForEntity(this));
    }
    // also see EventServiceImpl decorateCalendarEntry which also checks for /null from the underlying calendar
    public URI getManagedByReconstructed() {
        return entityUriMapper.uriForEntity(this.getClass().getSimpleName(), getId());
    }

    @PreUpdate
    public void verify() {
        // preUpdateCalled prevents an infinite loop between calendarService.updateRecurringEntryBounds and back here
        if (!preUpdateCalled) {
            preUpdateCalled = true;
            // verify that recurring elements can't change:
            //      We can change the recurring elements of the schedule, but this would mean changing previous events
            //      and so increasing the days (for example) could show up as missed appointments, and any exceptions (dropped events)
            //      may not match up - although, importantly, concrete recurrences would NOT be updated when changing recurring
            //      elements so any evidence recorded against concrete/confirmed visits will still be correct.
            //      So, if we want to rely on a schedule's consistency (eg avoiding 'new' missed appointment / dropped)
            //      then we should amend the recurring elements 'from now', which involves ending/cloning the schedule 'from now'
            //      - taking into account the already confirmed future-dated appointments.
            //      This also means the start/end dates of a schedule become more useful, as opposed to just being the agreement date,
            //      and we should probably ignore the ability for the start/end range to be extended - as per updateRecurringEntryBounds
            //      (which currently has the ability to enable extra missed appointments in the past).
            //
            //      Furthermore, if the start/end dates are freely editable (and not cloned) then what does it mean for the schedule
            //      to change days - users will expect to update the start date (as the applicable from date) which will cause problems
            //      since there will be lots of conflicts of concrete events to deal with. We'd end up with a read only start date
            //      for the schedule start date which then looks at odds with the data.
            //
            //      NB The comments on the tests (f9fb1b07) show exceptions can be created with different durations
            //      and we have audits so editing schedules could push its changes to updating the recurring entry
            //      so this Assert-ing is not for any reason except to avoid updating previous/future events
            //          - see 89b5f87 which indicates that updating a schedule won't automatically update the calendar events (note the tests)
            //          - and 0429a624 which reinforces this
            //      although we do allow the updateRecurringEntryBounds to amend the range of the schedule (if it doesn't affect any concrete recurrences)
            Assert.state(persistedDurationInMinutes == getAgreedDurationInMinutes(), "The duration of a recurring scheduled event cannot be changed after initial creation (recurring events would be out of sync).");
            Assert.state(Objects.equals(persistedTime, time), "The time of a recurring scheduled event cannot be changed after initial creation (recurring events would be out of sync).");
            Assert.state(persistedIntervalType.equals(intervalType), "The frequency intervalType on which a scheduled event recurs cannot be changed after initial creation (recurring events would be out of sync).");
            Assert.state(persistedDaysOfWeek.equals(days), "The days on which a scheduled event recurs cannot be changed after initial creation (recurring events would be out of sync).");
        }
    }

    public void updateBoundsInCalendarEnd() {
        calendarRecurringService.updateRecurringEntryBoundsEnd(getRecurringEntryHandle(), getEnd());
    }

    /**
     * Wraps the logic for determining if a demand schedule can be deleted or not in a service operation.
     * A demand schedule cannot be delete if any individual recurrences have been allocated (to workers or resources).
     *
     * @throws IllegalStateException if the demand schedule cannot be deleted due to having allocated recurrences.
     */
    public void delete() {
        calendarRecurringService.deleteRecurringEntry(getRecurringEntryHandle(), true);

        // Delete the rota
        em.remove(AntiProxyUtils.ensureManaged(em, this));
        em.flush();
    }

    public void setEnd(LocalDate newEnd) {
        this.end = newEnd;

        if (this.getId() != null) {
            // OR do we do this conditionally in verify() to work around previous issue?
            this.updateBoundsInCalendarEnd();
        }
    }

    public void truncate(@NonNull LocalDate endDate) {
        // NB allocated appointments in the schedule are removed in the handler

        this.end = endDate;

        // NB flush is needed to trigger the @PreUpdate
        // but it also causes errors with "org.hibernate.HibernateException: Found shared references to a collection: com.ecco.dom.agreements.AppointmentType.requirements"
        // however, removing the 'requirements' entirely gets the same exception on another collection - so some session issue.
        //em.flush();

        // manually trigger the verify, which is all we need in this scenario since the aop calendar code isn't required
        // we did try the same technique as delete with AntiProxyUtils.ensureManaged.
        this.verify();

        this.updateBoundsInCalendarEnd();
    }

    public int getAgreedDurationInMinutes() {
        return agreedDurationUnitMeasurement == null
                ? 0  // Because in ServiceRecipientResourceScheduleCommandHandler.findOrCreateSchedule we don't set a a time
                : agreedDurationUnitMeasurement.getTemporalUnitsAsMinutes(agreedDurationUnits);
    }

    public RecurringEntryHandle getRecurringEntryHandle() {
        return RecurringEntryHandle.fromString(entryHandleAsString);
    }

    public Range<Instant> getRange() {
        return Range.closedOpen(dateTimeToJdk(getStart().toDateTime(getTime())).toInstant(),
                RecurringEntry.getEndPlus1AsInstant(this.getEnd()));
    }

    /**
     * Indication that this is an ad-hoc schedule.
     * Ideally as a boolean, but we also have historic data.
     */
    public boolean isAdHoc() {
        return (start != null) && start.equals(end);
    }

    protected void copy(@NonNull DemandSchedule shallowClone) {
        shallowClone.setAgreement(getAgreement());
        //shallowClone.setParentScheduleId(getParentScheduleId());
        //shallowClone.setEntryHandleAsString(null);
        shallowClone.setStart(getStart());
        shallowClone.setEnd(getEnd());
        shallowClone.setTime(getTime());
        shallowClone.setAgreedDurationUnitMeasurement(getAgreedDurationUnitMeasurement());
        shallowClone.setAgreedDurationUnits(getAgreedDurationUnits());
        shallowClone.setDays(getDays().clone());
        if (getParameters() != null) {
            shallowClone.setParameters((HashMap<String, Object>) getParameters().clone());
        }
        shallowClone.setIntervalType(getIntervalType());
        shallowClone.setIntervalFrequency(getIntervalFrequency());
        shallowClone.setRateCard(getRateCard());
        shallowClone.setRateCardName(getRateCardName());
    }

    /**
     * Used in @PrePersist to create a recurring entry for this DemandSchedule
     */
    static class DemandScheduleRecurringEntryDefinitionAdapter implements RecurringEntryDefinition {
        private final DemandSchedule demandSchedule;
        private final EntityUriMapper entityUriMapper;

        DemandScheduleRecurringEntryDefinitionAdapter(DemandSchedule demandSchedule, EntityUriMapper entityUriMapper) {
            this.demandSchedule = demandSchedule;
            this.entityUriMapper = entityUriMapper;
        }

        @Override
        public String getTitle() {
            return demandSchedule.getTitle();
        }

        @Override
        public String getDescription() {
            return null;
        }

        @Override
        public DateTime getStart() {
            return demandSchedule.getStart().toDateTime(demandSchedule.getTime());
        }

        @Override
        public Duration getDuration() {
            return Duration.standardMinutes(demandSchedule.getAgreedDurationInMinutes());
        }

        @Override
        public LocalDate getScheduleEndDate() {
            return demandSchedule.getEnd();
        }

        @Override
        public String getIntervalType() {
            return demandSchedule.getIntervalType();
        }

        @Override
        public Set<Integer> getCalendarDays() {
            return demandSchedule.getDays().toCalendarDaySet();
        }

        @Override
        public Integer getIntervalFrequency() {
            return demandSchedule.getIntervalFrequency();
        }

        @Override
        public URI getManagedByUri() {
            // DEV-14: this doesn't work because the ID is null at the time this is created.
            return entityUriMapper.uriForEntity(demandSchedule);
        }

        @Override
        public URI getUpdatedByUri() {
            // we don't have an updatedBy when creating the demand
            return null;
        }
    }

}
