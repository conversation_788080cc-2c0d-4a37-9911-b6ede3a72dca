package com.ecco.dom;

import java.util.Locale;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Test;

public class JodaShortParseTest {

    static String MESSAGE = "Fails if -Djava.locale.providers=JRE,SPI is missing\n" +
            "Use build-and-test.sh/ps1";

    DateTimeFormatter fmt = DateTimeFormat.shortDateTime();

    @Test
    public void testJodaShortPrintTest() {
        // construct a known date
        DateTime instance = new DateTime(2009, 11, 20,
                13, 16, 43, 55, DateTimeZone.UTC);

        // the formatter uses the zone and locale of the instant, therefore the underlying jvm
        // which demonstrates that our code will transfer to other locales
        //System.out.println("default TZ: " + java.util.TimeZone.getDefault());
        //System.out.println("default locale: " + java.util.Locale.getDefault());
        String jodaPrint = fmt.withZone(DateTimeZone.UTC).withLocale(Locale.UK).print(instance);
        Assert.assertEquals(MESSAGE, "20/11/09 13:16", jodaPrint);
    }

    @Test
    public void testJodaShortParseTest() {
        // construct a known date
        DateTime instance = new DateTime(2009, 11, 20,
                13, 16, 43, 55, DateTimeZone.UTC);

        // the formatter uses the zone and locale of the instant, therefore the underlying jvm
        // which demonstrates that our code will transfer to other locales
        //System.out.println("default TZ: " + java.util.TimeZone.getDefault());
        //System.out.println("default locale: " + java.util.Locale.getDefault());
        String jodaPrint = fmt.withZone(DateTimeZone.UTC).withLocale(Locale.UK).print(instance);
        Assert.assertEquals("20/11/09 13:16", jodaPrint);

        // milis are different
        DateTime dt = fmt.withZone(DateTimeZone.UTC).withLocale(Locale.UK).parseDateTime(jodaPrint);
        Assert.assertNotEquals(MESSAGE, 0, dt.compareTo(instance));

        // milis are the same
        DateTime instanceNoMs = new DateTime(2009, 11, 20,
                13, 16, 0, 0, DateTimeZone.UTC);
        Assert.assertEquals(MESSAGE, 0, dt.compareTo(instanceNoMs));

        // append a blank and see if joda deals with it (blank is seemingly added in mvc jquery)
        // indeed there is an error!
        try {
            fmt.withZone(DateTimeZone.UTC).withLocale(Locale.UK).parseDateTime(jodaPrint + " ");
            Assert.fail("parsing the date passed with a space - it hasn't in the past!");
        } catch (IllegalArgumentException ignored) {
        }
    }
}
