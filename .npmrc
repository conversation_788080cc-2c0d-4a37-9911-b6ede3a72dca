# config as per https://docs.npmjs.com/cli/v6/using-npm/config#per-package-config-settings
# specific repository registry's (commented below) not required to split @eccosolutions between github/npm
# as github mirrors npm public registry
# the format here is @OWNER:registry, so it doesn't support individual packages like '@eccosolutions/blah'
    # npm config set @eccosolutions/rjsf-core:registry https://registry.npmjs.org/
    # npm config set @eccosolutions/rjsf-material-ui:registry https://registry.npmjs.org/
# npm config set @eccosolutions:registry https://npm.pkg.github.com
@eccosolutions:registry=https://npm.pkg.github.com
link-workspace-packages=true
