package com.ecco.web.jsp;

import com.ecco.serviceConfig.dom.NameValue;
import com.google.common.collect.ImmutableSet;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeDiagnosingMatcher;
import org.junit.Assert;
import org.junit.Test;

import java.net.URI;
import java.util.Set;

import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.arrayContaining;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;

public class EccoFnTest {
    @Test
    public void whenConvertingNullThenNullReturned() {
        Assert.assertNull(EccoFn.settingsFromUri(null));
    }

    @Test
    public void whenConvertingUriWithoutQueryStringThenEmptySettingsReturned() {
        assertThat(EccoFn.settingsFromUri(URI.create("urn:opaque?howdy=cowboy-talk")), hasSize(0));
    }

    @Test
    public void whenConvertingNormalParametersThenCorrectSettingValuesReturned() {
        final Set<NameValue> nameValues = EccoFn.settingsFromUri(URI.create("flow:/foreignHello?howdy=cowboy-talk&gday=aussie"));
        final Matcher<Iterable<? super NameValue>> howdy = hasItem(nameValue("howdy", "cowboy-talk"));
        final Matcher<Iterable<? super NameValue>> gday = hasItem(nameValue("gday", "aussie"));
        assertThat(nameValues, allOf(howdy, gday));
    }

    @Test
    public void whenConvertingUrlEncodedParametersThenCorrectSettingValuesReturned() {
        final Set<NameValue> nameValues = EccoFn.settingsFromUri(URI.create("flow:/punctuation?amp=%26amp%3B&query=%3F"));
        final Matcher<Iterable<? super NameValue>> amp = hasItem(nameValue("amp", "&amp;"));
        final Matcher<Iterable<? super NameValue>> query = hasItem(nameValue("query", "?"));
        assertThat(nameValues, allOf(amp, query));
    }

    @Test
    public void whenConvertingPlusAsSpaceThenCorrectSettingValuesReturned() {
        final Set<NameValue> nameValues = EccoFn.settingsFromUri(URI.create("flow:/spaces?plus=%2B&space=the+final%20frontier"));
        final Matcher<Iterable<? super NameValue>> plus = hasItem(nameValue("plus", "+"));
        final Matcher<Iterable<? super NameValue>> space = hasItem(nameValue("space", "the final frontier"));
        assertThat(nameValues, allOf(plus, space));
    }

    @Test
    public void givenSettingAbsentWhenGettingSettingAsArrayThenReturnsNull() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("Mansuete", "Triumpher", false));
        final String[] result = EccoFn.settingAsArray("missingSetting", settings);
        assertNull(result);
    }

    @Test
    public void givenSettingBlankWhenGettingSettingAsArrayThenReturnsEmptyArray() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("Communalistic", "", false));
        final String[] result = EccoFn.settingAsArray("Communalistic", settings);
        assertEquals(0, result.length);
    }

    @Test
    public void givenSettingSingleValuedWhenGettingSettingAsArrayThenReturnsArrayOf1() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("Mansuete", "Triumpher", false));
        final String[] result = EccoFn.settingAsArray("Mansuete", settings);
        assertThat(result, arrayContaining("Triumpher"));
    }

    @Test
    public void givenSettingManyValuedWhenGettingSettingAsArrayThenReturnsArray() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("contender", "verbarflambeacute,appalled,methylic", false));
        final String[] result = EccoFn.settingAsArray("contender", settings);
        assertThat(result, arrayContaining("verbarflambeacute", "appalled", "methylic"));
    }

    @Test
    public void givenSettingAbsentWhenGettingSettingAsSetThenReturnsNull() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("Mansuete", "Triumpher", false));
        final Set<String> result = EccoFn.settingAsSet("missingSetting", settings);
        assertNull(result);
    }

    @Test
    public void givenSettingBlankWhenGettingSettingAsSetThenReturnsEmptySet() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("Communalistic", "", false));
        final Set<String> result = EccoFn.settingAsSet("Communalistic", settings);
        assertEquals(0, result.size());
    }

    @Test
    public void givenSettingSingleValuedWhenGettingSettingAsSetThenReturnsSetOf1() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("Mansuete", "Triumpher", false));
        final Set<String> result = EccoFn.settingAsSet("Mansuete", settings);
        assertThat(result, contains("Triumpher"));
    }

    @Test
    public void givenSettingManyValuedWhenGettingSettingAsSetThenReturnsSet() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("contender", "verbarflambeacute,appalled,methylic", false));
        final Set<String> result = EccoFn.settingAsSet("contender", settings);
        assertThat(result, contains("verbarflambeacute", "appalled", "methylic"));
    }

    @Test
    public void givenSettingAbsentWhenCheckingSettingContainsThenReturnsFalse() {
        final Set<NameValue> settings = ImmutableSet.of();
        final boolean result = EccoFn.settingHas("rivalry", settings, "errancy");
        assertFalse(result);
    }

    @Test
    public void givenSettingBlankWhenCheckingSettingsContainsThenReturnsFalse() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("droyle", "", false));
        final boolean result = EccoFn.settingHas("droyle", settings, "renerve");
        assertFalse(result);
    }

    @Test
    public void givenSettingContainsValueWhenCheckingSettingsContainsThenReturnsTrue() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("cordiality", "noumenal, puncture", false));
        final boolean result = EccoFn.settingHas("cordiality", settings, "noumenal") && EccoFn.settingHas("cordiality", settings, "puncture");
        assertTrue(result);
    }

    @Test
    public void givenSettingMissingValueWhenCheckingSettingsContainsThenReturnsFalse() {
        final Set<NameValue> settings = ImmutableSet.<NameValue>of(new EccoFn.SimpleNameValue("electro", "nonhereditary,jalap,slive", false));
        final boolean result = EccoFn.settingHas("electro", settings, "welcomely");
        assertFalse(result);
    }

    private static Matcher<NameValue> nameValue(final String name, final String value) {
        return new TypeSafeDiagnosingMatcher<>() {
            @Override
            protected boolean matchesSafely(NameValue item, Description mismatchDescription) {
                final boolean matches = item.getName().equals(name) && item.getValue().equals(value);
                if (!matches) {
                    mismatchDescription.appendText("was (").appendValue(item.getName()).appendText(",").appendValue(item.getValue()).appendText(")");
                }
                return matches;
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("(").appendValue(name).appendText(",").appendValue(value).appendText(")");
            }
        };
    }
}
