package com.ecco.web.nav;

import com.ecco.infrastructure.web.WebSlice;
import com.ecco.security.ReferenceDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Controller
@WebSlice("nav")
@RequestMapping("/secure/admin/uploadLogo.html")
@Secured({"ROLE_SYSADMIN", "ROLE_ADMIN"})
public class LogoSettingController {

    private CommonsMultipartResolver multipartResolver;

    @Autowired
    public LogoSettingController(CommonsMultipartResolver multipartResolver) {
        this.multipartResolver = multipartResolver;
    }

    @Resource(name="applicationReferenceData")
    private ReferenceDataSource referenceData;

    @ModelAttribute
    public void referenceData(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        final double max = multipartResolver.getFileUpload().getSizeMax();
        model.put("uploadSizeMax", max);
    }

    // useful simply to pass error messages back
    // this is not required with the html5 approach
    @ModelAttribute("upload")
    public Object formBackingObject() {
        return new Object();
    }

    @RequestMapping(method=RequestMethod.GET)
    public ModelAndView showLogoUploadPage(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return new ModelAndView("online/admin/uploadLogo");
    }

}
