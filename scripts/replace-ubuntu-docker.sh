sudo snap remove docker

curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update

# To check we're configured to go to docker.com
#apt-cache policy docker-ce

sudo apt-get install -y docker-ce docker-ce-cli containerd.io
sudo systemctl status docker

# To run docker without sudo
sudo usermod -aG docker $USER

sudo systemctl enable docker
sudo systemctl start docker

echo "If docker fails to start you may need to delete the /var/lib/docker directory and restart the docker service."