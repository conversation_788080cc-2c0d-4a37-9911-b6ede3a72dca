package com.ecco.config.security;

import org.springframework.context.annotation.Configuration;

/**
 * We moved the code to determine if an instance is under acl checking to a common place - AclConfig. However, in doing
 * so we now have tests using AclConfig who know nothing of the acl checking which uses the SettingsService lookup in the database.
 * To avoid moving the common code to another config (because it seems appropriate on the AclConfig) I decided to simply stub it here
 * and add it to all the tests
 *
 * This could have been replaced by StubSettingsService in ecco-config, however settingsService is no longer applicable - see 5d132b5d.
 * Therefore, we need to specify elsewhere, either:
 *  1) AclConfig @Value make true by default
 *  2) Supply -Decco.authn.acl=true
 *   a) in the pom.xml as an arg line, which always gets picked up
 *      via cmd line 'mvn verify -pl med-security' or IDE module install and refresh
 *  3) Or override the argument in SecurityDomainAppContextInitializer
 *
 * TODO Probably should move ecco.authn.acl to ConfigurableEccoEnvironment
 */
@Configuration
public class EnableAclsConfig {

}
