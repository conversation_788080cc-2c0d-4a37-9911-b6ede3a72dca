package com.ecco.security.config;

import com.ecco.security.service.MfaAuthentication;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * An authentication handler that moves to another authentication point.
 * NB see spring samples for ideas - it kept the failures instead of failing at each point
 */
public class MfaAuthenticationHandler implements AuthenticationSuccessHandler {

	private final AuthenticationSuccessHandler mfaNextHandler;

	public MfaAuthenticationHandler(String url) {
		SimpleUrlAuthenticationSuccessHandler successHandler = new SimpleUrlAuthenticationSuccessHandler(url);
		successHandler.setAlwaysUseDefaultTargetUrl(true);
        this.mfaNextHandler = successHandler;
	}

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
			Authentication authentication) throws IOException, ServletException {
        saveMfaAuthentication(request, response, authentication);
	}

	private void saveMfaAuthentication(HttpServletRequest request, HttpServletResponse response,
			Authentication authentication) throws IOException, ServletException {
		SecurityContextHolder.getContext().setAuthentication(new MfaAuthentication(authentication));
		this.mfaNextHandler.onAuthenticationSuccess(request, response, authentication);
	}

}
