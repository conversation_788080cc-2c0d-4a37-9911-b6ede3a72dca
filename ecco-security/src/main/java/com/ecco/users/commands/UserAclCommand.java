package com.ecco.users.commands;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Changes to typical 'core' account details.
 */
@Entity
@DiscriminatorValue("acl")
public class UserAclCommand extends UserCommand {

    public UserAclCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime, long userId, @NonNull String body,
                          long userIdSubject) {
        super(uuid, remoteCreationTime, userId, body, userIdSubject);
    }

    /**
     * Required by JPA/Hibernate.
     */
    @Deprecated
    protected UserAclCommand() {
    }
}
