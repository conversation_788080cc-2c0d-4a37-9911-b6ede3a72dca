package com.ecco.security.acl.dom;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;


@Entity
@Table(name = "acl_entry", uniqueConstraints = @UniqueConstraint(name = "acl_entry_unique_oid_order", columnNames = {"acl_object_identity", "ace_order"}))
public class AclEntry extends AbstractAclEntity {

    @ManyToOne(optional = false)
    @JoinColumn(name = "acl_object_identity", nullable = false)
    private AclObjectIdentity aclObjectIdentity;

    @Column(name="ace_order", nullable = false)
    private int entryOrder;

    @ManyToOne(optional = false)
    @JoinColumn(name = "sid", nullable = false)
    private AclSecurityIdentity sid;

    @Column(name = "mask", nullable = false)
    private int mask;

    @Column(name = "granting", nullable = false)
    private boolean granting;

    @Column(name = "audit_success", nullable = false)
    private boolean auditSuccess;

    @Column(name = "audit_failure", nullable = false)
    private boolean auditFailure;

    public AclObjectIdentity getAclObjectIdentity() {
        return aclObjectIdentity;
    }

    public int getEntryOrder() {
        return entryOrder;
    }

    public AclSecurityIdentity getSid() {
        return sid;
    }

    public int getMask() {
        return mask;
    }

    public boolean isGranting() {
        return granting;
    }

    public boolean isAuditSuccess() {
        return auditSuccess;
    }

    public boolean isAuditFailure() {
        return auditFailure;
    }

    public void setAclObjectIdentity(AclObjectIdentity aclObjectIdentity) {
        this.aclObjectIdentity = aclObjectIdentity;
    }

    public void setEntryOrder(int entryOrder) {
        this.entryOrder = entryOrder;
    }

    public void setSid(AclSecurityIdentity sid) {
        this.sid = sid;
    }

    public void setMask(int mask) {
        this.mask = mask;
    }

    public void setGranting(boolean granting) {
        this.granting = granting;
    }

    public void setAuditSuccess(boolean auditSuccess) {
        this.auditSuccess = auditSuccess;
    }

    public void setAuditFailure(boolean auditFailure) {
        this.auditFailure = auditFailure;
    }
}
