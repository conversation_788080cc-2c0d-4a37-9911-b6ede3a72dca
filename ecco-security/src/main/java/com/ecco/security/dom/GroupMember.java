package com.ecco.security.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "group_members")
@Audited
public class GroupMember extends AbstractLongKeyedEntity {

    @ManyToOne
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Group group;

    @ManyToOne
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private User member;

    protected GroupMember() {}

    public GroupMember(Group group, User member) {
        this.group = group;
        this.member = member;
    }

    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    public User getMember() {
        return member;
    }

    public void setMember(User member) {
        this.member = member;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || !(o instanceof GroupMember)) return false;

        GroupMember that = (GroupMember) o;

        if (getGroup() != null ? !getGroup().equals(that.getGroup()) : that.getGroup() != null) return false;
        if (getMember() != null ? !getMember().equals(that.getMember()) : that.getMember() != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = getGroup() != null ? getGroup().hashCode() : 0;
        result = 31 * result + (getMember() != null ? getMember().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "GroupMember{" + "member=" + member + ", group=" + group + '}';
    }
}
