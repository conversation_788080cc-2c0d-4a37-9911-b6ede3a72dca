package com.ecco.security.event;

import com.ecco.dom.Individual;

public abstract class IndividualContactEvent extends CalendarableEvent<Individual> {

    private static final long serialVersionUID = 1L;

    public IndividualContactEvent(Object source, Individual individual) {
        super(source, individual);
    }

    public String getCalendarId() {
        return contact.getCalendarId();
    }

    public String getFirstname() {
        return contact.getFirstName();
    }

    public String getLastname() {
        return contact.getLastName();
    }
}
