package com.ecco.dom;

import com.ecco.calendar.dom.MedDate;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.contacts.Address;
import com.ecco.dom.contacts.Contact;
import com.ecco.infrastructure.hibernate.HibTypeNames;
import com.ecco.infrastructure.util.Sortable;
import com.ecco.security.dom.User;
import com.ecco.security.event.ContactNameUpdated;
import com.ecco.calendar.core.CalendarOwnerDefinition;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.annotations.Type;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import java.util.Set;
import java.util.UUID;

import static org.springframework.util.StringUtils.hasText;

@Entity
@DiscriminatorValue("individual")
public class Individual extends ContactImpl implements Contact {

    static public class Builder {

        private final Individual individual;

        public Builder(Individual individual) {
            this.individual = individual;
        }

        public Builder withNewUser(String username) {
            return withNewUser(username, null);
        }

        public Builder withNewUser(String username, String password) {
            User user = new User(individual, username);
            user.setNewPassword(password);
            user.setEnabled(true);
            individual.setUser(user);
            user.setContact(individual);
            return this;
        }

        public Builder withNewGroups(Set<String> groups) {
            individual.getUser().setNewGroups(groups);
            return this;
        }

        public Builder withMfaRequired(boolean required) {
            individual.getUser().setMfaRequired(required);
            return this;
        }

        public Builder withMfaSecret(String secret) {
            individual.getUser().setMfaSecret(secret);
            return this;
        }

        public Builder withAddressLine1(String line1) {
            getAddress().setLine1(line1);
            return this;
        }

        public Builder withEmail(String email) {
            individual.setEmail(email);
            return this;
        }
        public Builder withAddressPostcode(String postcode) {
            getAddress().setPostCode(postcode);
            return this;
        }

        public Builder withAddressedLocation(String line1, String postcode) {
            AddressedLocation addressedLocation = individual.getAddressedLocation();
            if (addressedLocation == null) {
                addressedLocation = new AddressedLocation();
                individual.setAddressedLocation(addressedLocation);
            }
            addressedLocation.setLine1(line1);
            addressedLocation.setPostCode(postcode);
            return this;
        }

        private Address getAddress() {
            Address result = individual.getAddress();
            if (result == null) {
                result = new Address();
                individual.setAddress(result);
            }
            return result;
        }

        public Individual build() {
            return individual;
        }

    }

    @Getter
    @Setter
    private String title;
    @Getter
    @Setter
    @Type(type=HibTypeNames.ENCRYPTED_STRING)
    @Column(name="firstname")
    private String firstName;
    private transient String previous_FirstName;
    @Getter
    @Type(type=HibTypeNames.ENCRYPTED_STRING)
    @Column(name="lastname")
    private String lastName;
    private transient String previous_LastName;

    @Getter @Setter
    @Type(type=HibTypeNames.ENCRYPTED_STRING)
    @Column
    private String knownAs;

    @Getter @Setter
    @ManyToOne
    @Nullable
    @JoinColumn(name="pronounsId")
    private ListDefinitionEntry pronouns;

    @Setter
    @Getter
    private String jobTitle; // for when this is a contact within a company
    @Getter
    @Setter(lombok.AccessLevel.PROTECTED)
    @Column(name="userkey")
    private Long key; // derived for database only

    @Setter
    @OneToOne(fetch=FetchType.LAZY)
    // Adding 'referencedColumnName' here means that Hibernate persists User before Individual in a transaction, which is what we want.
    @JoinColumn(name="usersId", referencedColumnName = "id")
    private User user;
    @Getter
    @Setter
    @ManyToOne
    @JoinColumn(name="companyId")
    private Company company;

    @Getter
    @Setter
    @Type(type="enumPreferredContactMethod")
    private PreferredContactMethod preferredContactMethod;
    // added for convenience for referral contacts
    @Getter
    @Setter
    @Embedded
    @AttributeOverrides( {
        @AttributeOverride(name="day", column = @Column(name="birthday")),
        @AttributeOverride(name="month", column = @Column(name="birthmonth")),
        @AttributeOverride(name="year", column = @Column(name="birthyear"))
    })
    private MedDate birthDate;

    @Transient
    private String displayName;

    public String getPreferredContactInfo() {
        if (PreferredContactMethod.Email.equals(preferredContactMethod)) {
            return getEmail();
        }
        if (PreferredContactMethod.Landline.equals(preferredContactMethod)) {
            return getPhoneNumber();
        }
        if (PreferredContactMethod.Mobile.equals(preferredContactMethod)) {
            return getMobileNumber();
        }
        if (PreferredContactMethod.Sms.equals(preferredContactMethod)) {
            return getMobileNumber();
        }
        return null;
    }

    @Override
    // we need to name this differently since it could be classed as a getter
    // that is - hibernate gets confused on a unit test when flushing and checking for dirty
    public boolean getIsUser() {
        return user != null;
    }

    @Override
    @NonNull
    public String getDisplayName() {
        if (StringUtils.isEmpty(displayName)) {
            displayName = joinNonBlanksWithSpace(getFirstName(), getLastName());
        }
        return displayName;
    }


    // lazy here should be lazy because we don't want the users being loaded
    // loading the user generates lines of sql - groups, group members etc
    // and the application should operate at the Individual and no further unless actually dealing with users
    // so we try to maintain a lazy here, even though we can only use this as a hint to hibernate

    // hibernate assumes a OneToOne is a shared primary key
    // so hibernate can't obey the optional=true and lazy since it can't know its optional without loading its existence
    // however, the use of JoinColumn below indicates that we can infer LAZY from the FK
    // but it doesn't necessarily apply this logic when using other annotations - such as NotFound which causes an EAGER

    // "looking at org.hibernate.type.EntityType.resolveIdentifier and org.hibernate.impl.SessionImpl.internalLoad
    // the latter categorises the load as either NULLABLE, EAGER or LAZY and anything with @NotFound(action=NotFoundAction.IGNORE)
    // was being treated as NULLABLE, regardless of the laziness setting, which actually meant it was eagerly loaded."
    // [Gareth 23 Oct 23:27:28]

    // see http://chekkal.blogspot.com.br/2012/09/hibernate-lazy-loading-and-notfound.html
    // http://stackoverflow.com/questions/1444227/making-a-onetoone-relation-lazy
    // https://developer.jboss.org/wiki/SomeExplanationsOnLazyLoadingone-to-one
    // http://stackoverflow.com/questions/4653909/hibernate-jpa-onetoone-querying-despite-lazy-fetch
    // http://stackoverflow.com/questions/222453/how-to-stop-hibernate-from-eagerly-fetching-many-to-one-associated-object
    /**
     * @deprecated we shouldn't access a User from an Individual
     * because most things refer to an Individual and we wouldn't want the user except in only some scenarios
     */
    @Deprecated
    public User getUser() {
        // without this block, Hibernate complains at the "unsafe use of the session"
        // which could be due to event listeners touching the property, or some hibernate expectatation
        // see http://cranecourt.blogspot.co.uk/2012/06/i-recently-had-to-trouble-shoot-cause.html
        // which says its resolved in hiberante 4.0.1
        if(!Hibernate.isInitialized(user)) {
            try {
                Hibernate.initialize(user);
            } catch (org.hibernate.ObjectNotFoundException one) {
                user = null;
            }
        }
        return user;
    }

    public void setLastName(String lastName) {
        if (this.lastName != null && !lastName.equals(this.lastName)) {
            this.key = null; // reset key so it gets recalculated by recalculateKey()
        }
        this.lastName = lastName;
    }

    @Override
    protected void prePersist() {
        super.prePersist();
        recalculateKey();
    }

    @PreUpdate
    protected void preUpdate() {
        recalculateKey();
    }

    protected void recalculateKey() {
        if ((key == null || key == 0) && hasText(lastName)) {
            key = Sortable.encode(lastName);
        }
    }

    @Override
    protected void postLoadOrPostPersist() {
        super.postLoadOrPostPersist();
        storePreviousNames();
    }

    protected void storePreviousNames() {
        previous_FirstName = firstName;
        previous_LastName = lastName;
    }

    @Override
    protected void postUpdate() {
        super.postUpdate();
        fireContactNameUpdated();
    }

    protected void fireContactNameUpdated() {
        if (!StringUtils.equals(firstName, previous_FirstName) || !StringUtils.equals(lastName, previous_LastName)) {
            final ContactNameUpdated event = new ContactNameUpdated(this, this);
            messageBus.publishBeforeTxEnd(event);
        }
    }

    // NB only used by testing code
    public static Builder builder(String firstName, String lastName) {
        Individual individual = new Individual();
        individual.setFirstName(firstName);
        individual.setLastName(lastName);
        individual.setEmail(firstName+"@me.com"); // include so that any save to cosmo is easier
        return new Builder(individual);
    }

    static public String joinNonBlanksWithSpace(String first, String last) {
        // Deliberately allows JIT to use optimised impl of + and avoids silly case of concatenating an empty string.
        if (StringUtils.isBlank(first)) {
            return StringUtils.isBlank(last) ? "" : last;
        } else {
            return StringUtils.isBlank(last) ? first : first + " " + last;
        }
    }

    @Override
    protected boolean isCalendarable() {
        // only if the individual has a user do we set a calendar
        // which means clients only, and not 'contacts', random test creations(?) etc
        // but doesn't include workers
        // TODO perhaps create an IndividualWithCalendar to override this and be clearer
        return user != null;
    }

    @Override
    public CalendarOwnerDefinition.Builder buildCalendarOwner(CalendarOwnerDefinition.Builder builder) {
        // The builder will already have the contact's email address populated on it by CalendarUserSyncAgent.createCalendar()
        String uniqueUsername = UUID.randomUUID().toString();
        // Use a unique email for the calendaring system (required)
        // This is a separate system and won't be fronted on its own without some syncing, as emails don't sync between accounts
        // now, and many emails are auto-generated for blank provided emails, so are inaccurate anyway
        builder.email(uniqueUsername + "@eccosolutions.co.uk");
        return builder
                .username(uniqueUsername)
                .firstName(getFirstName())
                .lastName(getLastName());
    }

    @Override
    public CalendarOwnerDefinition.Builder buildCalendarNamesUpdate(CalendarOwnerDefinition.Builder builder) {
        return builder
                .firstName(getFirstName())
                .lastName(getLastName());
    }
}
