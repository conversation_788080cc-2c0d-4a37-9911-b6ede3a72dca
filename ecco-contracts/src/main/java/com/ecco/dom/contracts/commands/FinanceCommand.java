package com.ecco.dom.contracts.commands;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import java.util.UUID;

@Entity
@Table(name = "fin_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
public abstract class FinanceCommand extends BaseIntKeyedCommand {

    public FinanceCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime, long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected FinanceCommand() {
    }
}
