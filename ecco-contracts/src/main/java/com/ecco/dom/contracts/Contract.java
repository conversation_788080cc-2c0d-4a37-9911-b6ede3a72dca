package com.ecco.dom.contracts;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.EvidenceCapable;
import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.dom.Identified;
import com.querydsl.core.annotations.QueryInit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Setter
@Getter
@Entity
@Table(name="fin_contracts")
@Configurable
public class Contract extends AbstractUnidentifiedVersionedEntity<Integer> implements EvidenceCapable {

    private static final int DEFAULT_SERVICE_ALLOCATION_ID = -300;

    @Id
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    @GeneratedValue(generator="contractsTableGenerator")
    @TableGenerator(
            name = "contractsTableGenerator", initialValue = 1, pkColumnValue = "contracts",
            allocationSize = 1, table = "hibernate_sequences")
    private Integer id = null;

    @PersistenceContext
    @Transient
    private transient EntityManager em;

    @NotNull
    private String name;

    @Column(name="startInstant", nullable = false)
    @NotNull
    Instant startInstant;
    @Column(name="endInstant")
    Instant endInstant;

    // TODO possibly use json like DemandParameters
    String PONumbers;

    //Service service // ? link to a service to get supplier and purchaser view

    /**
     * An overall charge. Technically a total could be calculated from the schedules but there will always be edge cases
     * so its useful to have a manual definitive value that the client is paying for.
     */
    BigDecimal agreedCharge;

    /**
     * eg Individual (personal budgets) / Block / Framework
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contractTypeId")
    ListDefinitionEntry contractType;

    /**
     * Associated clients/resources - could be ReferralServiceAgreement / BuildingServiceAgreement / BlockServiceAgreement??
     */

//    @OneToMany(mappedBy = "contract")
//    List<ServiceAgreement> serviceAgreements;

    /**
     * Associated rate cards this contract is allowed to use. These are then chosen by DemandSchedules
     */
    @ManyToMany(fetch=FetchType.LAZY)
    @JoinTable(name = "fin_contracts_ratecards",
            joinColumns = @JoinColumn(name = "contractId"),
            inverseJoinColumns = @JoinColumn(name = "rateCardId"))
    List<RateCard> rateCards;

    @OneToOne(cascade= {CascadeType.PERSIST, CascadeType.REMOVE})
    @JoinColumn(name="serviceRecipientId")
    @QueryInit("*.*.*")
    private ContractServiceRecipient serviceRecipient;

    @Setter(AccessLevel.PRIVATE)
    @Column(insertable = false, updatable = false)
    private Integer serviceRecipientId;

    public Contract() {
        injectServices();
    }

    public Object readResolve()  {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @PrePersist
    protected void createServiceRecipient() {
        this.serviceRecipient = new ContractServiceRecipient();
        this.serviceRecipient.setContract(this);
        serviceRecipient.setServiceAllocation(em.getReference(ServiceCategorisation.class, DEFAULT_SERVICE_ALLOCATION_ID));
    }

    @Override
    public EvidenceCapable getParentEvidenceCapable() {
        return null; // doesn't yet support having a parent
    }

    @Override
    public Integer countSiblings() {
        return null;
    }
    @Override
    public Identified getGrandParent() {
        return null;
    }

    @Override
    public String toString() {
        return "Contract {" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
