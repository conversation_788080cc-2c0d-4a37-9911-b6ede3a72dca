package com.ecco.test.entities.hibernate;

import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.infrastructure.config.root.Profiles;
import com.ecco.test.support.TestAppContextInitializer;
import com.github.springtestdbunit.DbUnitTestExecutionListener;
import com.github.springtestdbunit.annotation.DbUnitConfiguration;
import org.hibernate.Session;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.support.DirtiesContextTestExecutionListener;
import org.springframework.test.context.transaction.TransactionalTestExecutionListener;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.sql.DataSource;

@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
@ContextConfiguration(classes=InfrastructureConfig.class, initializers=TestAppContextInitializer.class)
@Transactional

// include a ForeignKeyDisabler so that dbunit files don't get circular problems
// based on http://stackoverflow.com/questions/2685274/tdd-with-hsqldb-removing-foreign-keys
@TestExecutionListeners({ DependencyInjectionTestExecutionListener.class, DirtiesContextTestExecutionListener.class,
        TransactionalTestExecutionListener.class, DbUnitTestExecutionListener.class, ForeignKeyDisabling.class })

// allow the first column to avoid specifying everything in the database
@DbUnitConfiguration(dataSetLoader=AllColumnsDateSubstitutingDataSetLoader.class)

@RunWith(SpringJUnit4ClassRunner.class)
abstract public class BaseTest {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    protected EntityManager getEntityManager() {
        return entityManager;
    }
    protected DataSource getDataSource() {
        return dataSource;
    }

    // flush but don't commit - as this lets us test aspects which would normally occur over transaction boundaries
    // transactions are held otherwise around a test
    protected static void flush(Session session) {
        session.flush();
        session.clear();
    }

    protected static void commit(Session session) throws Exception {
        // flush and close (if created by the session object - which it is)
        // flush mode will be the default 'auto' since we don't set it anywhere
        session.getTransaction().commit();
    }

}
