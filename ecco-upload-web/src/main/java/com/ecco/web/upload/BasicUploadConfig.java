package com.ecco.web.upload;

import com.ecco.dom.upload.SimpleUploadedFile;
import com.ecco.dom.upload.UploadedFile;
import com.ecco.upload.dao.SimpleUploadedFileRepository;
import com.ecco.upload.dao.UploadedFileRepository;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public class BasicUploadConfig extends AbstractUploadConfig<SimpleUploadedFile> {
    private final SimpleUploadedFileRepository repository;

    public BasicUploadConfig(MultipartFile file, SimpleUploadedFileRepository repository) {
        super(file);
        this.repository = repository;
    }

    @Override
    public SimpleUploadedFile constructAttachment() throws IOException {
        SimpleUploadedFile file = populateAttachment(new SimpleUploadedFile());
        file.setMediaType(getFile().getContentType());
        return file;
    }

    @Override
    public List<? extends UploadedFile> findFiles() {
        return repository.findFiles(null);
    }

    @Override
    public UploadedFileRepository<SimpleUploadedFile> getRepository() {
        return repository;
    }
}
