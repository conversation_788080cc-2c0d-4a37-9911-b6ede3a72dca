package com.ecco.rota.service;

import java.util.List;

import com.ecco.dom.agreements.ServiceAgreement;

import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaParams;
import org.jspecify.annotations.Nullable;

/**
 * A strategy for working with a variety of rota back end requirements against a consistent front end
 * service API.allocateResource
 */
public interface RotaHandler {

    /**
     * Determine if this handler is the correct handler for the supplied handle, which can be null for the
     * default rota.
     * The filters form the patterns below, where specificId is designed to help cache parentId:
     *      resource:   type:all|specificId
     *      demand:     type:parentId:specificId
     * NB resources are typically 'type:all' because it's all resources for that demand
     * eg:
     *  - ReferralWorkerRotaHandler - (whole org rota) worker's (all employed at) live rota with demand (all schedules)
     *      - demand referrals:all
     *      - resources workers:all
     *  - BuildingWorkerRotaHandler - worker's (all employed at in bldg) live rota with demand (based on the building)
     *      - demand buildings:bldgId:srId loads building and clients in building and either all careruns in building or specific srId
     *          "used by the rota (see RotaView.tsx rotaRepository.findOneByDate) to get unallocated appointments"
     *          "also used by RotaPreCacheAgent to pre-cache them"
     *      - resource workers:all at the bldg, employed by
     *  - BuildingCareRunRotaHandler - run builder (careruns of the building) with demand (based on the building)
     *      - demand is THE SAME as BuildingWorkerRotaHandler
     *      - resource careruns:all|srId
     *  - ServiceCatWorkerRotaHandler - worker's (users of svccat list) live rota with demand (based on clients in svccat list)
     *      - demand svccats:svccat-list:srId
     *      - resource workers:all
     *
     * @param resourceFilter e.g. "workers:all", "workers:20010", "resources:bed"
     * @param demandFilter e.g. null, "building:123", "referral:100223", "building:123:10023"
     * @return true if can handle this one
     */
    boolean canHandle(String resourceFilter, @Nullable String demandFilter);

    String getResourceCalendarId(int resourceId);

    /**
     * Populate the rota as appropriate.
     * Can be used in conjunction with findAllAgreementsByScheduleDate to get the demand,
     * to repeatedly call this method with demandFilter appended with specific srId,
     * to split up the load and cache it.
     */
    void populateRota(Rota rota);

    /**
     * Find all RESOURCES to loop client-side.
     * Since we used to be findRotaByDate we should be able to re-use the same rota/${resourceFilter}/view logic here (RotaController.viewRota)
     * which is handler.populateRota.
     */
    List<Integer> findAllResourceServiceRecipientIds(RotaParams params);

    /**
     * Find all DEMANDS to loop client-side.
     * Find all agreements appropriate to the rota type in the dates given.
     * Used client-side to get rota demand cached requests - via findAgreementsByDemandAndScheduleDate.
     * This demand should match the populateRota loadDemand - the legacy all at once approach.
     */
    List<Integer> findAllAgreementSrIdsByScheduleDate(RotaParams params);

    /**
     * Find agreements - typically for ad-hoc
     */
    List<ServiceAgreement> findAllAgreementsByScheduleDate(RotaParams params);

}
