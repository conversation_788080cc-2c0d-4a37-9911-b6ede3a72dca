const {config} = require("ecco-webpack-config");

module.exports = config({
    target: "single-page-app",
    publicUrl: "_CONTEXT_PATH_/r/app",
    imageInlineSizeLimit: 81920,
    externals: {
        "application-properties": "applicationProperties",
        "lazy": "Lazy"
    },
    tests: {
        setup: {
            beforeTestEnvironment: ["./tests/setupJest.ts"]
        },
        moduleMap: {
            "application-properties": "./tests/mocks/application-properties.ts",
            "services": "./tests/mocks/services.ts"
        }
    }
});
