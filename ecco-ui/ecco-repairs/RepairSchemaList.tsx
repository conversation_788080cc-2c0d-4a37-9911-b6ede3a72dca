import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {Box} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {useState} from "react";
import {useHistory} from "react-router";
import {SchemaList} from "ecco-components";
import {RepairDto} from "ecco-dto";
import {applicationRootPath} from "application-properties";

/**
 * NB serviceRecipientId comes from SvcRecPageRouter usePageComponentLookup
 * which passes it to the component - so we could add contactId, but instead simply load it server-side for simplicity
 */
export function RepairSchemaList(props: {buildingSrId?: number | undefined}) {
    //const [search, setSearch] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});

    const params = new URLSearchParams();
    if (props.buildingSrId) params.append("buildingSrId", props.buildingSrId.toString());
    //if (search) params.append("search", search);
    if (page) params.append("page", page.toFixed());
    // add filters to the params
    const filterKeys = Object.keys(filters);
    filterKeys.forEach(key => filters[key].forEach(val => params.append(key, val)));

    const paramStr = params.toString();
    const src = paramStr.length ? `repairs/?${paramStr}` : "repairs/";

    const history = useHistory();

    return (
        <Box m={1}>
            <SchemaList
                title="repairs"
                src={src}
                /* see http://localhost:8888/ecco-war/api/repairs/$schema/ */
                displayFields={[
                    "repairId",
                    "receivedDate",
                    "serviceDescription",
                    "categoryName",
                    "significant",
                    "supportWorkerDisplayName",
                    "statusMessage",
                    "reviewDate",
                    "exitedDate"
                ]}
                filterFields={[]}
                filters={filters}
                onFilter={filters => {
                    setPage(0);
                    setFilters(filters);
                }}
                page={page}
                onPage={setPage}
                /*onSearch={search => {
                    setPage(0);
                    setSearch(search);
                }}
                searchText={search}*/
                onRowClick={
                    repair =>
                        (window.location.href = `${applicationRootPath}nav/r/main/sr2/${
                            (repair as any as RepairDto).serviceRecipientId
                        }/`)
                    // history.push
                }
            />
        </Box>
    );
}
