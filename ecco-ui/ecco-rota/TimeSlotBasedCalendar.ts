import {EccoDate, StringToObjectMap} from "@eccosolutions/ecco-common";
import {Activity, TimeRange} from "./rota-domain";

class TimeSlotBasedCalendar<T extends TimeRange<T>> {
    private slotsByDate: StringToObjectMap<T[][]> = {};

    public getEntries(date: EccoDate, index: number): T[] {
        return this.slotsByDate[date.formatIso8601()][index];
    }

    public getEntry(date: EccoDate, index: number, matcher: (e: T) => boolean): T {
        return this.getEntries(date, index).find(matcher)!!; // Assumes we'll always find it
    }

    public setEntry(date: EccoDate, index: number, entry: T) {
        let slotsForDate = this.slotsByDate[date.formatIso8601()];
        if (!slotsForDate) {
            slotsForDate = this.slotsByDate[date.formatIso8601()] = [];
        }
        let slot = slotsForDate[index];
        if (!slot) {
            slotsForDate[index] = [];
            slot = slotsForDate[index];
        }
        slot.push(entry);
    }

    public hasEntryAtTimeSlot(date: EccoDate, index: number): boolean {
        let slot = this.getExistingSlot(date, index);
        return slot != null && slot.length > 0;
    }

    public hasOverlappingEntryAtTimeSlot(date: EccoDate, index: number, entry: T): boolean {
        const slot = this.getExistingSlot(date, index);
        if (!slot) return false;
        // NB testing equality 'existingEntry != entry' could not match, depending on when the entry is constructed,
        // so it's best to test equality within 'intersects' and return false for the same object
        return (
            slot.find(existingEntry => existingEntry.intersects(entry) && existingEntry != entry) !=
            null
        );
    }

    public removeEntry(date: EccoDate, index: number, entry: T) {
        const slot = this.getExistingSlot(date, index);
        const found = slot.indexOf(entry);
        if (found >= 0) {
            slot.splice(found, 1);
        }
    }

    hasOverlappingActivityAtTimeSlot(date: EccoDate, activity: Activity, index: number) {
        const slot = this.getExistingSlot(date, index);
        if (!slot) return false;
        return (
            slot.find(existingEntry =>
                existingEntry.intersectsInterval(activity.getStart(), activity.getEnd())
            ) != null
        );
    }

    private getExistingSlot(date: EccoDate, index: number) {
        const slotsForDate = this.slotsByDate[date.formatIso8601()];
        return slotsForDate && slotsForDate[index];
    }
}

export default TimeSlotBasedCalendar;
