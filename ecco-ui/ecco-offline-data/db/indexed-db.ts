import * as bowser from "bowser"
import {delay} from "@eccosolutions/ecco-common";

import {Observable} from "rxjs";


let dbFactory = window.indexedDB;
let keyRangeFactory = IDBKeyRange;

// Don't support offline on iOS 9 which has bugging IndexedDB (we used to polyfill for WebSQL but removing that)
if (bowser.ios && Boolean(String(bowser.osversion).match(/^9(?:\D|$)/))) {
    dbFactory = null!!; // Yes. That's nasty, but it allows code below to correctly know that dbFactory is defined
}
if (!dbFactory) {
    console.error("This is an old browser that does not support offline working (see https://caniuse.com/#feat=indexeddb)");
}

export var NOT_SUPPORTED = {
    toString: () => "Not supported."
};

/** Creates a Transaction Error event handler that rejects via the specified callback */
function onTransactionError<T>(reject: (reason?: any) => void) {
    return (event: Event) => {
        const reason = (<IDBTransaction>event.target).error;
        console.error("Database transaction error: %o", reason);
        reject(reason);
    };
}

/** Creates a Transaction Abort event handler that rejects via the specified callback */
function onTransactionAbort<T>(reject: (reason?: any) => void) {
    //noinspection JSUnusedLocalSymbols
    return (event: Event) => {
        console.warn("Database transaction aborted");
        reject(new Error("Transaction aborted."));
    };
}

/** Creates a Request Error event handler that rejects the specified
 * Deferred object. */
function onRequestError<T>(reject: (reason?: any) => void) {
    return (event: Event) => {
        const reason = (<IDBRequest>event.target).error;
        console.error("Database request rejected with error %o", reason);
        reject(reason);
    };
}

/** Tests if IndexedDB is supported by the browser. */
export function isSupported(): boolean {
    return !!dbFactory && !!dbFactory.open;
}

/** A database Schema. */
export interface Schema {
    /** The name of the IndexedDB database. */
    getName(): string;

    /** The database version number.
     *
     * You must increment this version number whenever the schema changes. */
    getVersion(): number;

    /** Upgrades or creates the database schema.
     *
     * The implementation of this method is the only opportunity to create or
     * modify the database schema. It is called when opening a newly-created
     * database, or when opening an existing database whose version number is
     * less than the version number of this Schema object.
     *
     * This method must be able to create a schema from scratch as well as be
     * able to upgrade a database from any earlier version of the schema to the
     * current version, so implement it carefully.
     *
     * The upgrade takes place in an isolated transaction before the database
     * is available for querying, so it either succeeds or fails as a whole.
     *
     * After the upgrade, the IndexedDB infrastructure automatically sets the
     * database version number to the version number of this Schema object. */
    upgrade(database: IDBDatabase, transaction: IDBTransaction, oldVersion: number): void;
}

/** An IndexedDB database. */
export class Database<TSchema extends Schema> {
    /** Opens an IndexedDB database with the specified schema.
     *
     * Returns a promise which is fulfilled by the successfully opened
     * database, or rejected if the database could not be opened.
     *
     * If a database with the name schema.name does not exist, then it will
     * be created, and schema.upgrade() will be called to create the schema
     * before the database is opened.
     *
     * If a database with the name schema.name already exists, but its
     * version number is less than schema.version, then schema.upgrade() will
     * be called to upgrade the schema before the database is opened.
     *
     * If a database with the name schema.name already exists, but its
     * version number is greater than schema.version, then the database cannot
     * be opened and the promise will be rejected. */
    public static open<TSchema extends Schema>(schema: TSchema): Promise<Database<TSchema>> {
        if (!schema) {
            throw new TypeError("'schema' is not set.");
        }

        if (!isSupported()) {
            throw new Error("IndexedDB is not supported by this browser.");
        }

        return new Promise<Database<TSchema>>(((resolve, reject) => {

            const openRequest = dbFactory.open(schema.getName(), schema.getVersion());

            openRequest.onerror = reject;

            openRequest.onupgradeneeded = (event: IDBVersionChangeEvent) => {
                const database = <IDBDatabase>openRequest.result;
                const transaction = openRequest.transaction!!;
                transaction.onerror = onTransactionError(reject);
                transaction.onabort = onTransactionAbort(reject);

                schema.upgrade(database, transaction, event.oldVersion);
            };

            openRequest.onsuccess = () => {
                resolve(new Database<TSchema>(<IDBDatabase>openRequest.result));
            };
        }));
    }

    /** Deletes the database with the specified name.
     *
     * Returns a promise for void, which will resolve if the database
     * existed and was successfully deleted, or if it did not exist in the
     * first place.
     *
     * This operation is not supported by all browsers. If the operation fails,
     * then the promise will be rejected with the reason NOT_SUPPORTED.
     *
     * If the operation fails for any other reason, then the promise will be
     * rejected with the corresponding DOMError as its reason. */
    public static deleteDatabase(name: string): Promise<void> {
        if (name == null) {
            throw new TypeError("Name must not be null.")
        }

        return new Promise<void>(((resolve, reject) => {
            if (dbFactory.deleteDatabase) {
                const request = dbFactory.deleteDatabase(name);

                request.onsuccess = () => resolve();
                request.onerror = () => reject(request.error);
            } else {
                reject(NOT_SUPPORTED);
            }
        }));
    }

    constructor(private database: IDBDatabase) {
        if (!database) {
            throw new TypeError("'database' is not set");
        }
    }

    /** Closes the connection to the database. */
    public close(): void {
        this.database.close();
    }

    /** Deletes the database.
     *
     * Returns a promise for void, which will resolve if the database was
     * successfully deleted.
     *
     * This operation is not supported by all browsers. If the operation fails,
     * then the promise will be rejected with the reason NOT_SUPPORTED.
     *
     * If the operation fails for any other reason, then the promise will be
     * rejected with the corresponding DOMError as its reason. */
    public deleteDatabase(): Promise<void> {
        this.close();

        return Database.deleteDatabase(this.database.name);
    }

    /** Finds a single object from the specified object store. The object is
     * identified by its object ID. */
    public findOne<T>(objectStoreName: string, objectId: any): Promise<T> {
        return this.withReadTransaction<T>([objectStoreName], (transaction: ReadTransaction<T>) => {
            transaction.findOne<T>(objectStoreName, objectId)
                .then((result: T) => transaction.resolve(result))
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Finds zero or more objects from the specified object store, using the
     * specified index. The objects are identified by their corresponding
     * key(s) in the index. */
    public findBy<T>(objectStoreName: string, indexName: string, indexKey: any): Promise<T[]> {
        return this.withReadTransaction<T[]>([objectStoreName], (transaction: ReadTransaction<T[]>) => {
            transaction.findBy<T>(objectStoreName, indexName, indexKey)
                .then((result: T[]) => transaction.resolve(result))
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Finds zero or more objects from the specified object store, using the
     * specified index. The objects are identified by their corresponding
     * key(s) in the index. Produce a cold Observable of the results */
    public asyncFindBy<T>(objectStoreName: string, indexName: string, indexKey: any): Observable<T> {

        return new Observable<T>(observer => {
            let transaction = this.database.transaction([objectStoreName], "readonly");
            transaction.onerror = (event: Event) => observer.error((<IDBTransaction>event.target).error);
            transaction.onabort = () => observer.error(new Error("Transaction aborted."));

            let objectStore = transaction.objectStore(objectStoreName);
            let index = objectStore.index(indexName);
            let cursor = index.openCursor(keyRangeFactory.only(indexKey));

            cursor.onerror = (event: Event) => observer.error((<IDBRequest>event.target).error);

            cursor.onsuccess = () => {
                if (cursor.result) {
                    observer.next(cursor.result.value);
                    cursor.result.continue();
                } else {
                    observer.complete();
                }
            };
        });
    }

    /** Finds a single object from the specified object store, using the
     * specified index. The object is identified by its corresponding key in
     * the index. */
    public findOneBy<T>(objectStoreName: string, indexName: string, indexKey: any): Promise<T> {
        return this.withReadTransaction<T>([objectStoreName], (transaction: ReadTransaction<T>) => {
            transaction.findOneBy<T>(objectStoreName, indexName, indexKey)
                    .then((result: T) => transaction.resolve(result))
                    .catch((reason) => transaction.reject(reason));
        });
    }

    /** Finds all of the objects in the specified object store. */
    public findAll<T>(objectStoreName: string): Promise<T[]> {
        return this.withReadTransaction<T[]>([objectStoreName], (transaction: ReadTransaction<T[]>) => {
            transaction.findAll<T>(objectStoreName)
                .then((result: T[]) => transaction.resolve(result))
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Finds all of the objects in the specified object store, and produce a cold Observable
     *  of the results */
    public asyncFindAll<T>(objectStoreName: string): Observable<T> {

        console.debug("asyncFindAll(" + objectStoreName + ")");

        return new Observable<T>(observer => {
            let transaction = this.database.transaction([objectStoreName], "readonly");
            transaction.onerror = (event: Event) => observer.error((<IDBTransaction>event.target).error);
            transaction.onabort = () => observer.error(new Error("Transaction aborted."));

            let objectStore = transaction.objectStore(objectStoreName);
            console.debug("opening cursor for asyncFindAll(%s)", objectStoreName);
            let cursor = objectStore.openCursor();

            cursor.onerror = (event: Event) => observer.error((<IDBRequest>event.target).error);

            cursor.onsuccess = () => {
                if (cursor.result) {
                    observer.next(cursor.result.value);
                    cursor.result.continue();
                } else {
                    observer.complete();
                }
            };
        })
        .do( value => {console.debug("findAllAsync(" + objectStoreName + ") -> %o", value);});
    }

    /** Counts all of the objects in the specified object store. */
    public count(objectStoreName: string): Promise<number> {
        return this.withReadTransaction<number>([objectStoreName], (transaction: ReadTransaction<number>) => {
            transaction.count(objectStoreName)
                .then((count: number) => transaction.resolve(count))
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Saves an object to the specified object store. The object is uniquely
     * identified by an object ID.
     *
     * If the object store has an associated key path, then the object ID is
     * determined implicitly and the objectId parameter must not be specified.
     *
     * If the object store does not have an associated key path, then the
     * objectId parameter is used as the object ID and is required.
     *
     * If there is an existing object in the object store with the same object
     * ID, then it is replaced by the new object. Otherwise, a new object is
     * created.
     * @returns a promise for the object that was saved
     */
    public save<T>(objectStoreName: string, object: T, objectId?: any): Promise<T> {
        return this.withReadWriteTransaction<T>([objectStoreName], (transaction: ReadWriteTransaction<T>) => {
            transaction.save<T>(objectStoreName, object, objectId)
                .then((result: T) => transaction.resolve(result))
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Removes an object from the specified object store. The object to remove
     * is identified by the specified objectId. */
    public remove(objectStoreName: string, objectId: any): Promise<void> {
        return this.withReadWriteTransaction<void>([objectStoreName], (transaction: ReadWriteTransaction<void>) => {
            transaction.remove(objectStoreName, objectId)
                .then(() => transaction.resolve())
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Removes all objects from the specified object store. */
    public clear(objectStoreName: string): Promise<void> {
        return this.withReadWriteTransaction<void>([objectStoreName], (transaction: ReadWriteTransaction<void>) => {
            transaction.clear(objectStoreName)
                .then(() => transaction.resolve())
                .catch((reason) => transaction.reject(reason));
        });
    }

    /** Creates an isolated transaction for performing read-only operations. */
    public withReadTransaction<T>(objectStoreNames: string[], handler: (transaction: ReadTransaction<T>) => void): Promise<T> {
        return this.withTransaction<T>(objectStoreNames, "readonly", (transaction: IDBTransaction, opResolve, opReject) => {
            handler(new ReadTransactionImpl(transaction, opResolve, opReject));
        });
    }

    /** Creates an isolated transaction for performing read-write operations. */
    public withReadWriteTransaction<T>(objectStoreNames: string[], handler: (transaction: ReadWriteTransaction<T>) => void): Promise<T> {
        return this.withTransaction<T>(objectStoreNames, "readwrite", (transaction: IDBTransaction, opResolve, opReject) => {
            handler(new ReadWriteTransactionImpl<T>(transaction, opResolve, opReject));
        });
    }

    private withTransaction<T>(objectStoreNames: string[],
                               accessMode: IDBTransactionMode,
                               operation: (transaction: IDBTransaction, opResolve: (value?: (T | PromiseLike<T>)) => void, opReject: (reason?: any) => void) => void): Promise<T> {
        return new Promise<T>((txResolve, txReject) => {

            let operationComplete = false;
            let transactionComplete = false;
            let result: T | null | undefined = null;

            const opPromise = new Promise<T | undefined>((opResolve, opReject) => {

                var transaction = this.database.transaction(objectStoreNames, accessMode);
                transaction.onerror = onTransactionError(opReject);
                transaction.onabort = onTransactionAbort(opReject);
                transaction.oncomplete = () => {
                    transactionComplete = true;

                    if (operationComplete) {
                        txResolve(result!!);
                    } else {
                        // Hack: Give the operation an extra 100ms to finish, to work around ECCO-1136.
                        delay(100)
                            .then(() => txReject(new Error(accessMode + " transaction on stores ["
                                + objectStoreNames + "] committed before result was received (see ECCO-1136).")));
                    }
                };

                operation(transaction, opResolve, opReject);
            });

            opPromise
                .then(r => {
                    operationComplete = true;
                    result = r;

                    if (transactionComplete) {
                        txResolve(result as T);
                    }
                })
                .catch(reason => {
                    console.error(`Transaction failed due to ${reason}`);
                    txReject(reason);
                });
        });
    }
}

export interface ReadTransaction<TTransactionResult> {
    reject(reason: any): void;
    resolve(result: TTransactionResult): void;

    findOne<TElement>(objectStoreName: string, objectId: any): Promise<TElement>;
    findBy<TElement>(objectStoreName: string, indexName: string, indexKey: any): Promise<TElement[]>;
    findOneBy<TElement>(objectStoreName: string, indexName: string, indexKey: any): Promise<TElement>;
    findAll<TElement>(objectStoreName: string): Promise<TElement[]>;
    count(objectStoreName: string): Promise<number>;
}

export interface ReadWriteTransaction<TTransactionResult> extends ReadTransaction<TTransactionResult> {
    /** Adds to the database except where objectId is present - in which case replace */
    save<TElement>(objectStoreName: string, object: TElement, objectId?: any): Promise<TElement>;
    remove(objectStoreName: string, key: any): Promise<void>;
    clear(objectStoreName: string): Promise<void>;
}

class ReadTransactionImpl<TTransactionResult> implements ReadTransaction<TTransactionResult> {
    constructor(private transaction: IDBTransaction,
                private opResolve: (value?: TTransactionResult) => void,
                private opReject: (reason?: any) => void) {
    }

    public reject(reason?: any): void {
        this.opReject(reason);
    }

    public resolve(result: TTransactionResult): void {
        this.opResolve(result);
    }

    public findOne<TElement>(objectStoreName: string, objectId: any): Promise<TElement> {
        const promise = new Promise<TElement>((resolve, reject) => {
            const objectStore = this.transaction.objectStore(objectStoreName);
            const request = objectStore.get(objectId);
            request.onerror = onRequestError(reject);

            request.onsuccess = () => {
                resolve(request.result);
            };
        });
        promise.catch(reason => this.reject(reason));
        return promise;
    }

    public findBy<TElement>(objectStoreName: string, indexName: string, indexKey: any): Promise<TElement[]> {
        const promise = new Promise<TElement[]>((resolve, reject) => {
            const objectStore = this.transaction.objectStore(objectStoreName);
            const index = objectStore.index(indexName);
            const cursor = index.openCursor(keyRangeFactory.only(indexKey));
            cursor.onerror = onRequestError(reject);

            const result: TElement[] = [];

            cursor.onsuccess = () => {
                if (cursor.result) {
                    result.push(cursor.result.value);
                    cursor.result['continue']();
                } else {
                    resolve(result);
                }
            };
        });
        promise.catch(reason => this.reject(reason));
        return promise;
    }

    public findOneBy<TElement>(objectStoreName: string, indexName: string, indexKey: any): Promise<TElement> {
        const promise = new Promise<TElement>((resolve, reject) => {
            const objectStore = this.transaction.objectStore(objectStoreName);
            const index = objectStore.index(indexName);
            const request = index.get(indexKey);
            request.onerror = onRequestError(reject);

            request.onsuccess = () => resolve(request.result);
        });
        promise.catch(reason => this.reject(reason));
        return promise;
    }

    public findAll<TElement>(objectStoreName: string): Promise<TElement[]> {
        const promise = new Promise<TElement[]>((resolve, reject) => {

            const objectStore = this.transaction.objectStore(objectStoreName);
            const cursor = objectStore.openCursor();
            cursor.onerror = onRequestError(reject);

            const result: TElement[] = [];

            cursor.onsuccess = () => {
                if (cursor.result) {
                    result.push(cursor.result.value);
                    cursor.result['continue']();
                } else {
                    resolve(result);
                }
            };
        });
        promise.catch(reason => this.reject(reason));
        return promise;
    }

    public count(objectStoreName: string): Promise<number> {
        const promise = new Promise<number>((resolve, reject) => {
            const objectStore = this.transaction.objectStore(objectStoreName);
            const request = objectStore.count();
            request.onerror = onRequestError(reject);
            request.onsuccess = () => resolve(request.result);
        });
        promise.catch(reason => this.reject(reason));
        return promise;
    }
}

class ReadWriteTransactionImpl<TTransactionResult> extends ReadTransactionImpl<TTransactionResult> implements ReadWriteTransaction<TTransactionResult> {
    private readWriteTransaction: IDBTransaction;

    constructor(transaction: IDBTransaction, opResolve: (value?: (TTransactionResult | PromiseLike<TTransactionResult>)) => void, opReject: (reason?: any) => void) {
        super(transaction, opResolve, opReject);
        this.readWriteTransaction = transaction;
    }

    public save<TElement>(objectStoreName: string, object: TElement, objectId?: any): Promise<TElement> {
        const promise = new Promise<TElement>((resolve, reject) => {
            const objectStore = this.readWriteTransaction.objectStore(objectStoreName);

            // see https://developer.mozilla.org/en-US/docs/Web/API/IDBObjectStore/put
            const request: IDBRequest = typeof objectId === "undefined" || objectId === null
                ? objectStore.put(object)
                : objectStore.put(object, objectId);
            request.onerror = onRequestError(reject);
            request.onsuccess = () => resolve(object);
        });
        promise.catch(reason => this.reject(reason));
        return promise;
    }

    public remove(objectStoreName: string, key: any): Promise<void> {
        const promise = new Promise<void>((resolve, reject) => {
            const objectStore = this.readWriteTransaction.objectStore(objectStoreName);

            const request = objectStore['delete'](key);
            request.onerror = onRequestError(reject);
            request.onsuccess = () => resolve();
        });
        promise.catch((reason: any) => this.reject(reason));
        return promise;
    }

    public clear(objectStoreName: string): Promise<void> {
        const promise = new Promise<void>((resolve, reject) => {
            const objectStore = this.readWriteTransaction.objectStore(objectStoreName);

            const request = objectStore.clear();
            request.onerror = onRequestError(reject);
            request.onsuccess = () => resolve();
        });

        promise.catch(reason => this.reject(reason));
        return promise;
    }
}
