import {bus} from "@eccosolutions/ecco-common";
import { OfflineSyncStatus } from './OfflineSyncStatus';

export class OfflineSyncStatusEvent {
    public static bus = bus<OfflineSyncStatusEvent>();

    /** Call from service worker to pass events to navigator */
    public static initServiceWorker(serviceWorker: EventTarget /* ServiceWorkerGlobalScope */) {

        serviceWorker.addEventListener('message', (event: any /* MessageEvent */) => {
            if ((event as MessageEvent).data instanceof OfflineSyncStatusEvent) {
                OfflineSyncStatusEvent.bus.fire(event.data)
            }
        });
        OfflineSyncStatusEvent.bus.addHandler(event => {
            (serviceWorker as any).clients.matchAll().then((all: any[]) => all.map((client: any) => client.postMessage(event)));
        })
    }

    /** Call from navigator to pass events to service worker */
    public static initNavigator(nav: Navigator) {
        nav.serviceWorker.ready.then(worker => {
            worker.addEventListener('message', event => {
                if (event instanceof MessageEvent && event.data instanceof OfflineSyncStatusEvent) {
                    OfflineSyncStatusEvent.bus.fire(event.data)
                }
            });
            OfflineSyncStatusEvent.bus.addHandler(event => {
                if (worker.active) {
                    worker.active.postMessage(event)
                }
                else {
                    console.info("Couldn't pass event to inactive service worker: %o", event)
                }
            });
        });
    }

    /**
     * status: The current status of the sync operation.
     *
     * progress: A number between 0 and 1 representing the overall progress
     * of the sync operation.  If null, this isn't a progress update.
     *
     * message: A human-readable message describing the current status of the
     * sync operation.
     */
    constructor(private status: OfflineSyncStatus,
            private progress: number | null,
            private message: string) {
    }

    /** Gets the current status of the sync operation. */
    public getStatus(): OfflineSyncStatus {
        return this.status;
    }

    /** Gets a number between 0 and 1 representing the overall progress of the
     * sync operation. */
    public getProgress(): number | null {
        return this.progress;
    }

    /** Gets a human-readable message describing the current status of the
     *  sync operation. */
    public getMessage(): string {
        return this.message;
    }
}