import {withMobileDialog, WithMobileDialog, WithWidth} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, MouseEventHandler, ReactNode} from "react";
import * as ReactDOM from "react-dom";
import {DialogAction, EccoModal, Footer} from "./forms";

interface Props {
    /** If omitted, modal is always shown */
    show?: boolean | undefined;
    title: string;
    action: DialogAction;
    children: ReactNode;
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | undefined;
    minHeight?: number | undefined;
    saveEnabled?: boolean | undefined;
    onCancel: () => void;
    onSave?: MouseEventHandler<any> | undefined;
    isLegacy?: true | undefined;
    helpTitle?: ReactNode | undefined;
    helpCallback?: (() => void) | undefined;
    helpContent?: ReactNode | Promise<ReactNode> | undefined;
    showPrint?: true | undefined;
}

const _EccoV3Modal: FC<Props & WithMobileDialog & Partial<WithWidth>> = props => {
    // We have a 'close' button too close to our 'save', and we don't need the close/cancel
    // because <EccoModal below uses DialogTitleWithClose which always has a close icon (or back arrow) at the top.
    // So we can ignore the actual 'cancel' or 'close' icon by supplying actionWithClose={false}
    const footer = (
        <Footer
            action={props.action}
            actionWithoutCancel={true}
            onCancel={props.onCancel}
            onSave={props.onSave}
            saveEnabled={props.saveEnabled}
        />
    );
    return <EccoModal onEscapeKeyDown={props.onCancel} footer={footer} {...props}></EccoModal>;
};

export const EccoV3Modal = withMobileDialog<Props>()(_EccoV3Modal);

/** Performs the same as EccoV3Modal, but instead of having title and footer around a modal dialog, we
 * use React.createPortal() to render to "editor-actions" and "editor-title".
 * If #editor-actions isn't found, then the actions will be rendered below
 */
export class EccoV3EditPane extends React.Component<Props> {
    private titleEl = document.getElementById("editor-title");
    private actionsEl = document.getElementById("editor-actions");

    override render() {
        const {title, onSave, onCancel, action, saveEnabled, children} = this.props;

        const actions = (
            <Footer action={action} onCancel={onCancel} onSave={onSave} saveEnabled={saveEnabled} />
        );

        return (
            <div
                className={`v-gap-15 container-fluid ${
                    this.props.isLegacy !== undefined ? "" : "m-ui"
                }`}
            >
                {this.titleEl
                    ? ReactDOM.createPortal(title, this.titleEl)
                    : title != "" && <h4 className="center text-lowercase">{title}</h4>}
                {children}
                {this.actionsEl ? (
                    ReactDOM.createPortal(actions, this.actionsEl)
                ) : (
                    <div style={{display: "flex"}}>
                        <span style={{marginLeft: "auto"}}>
                            {actions /* Fallback to putting actions below if nowhere to mount */}
                        </span>
                    </div>
                )}
            </div>
        );
    }
}
