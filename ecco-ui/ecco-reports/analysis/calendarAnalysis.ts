import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {EccoDateTime} from "@eccosolutions/ecco-common";
import types = require("./types");
import Analyser = types.Analyser;
import Group = types.Group;
import SequenceAnalysis = types.SequenceAnalysis;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import {EventResourceDto, SessionData} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import {
    booleanColumn,
    columnMap,
    dateTimeColumn,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {referralReportItemColumns} from "../tables/predefined-table-representations";
import analysisTypes = require("../analysis/types");
import ReferralAggregate = analysisTypes.ReferralAggregate;
import {Transformer} from "../analysis/types";

//*********************************
// exported properties

//*********************************
// Grouped Analysis: Calendar

const calendarCountsByServiceAnalyser: Analyser<
    Sequence<CalendarEventWithParent>,
    Sequence<Group<EventResourceDto>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<CalendarEventWithParent>
): GroupedCalendarAnalysis {
    return new GroupedCalendarAnalysis(
        ctx,
        calendarCountsBy(
            input,
            (input: Sequence<CalendarEventWithParent>, ctx?: AnalysisContext) =>
                groupByService(input, ctx!.getSessionData()),
            ctx
        )
    );
};

export function groupByService(
    input: Sequence<CalendarEventWithParent>,
    sessionData: SessionData
): Sequence<Group<CalendarEventWithParent>> {
    return input
        .groupBy(
            inputElement =>
                sessionData.getServiceCategorisation(
                    inputElement.parent.referral.serviceAllocationId
                ).serviceName
        )
        .pairs()
        .map(extractPair);
}

const calendarCountsByProjectAnalyser: Analyser<
    Sequence<CalendarEventWithParent>,
    Sequence<Group<EventResourceDto>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<CalendarEventWithParent>
): GroupedCalendarAnalysis {
    return new GroupedCalendarAnalysis(
        ctx,
        calendarCountsBy(
            input,
            (input: Sequence<CalendarEventWithParent>, ctx?: AnalysisContext) =>
                groupByProject(input, ctx!.getSessionData()),
            ctx
        )
    );
};

export function groupByProject(
    input: Sequence<CalendarEventWithParent>,
    sessionData: SessionData
): Sequence<Group<CalendarEventWithParent>> {
    return input
        .groupBy(
            inputElement =>
                sessionData.getServiceCategorisation(
                    inputElement.parent.referral.serviceAllocationId
                ).projectName || ""
        )
        .pairs()
        .map(extractPair);
}

const calendarCountsByTypeAnalyser: Analyser<
    Sequence<EventResourceDto>,
    Sequence<Group<EventResourceDto>>
> = function (ctx: AnalysisContext, input: Sequence<EventResourceDto>): GroupedCalendarAnalysis {
    return new GroupedCalendarAnalysis(ctx, calendarCountsBy(input, groupByCalendarType));
};

export function groupByCalendarType(
    input: Sequence<EventResourceDto>
): Sequence<Group<EventResourceDto>> {
    return input
        .groupBy(inputElement => inputElement.eventType)
        .pairs()
        .map(extractPair);
}

const calendarCountsByCategoryAnalyser: Analyser<
    Sequence<EventResourceDto>,
    Sequence<Group<EventResourceDto>>
> = function (ctx: AnalysisContext, input: Sequence<EventResourceDto>): GroupedCalendarAnalysis {
    return new GroupedCalendarAnalysis(ctx, calendarCountsBy(input, groupByCalendarCategory, ctx));
};

export function groupByCalendarCategory(
    input: Sequence<EventResourceDto>,
    ctx?: AnalysisContext
): Sequence<Group<EventResourceDto>> {
    return input
        .groupBy(inputElement =>
            inputElement.eventCategoryId
                ? ctx!
                      .getSessionData()
                      .getListDefinitionEntryById(inputElement.eventCategoryId)
                      .getDisplayName()
                : "no category"
        )
        .pairs()
        .map(extractPair);
}

class GroupedCalendarAnalysis extends SequenceAnalysis<Group<EventResourceDto>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<EventResourceDto>>) {
        super(ctx, data, (item: Group<EventResourceDto>) => item.key);
        // This deals with clicking on chart segment
        this.addOnClickAnalyser(
            "ungroup",
            (ctx: AnalysisContext, input: Group<EventResourceDto>): CalendarAnalysis => {
                return new CalendarAnalysis(
                    ctx,
                    input.elements as Sequence<CalendarEventWithParent>
                );
            }
        );
        this.addOnClickManyAnalysis("ungroup", CalendarAnalysis);
    }
}

function calendarCountsBy<T>(
    input: Sequence<T>,
    groupFn: GroupFn<T>,
    ctx?: AnalysisContext
): Sequence<Group<T>> {
    return groupFn(input, ctx).map(pair => {
        let input: Sequence<T> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}

//*********************************
// Analysis: Calendar

export const calendarOnlyColumns = columnMap(
    textColumn<EventResourceDto>("uid", row => row.uid),
    textColumn<EventResourceDto>("title", row => row.title),
    textColumn<EventResourceDto>("category", (row, ctx) =>
        row.eventCategoryId
            ? ctx.getSessionData().getListDefinitionEntryById(row.eventCategoryId).getDisplayName()
            : null
    ),
    dateTimeColumn<EventResourceDto>("planned date", row =>
        row.start ? EccoDateTime.parseIso8601(row.start.toString()) : null
    ),
    numberColumn<EventResourceDto>("planned time (mins)", row =>
        row.start && row.end
            ? Math.round(
                  EccoDateTime.parseIso8601(row.end.toString())
                      .subtractDateTime(EccoDateTime.parseIso8601(row.start.toString()), true)
                      .inMinutes()
              )
            : null
    ),
    booleanColumn<EventResourceDto>("allDay", row => row.allDay),
    booleanColumn<EventResourceDto>("recurrence", row => row.recurrence),
    booleanColumn<EventResourceDto>("recurringEntry", row => row.recurringEntry),
    numberColumn<EventResourceDto>("contactId", row => row.contactId),
    textColumn<EventResourceDto>("workUuid", row => row.evidenceWorkUuid),
    textColumn<EventResourceDto>("location", row => row.location),
    // attendees
    // calendarIdUserReferenceUri
    textColumn<EventResourceDto>("status", (row, ctx) =>
        row.eventStatusId
            ? ctx.getSessionData().getListDefinitionEntryById(row.eventStatusId).getDisplayName()
            : null
    ),
    textColumn<EventResourceDto>("eventType", row => row.eventType),
    textColumn<EventResourceDto>("ownerCalendarId", row => row.ownerCalendarId),
    textColumn<EventResourceDto>("updatedByUri", row => row.updatedByUri),
    numberColumn<EventResourceDto>("sr-id", row => row.serviceRecipientId),
    numberColumn<EventResourceDto>("alloc-id", row => row.serviceAllocationId),
    textColumn<EventResourceDto>("prefix", row => row.serviceRecipientPrefix)

    // The itemUid's owner reference uri.
    // The calendar id these events were retrieved from
    //calendarIdUserReferenceUri?

    //links? hateos

    //attendees: EventAttendee[];

    // address
    //location: string;

    // This is ISO-8601 date-time when retrieved from server
    // start: moment.Moment | string;
    // end: moment.Moment | string;

    // Other(-1), Interview(0), Review(1), Target(2), Expires(3), Meeting(4);
    //eventType: string;

    // Unknown(-1), DNA(0), Success(1), Rescheduled(2);
    // eventStatus: string;
);

const calendarToReferralAndClientColumns = joinNestedPathColumnMaps<
    CalendarEventWithParent,
    ReferralAggregate
>("r", row => row.parent, referralReportItemColumns);
const calendarColumns = joinColumnMaps(calendarOnlyColumns, calendarToReferralAndClientColumns);

export interface CalendarEventWithParent extends EventResourceDto {
    parent: ReferralAggregate;
}

export function flattenCalendar(
    input: Sequence<ReferralAggregate>
): Sequence<CalendarEventWithParent> {
    const allEvents: Sequence<CalendarEventWithParent> = input
        .filter(ra => ra != null && ra.referral.calendarEvents != null)
        .map(ra => ra.referral.calendarEvents.map(e => ensureCalendarReferencesItem(ra, e)))
        .flatten<CalendarEventWithParent>();
    return allEvents;
}
function ensureCalendarReferencesItem(
    item: ReferralAggregate,
    event: EventResourceDto
): CalendarEventWithParent {
    const result = <CalendarEventWithParent>event;
    result.parent = item;
    return result;
}
interface ItemsEntry<T> {
    key: string;
    items: T[];
}
export const calendarEventsFromReferralAggregateAnalyser: Transformer<
    ReferralAggregate,
    CalendarEventWithParent
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<CalendarEventWithParent> {
    return new CalendarAnalysis(ctx, flattenCalendar(input));
};

export function groupByCalendarEventType(
    input: Sequence<ReferralAggregate>
): Sequence<ItemsEntry<CalendarEventWithParent>> {
    const work = flattenCalendar(input);
    return work
        .groupBy(inputElement => inputElement.eventType || "no type assigned")
        .pairs()
        .map(group => ({key: group[0], items: group[1]}));
}


export class CalendarAnalysis extends SequenceAnalysis<CalendarEventWithParent> {
    constructor(ctx: AnalysisContext, data: Sequence<CalendarEventWithParent>) {
        super(ctx, data, (item: CalendarEventWithParent) => item.uid);
        this.derivativeAnalysers = {
            calendarCountsByType: calendarCountsByTypeAnalyser,
            calendarCountsByCategory: calendarCountsByCategoryAnalyser,
            calendarCountsByService: calendarCountsByServiceAnalyser,
            calendarCountsByProject: calendarCountsByProjectAnalyser
        };
        this.recordRepresentation = {
            CalendarOnlyColumns: calendarOnlyColumns,
            CalendarColumns: calendarColumns
        };
    }
}
