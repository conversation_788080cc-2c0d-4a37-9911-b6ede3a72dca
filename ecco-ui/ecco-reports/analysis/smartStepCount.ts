import Lazy = require("lazy");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import types = require("./types");
import Group = types.Group;
import extractPair = types.extractPair;
import * as evidenceDto from "ecco-dto";
import {isNotNull} from "@softwareventures/nullable";

function supportActionWithWorkFromWork(
    supportWork: evidenceDto.SupportWork,
    actions: Sequence<evidenceDto.SupportAction>
): Sequence<types.SupportActionWithWork> {
    return actions.filter(isNotNull).map(supportAction => {
        var result: types.SupportActionWithWork = {
            supportWork: supportWork,
            supportAction: supportAction
        };
        return result;
    });
}
export function smartStepCountsFromSupportWork(
    supportWork: Sequence<evidenceDto.SupportWork>
): Sequence<types.SupportActionWithWork> {
    if (!supportWork) {
        throw new Error("supportWork[] not found");
    }
    var allActionsWithWork: LazyJS.Sequence<types.SupportActionWithWork> = supportWork
        .filter(isNotNull)
        .map(sw => {
            return supportActionWithWorkFromWork(sw, Lazy(sw.actions));
        })
        .flatten<types.SupportActionWithWork>();

    return allActionsWithWork;
}

function groupByActionId(
    input: Sequence<types.SupportActionWithWork>
): Sequence<Group<types.SupportActionWithWork>> {
    return input
        .groupBy(saw => saw.supportAction.actionId.toString())
        .pairs()
        .map(extractPair);
}
function groupByServiceRecipientId(
    input: Sequence<types.SupportActionWithWork>
): Sequence<Group<types.SupportActionWithWork>> {
    return input
        .groupBy(saw => saw.supportWork.serviceRecipientId.toString())
        .pairs()
        .map(extractPair);
}
function hasRelevantAndAchieved(supportActions: evidenceDto.SupportAction[]): boolean {
    var hasRelevant = supportActions.filter(sa => sa.status == 1).length > 0;
    var hasAchieved = supportActions.filter(sa => sa.status == 3).length > 0;
    return hasRelevant && hasAchieved;
}

export class SmartStepCount {
    private totalAchievedSmartSteps: number = 0;
    private totalRelevantSmartSteps: number = 0;
    private totalOutstandingSmartSteps: number = 0;
    private percentSuccess: number = 0;

    constructor(private allActionsWithWorkUnclean: Sequence<types.SupportActionWithWork>) {
        var allActionsWithWork = allActionsWithWorkUnclean
            .filter(isNotNull)
            .filter(
                actionsWithWork =>
                    actionsWithWork.supportWork != null && actionsWithWork.supportAction != null
            );

        // NB outstanding = relevant - achieved works when the same client has relevant and achieved in this period
        // however, during the period, the clients who achieved things might not have the relevant smart step recorded
        // so if we have achieved = 1, relevant = 2, outstanding = 1 if the relevant '2' includes the client who achieved
        // so if we have achieved = 1, relevant = 2, outstanding = 2 if the relevant '2' doesn't include the client who achieved
        // therefore, we need to construct a numberOfSmartStepsOfReferralsWithRelevantAndAchieved
        // and assume achieved must have included relevant at some point in the past
        var numberOfSmartStepsOfReferralsWithRelevantAndAchieved = groupByServiceRecipientId(
            allActionsWithWork
        )
            .filter(isNotNull)
            .map(pair_rid =>
                groupByActionId(pair_rid.elements).filter(pair_aid =>
                    hasRelevantAndAchieved(
                        pair_aid.elements
                            .map(saw => saw.supportAction)
                            .flatten<evidenceDto.SupportAction>()
                            .toArray()
                    )
                )
            )
            .flatten<types.SupportActionWithWork>()
            .size();

        this.totalAchievedSmartSteps = allActionsWithWork
            .map(saw => saw.supportAction)
            .filter(isNotNull)
            .filter(sa => {
                return sa.status == 3;
            })
            .flatten<evidenceDto.SupportAction>()
            .size();

        this.totalRelevantSmartSteps = allActionsWithWork
            .map(saw => saw.supportAction)
            .filter(isNotNull)
            .filter(sa => {
                return sa.status == 1;
            })
            .flatten<evidenceDto.SupportAction>()
            .size();

        this.totalOutstandingSmartSteps =
            this.totalRelevantSmartSteps - numberOfSmartStepsOfReferralsWithRelevantAndAchieved;

        // NB for those with 'straightToAchieve' configured on evidence pages, totalOutstandingSmartSteps is still correct
        // because relevant is never recorded, so totalRelevantSmartSteps is 0, as is numberOfSmartStepsOfReferralsWithRelevantAndAchieved
        // and total achieved is still correct. The percentSuccess will be typically be 100%.

        this.percentSuccess =
            (this.totalAchievedSmartSteps /
                (this.totalOutstandingSmartSteps + this.totalAchievedSmartSteps)) *
            100;
    }

    public getTotalAchievedSmartSteps() {
        return this.totalAchievedSmartSteps;
    }
    public getTotalRelevantSmartSteps() {
        return this.totalRelevantSmartSteps;
    }
    public getTotalOutstandingSmartSteps() {
        return this.totalOutstandingSmartSteps;
    }
    public getPercentSuccess() {
        return this.percentSuccess;
    }
}
