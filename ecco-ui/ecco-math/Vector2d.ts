/** A two-dimensional vector; a point in two-dimensional Cartesian space.
 *
 * The documentation of this class assumes that the positive x-axis points
 * right and that the positive y-axis points down, which is the most useful
 * orientation for computer graphics. If the positive y-axis is treated as
 * pointing up, then the direction of angles is reversed. */
export class Vector2d {
    /** Constructs a new Vector2d with the specified x and y components. */
    constructor(private x: number, private y: number) {
    }

    /** Gets the value of the x component of the vector. */
    public getX(): number {
        return this.x;
    }

    /** Gets the value of the y component of the vector. */
    public getY(): number {
        return this.y;
    }

    /** Computes the sum of this Vector2d and the specified Vector2d values.
     *
     * If the array of values is empty, then the result has the same value as
     * this Vector2d. */
    public add(...values: Vector2d[]): Vector2d {
        let x = this.x;
        let y = this.y;

        for (let i = 0; i < values.length; ++i) {
            x += values[i].x;
            y += values[i].y;
        }

        return new Vector2d(x, y);
    }

    /** Subtracts the specified Vector2d value from this Vector2d value and
     * returns the resulting Vector2d value. */
    public subtract(v: Vector2d): Vector2d {
        return Vector2d.subtract(this, v);
    }

    /** Computes the dot product of this Vector2d value and the specified
     * Vector2d value, and returns the resulting scalar value. */
    public dot(v: Vector2d): number {
        return Vector2d.dot(this, v);
    }

    /** Computes the cross product of this Vector2d value and the specified
     * Vector2d value, and returns the resulting scalar value. */
    public cross(v: Vector2d): number {
        return Vector2d.cross(this, v);
    }

    /** Multiplies this Vector2d value by the specified scalar value, and
     * returns the resulting Vector2d value. */
    public scale(scalar: number): Vector2d {
        return Vector2d.scale(this, scalar);
    }

    /** Negates this Vector2d value and returns the resulting Vector2d value.
     *
     * This is equivalent to multiplying this Vector2d value by the scalar
     * value -1. */
    public negate(): Vector2d {
        return Vector2d.negate(this);
    }

    /** Compute the clockwise angle between the Vector2d value (0, -1) and this
     * Vector2d value, and returns the resulting angle in radians.
     *
     * The angle will be in the range -PI through PI. Positive angles are
     * clockwise, and negative angles are counterclockwise. */
    public angle(): number {
        return Vector2d.angle(this);
    }

    /** Measures the magnitude of this Vector2d value, and returns the
     * resulting scalar value. */
    public magnitude(): number {
        return Vector2d.magnitude(this);
    }

    /** Scales this Vector2d value such that the magnitude of the resulting
     * Vector2d value will be the specified magnitude, and returns the
     * resulting Vector2d value.
     *
     * If this Vector2d value to be scaled is (0, 0), then the result will be
     * the same Vector2d value (0, 0). */
    public scaleToMagnitude(v: Vector2d, magnitude: number): Vector2d {
        return Vector2d.scaleToMagnitude(this, magnitude);
    }

    /** Normalizes this Vector2d value and returns the resulting Vector2d
     * value.
     *
     * If this value to be normalized is the Vector2d value (0, 0), then the
     * result will be this same Vector2d value (0, 0).
     *
     * Otherwise, the result will be the Vector2d value that has a magnitude of
     * 1, and that points in the same direction as this Vector2d value. */
    public normalize(): Vector2d {
        return Vector2d.normalize(this);
    }

    /** Measures the distance between this Vector2d value and the specified
     * Vector2d value, and returns the resulting scalar value. */
    public distance(v: Vector2d): number {
        return Vector2d.distance(this, v);
    }

    /** Rotates this Vector2d value by the specified angle and returns the
     * resulting Vector2d value.
     *
     * The angle is in radians. Positive angles are clockwise and negative
     * angles are counterclockwise. */
    public rotate(angle: number): Vector2d {
        return Vector2d.rotate(this, angle);
    }

    /** Returns a string representation of this Vector2d value in the form
     * "(x, y)", where x and y are the numeric x and y components. */
    public toString(): string {
        return "(" + this.x + ", " + this.y + ")";
    }

    /** Computes the sum of the specified Vector2d values.
     *
     * If the array of values is empty, then the result is the vector (0, 0). */
    public static add(...values: Vector2d[]): Vector2d {
        let x = 0;
        let y = 0;

        for (let i = 0; i < values.length; ++i) {
            x += values[i].x;
            y += values[i].y;
        }

        return new Vector2d(x, y);
    }

    /** Subtracts w from v and returns the resulting Vector2d value. */
    public static subtract(v: Vector2d, w: Vector2d): Vector2d {
        return new Vector2d(v.x - w.x, v.y - w.y);
    }

    /** Computes the dot product of two Vector2d values, and returns the
     * resulting scalar value. */
    public static dot(v: Vector2d, w: Vector2d): number {
        return v.x * w.x + v.y * w.y;
    }

    /** Computes the cross product of two Vector2d values, and returns
     * the resulting scalar value. */
    public static cross(v: Vector2d, w: Vector2d): number {
        return v.x * w.y - v.y * w.x;
    }

    /** Multipies a Vector2d value by the specified scalar value, and returns
     * the resulting Vector2d value. */
    public static scale(v: Vector2d, scalar: number): Vector2d {
        return new Vector2d(v.x * scalar, v.y * scalar);
    }

    /** Negates the specified Vector2d value and returns the resulting Vector2d
     * value.
     *
     * This is equivalent to multiplying the specified Vector2d value by the
     * scalar value -1. */
    public static negate(v: Vector2d): Vector2d {
        return Vector2d.scale(v, -1);
    }

    /** Compute the clockwise angle between the Vector2d value (0, -1) and the
     * specified Vector2d value, and returns the resulting angle in radians.
     *
     * The angle will be in the range -PI through PI. Positive angles are
     * clockwise, and negative angles are counterclockwise. */
    public static angle(v: Vector2d): number {
        return Math.atan2(v.y, -v.x);
    }

    /** Measures the magnitude of the specified Vector2d value, and returns
     * the resulting scalar value. */
    public static magnitude(v: Vector2d): number {
        return Math.sqrt(v.x * v.x + v.y * v.y);
    }

    /** Scales the specified Vector2d value such that the magnitude of the
     * resulting Vector2d value is the specified magnitude, and returns the
     * resulting Vector2d value.
     *
     * If the Vector2d value to be scaled is (0, 0), then the result will be
     * the same Vector2d value (0, 0). */
    public static scaleToMagnitude(v: Vector2d, magnitude: number): Vector2d {
        if (v.x == 0 && v.y == 0) {
            return v;
        } else {
            const originalMagnitude = Vector2d.magnitude(v);
            return new Vector2d(v.x * magnitude / originalMagnitude, v.y * magnitude / originalMagnitude);
        }
    }

    /** Normalizes the specified Vector2d value and returns the resulting
     * Vector2d value.
     *
     * If the value to be normalized is the Vector2d value (0, 0), then the
     * result will be the same Vector2d value (0, 0).
     *
     * Otherwise, the result will be the Vector2d value that has a magnitude of
     * 1, and that points in the same direction as the specified Vector2d
     * value. */
    public static normalize(v: Vector2d): Vector2d {
        if (v.x == 0 && v.y == 0) {
            return v;
        } else {
            const m = Vector2d.magnitude(v);
            return new Vector2d(v.x / m, v.y / m);
        }
    }

    /** Measures the distance between the specified Vector2d values, and
     * returns the resulting scalar value. */
    public static distance(v: Vector2d, w: Vector2d): number {
        return Vector2d.subtract(v, w).magnitude();
    }

    /** Rotates the specified Vector2d value by the specified angle and returns
     * the resulting Vector2d value.
     *
     * The angle is in radians. Positive angles are clockwise and negative
     * angles are counterclockwise. */
    public static rotate(v: Vector2d, angle: number): Vector2d {
        const sin: number = Math.sin(angle);
        const cos: number = Math.cos(angle);
        return new Vector2d(cos * v.x - sin * v.y, cos * v.y + sin * v.x);
    }

    /** Constructs a new Vector2d value that is a unit vector with the
     * specified angle relative to the Vector2d value (0, -1).
     *
     * The angle is in radians. Positive angles are clockwise and negative
     * angles are counterclockwise. */
    public static unit(angle: number): Vector2d {
        return new Vector2d(Math.sin(angle), -Math.cos(angle));
    }

    /** Constructs a new Vector2d value from the specified polar coordinates.
     *
     * The angle is in radians and is relative to the direction of the Vector2d
     * value (0, -1). Positive angles are clockwise and negative angles are
     * counterclockwise. */
    public static polar(angle: number, magnitude: number): Vector2d {
        return new Vector2d(Math.sin(angle) * magnitude, -Math.cos(angle) * magnitude);
    }
}
