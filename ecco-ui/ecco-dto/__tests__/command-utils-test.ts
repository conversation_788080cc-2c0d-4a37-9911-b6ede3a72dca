import {asArrayChange} from "../command-utils";


describe('command-utils', () => {
    it("asArrayChange - add element", () => {
        const change = asArrayChange([1,2,3], [1,2,3,4])!;
        expect(change.from).toStrictEqual([1,2,3]);
        expect(change.to).toStrictEqual([1,2,3,4]);
    });

    it("asArrayChange - remove element", () => {
        const change = asArrayChange([1,2,3,6], [1,2,3])!;
        expect(change.from).toStrictEqual([1,2,3,6]);
        expect(change.to).toStrictEqual([1,2,3]);
    });

    it("asArrayChange - no change", () => {
        const change = asArrayChange([1,2,3], [1,2,3])!;
        expect(change).toBeUndefined();
    });

    it("asArrayChange - change order", () => {
        const change = asArrayChange([1,2,3], [1,3,2])!;
        expect(change.from).toStrictEqual([1,2,3]);
        expect(change.to).toStrictEqual([1,3,2]);
    });
});