/** Matches the view model ClientSalesInvoiceDetailResource.java */
import {HateoasResource} from "@eccosolutions/ecco-common";

export interface InvoiceLineDto extends HateoasResource {
    reverseCharge: boolean; // FIXME: This has no back end support

    /** The parent invoice that this is attached to when verified */
    invoiceId: number;
    /** true if the parent is final (i.e. "posted") */
    locked: boolean;

    /** If from uninvoiced work, this is present to allow grouping - and details lookup */
    serviceRecipientId: number;
    lineUuid: string;

    type: string;
    description: string;

    rateCardId: number;
    netAmount: number;
    taxRate: number;

    /** client-side reference back to parent */
    invoice?: InvoiceDto | undefined;

    // ROTA LINE
    workUuid?: string | undefined; // may not be recorded work
    eventId?: string | null | undefined;
    plannedDate?: string | undefined;
    plannedMinutes?: number | undefined;
    /** Worker or bed, or building that fulfills this - could have plannedResourceSRId */
    plannedResourceName?: string | undefined;
    plannedResourceCalendarId?: string | undefined;
    /** actual date of the work - if it has been recorded*/
    workDate?: string | undefined;
    /** actual minutes of the work */
    workMinutesSpent?: number | undefined;
    // ROTA LINE

    // SERVICE CHARGE LINE
    buildingId?: number | undefined;
    // localdatetime in UTC
    chargeFrom?: string | undefined;
    // localdatetime in UTC
    chargeTo?: string | undefined;
    // SERVICE CHARGE LINE
}

export enum InvoiceStatus {
    DRAFT = 'DRAFT',
    FINAL = 'FINAL',
    POSTED = 'POSTED' // FIXME: POSTED isn't a back end concept, so should either be introduced there or removed here (it's possible POSTED means FINAL)
}

/** Matches the view model ClientSalesInvoiceDetailResource.java */
export interface InvoiceDto extends HateoasResource<"proforma-lines" | "lines"> {
    invoiceId: number;
    serviceRecipientId: number;
    invoiceDate: string;

    /** POSTED / DRAFT */
    status: InvoiceStatus;

    /**
     * Total of invoice lines (not draft)
     * NB 'invoice.amount' is currently shown in InvoicesList, alongside properties of TempInvoiceDto
     */
    amount: number;

    /** Optional: as we won't have these in the list view */
    lines?: InvoiceLineDto[] | undefined;
}