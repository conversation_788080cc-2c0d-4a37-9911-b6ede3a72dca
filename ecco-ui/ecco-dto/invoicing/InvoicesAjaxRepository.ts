import {EccoDate} from "@eccosolutions/ecco-common";
import {InvoicesRepository} from "./InvoicesRepository";
import {InvoiceDto, InvoiceLineDto} from "./invoicing-dto";
import {ApiClient} from "../web-api";

export class InvoicesAjaxRepository implements InvoicesRepository {
    constructor(private apiClient: ApiClient) {}

    public findAllInvoices(startDate: EccoDate, endDate: EccoDate): Promise<InvoiceDto[]> {
        return this.apiClient.get<InvoiceDto[]>("rota/invoice/", {
            query: {
                startDate: startDate.formatIso8601(),
                endDate: endDate.formatIso8601()
            }
        });
    }

    // ignores incoming HATEOS 'links' for now
    public findOneInvoice(invoiceId: number): Promise<InvoiceDto> {
        return this.apiClient.get<InvoiceDto>(`rota/invoice/id/${invoiceId}/`);
    }

    // ignores incoming HATEOS 'links' for now
    public findAllInvoicesByServiceRecipientId(serviceRecipientId: number): Promise<InvoiceDto[]> {
        return this.apiClient.get<InvoiceDto[]>(
            `rota/invoice/serviceRecipient/${serviceRecipientId}/`
        );
    }

    findAllUninvoicedLines(
        startDate: EccoDate,
        endDate: EccoDate,
        buildingId: number | null
    ): Promise<InvoiceLineDto[]> {
        const query = {
            startDate: startDate.formatIso8601(),
            endDate: endDate.formatIso8601()
        };
        if (buildingId) {
            return this.apiClient.get<InvoiceLineDto[]>(
                `rota/invoice/uninvoiced/building/${buildingId}/`,
                {query: query}
            );
        } else {
            return this.apiClient.get<InvoiceLineDto[]>("rota/invoice/uninvoiced/", {query: query});
        }
    }

    // required because we are ignoring HATEOS links for now
    public findUninvoicedByServiceRecipientIdAndEndDate(
        serviceRecipientId: number,
        endDate: EccoDate
    ): Promise<InvoiceLineDto[]> {
        return this.apiClient.get<InvoiceLineDto[]>(
            `rota/invoice/uninvoiced/serviceRecipient/${serviceRecipientId}/`,
            {query: {endDate: endDate.formatIso8601()}}
        );
    }
}
