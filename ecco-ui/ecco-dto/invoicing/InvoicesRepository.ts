import {EccoDate} from "@eccosolutions/ecco-common";
import {InvoiceDto, InvoiceLineDto} from "./invoicing-dto";


export interface InvoicesRepository {
    findAllInvoices(startDate: EccoDate, endDate: EccoDate): Promise<InvoiceDto[]>;

    findOneInvoice(invoiceId: number): Promise<InvoiceDto>;

    findAllInvoicesByServiceRecipientId(serviceRecipientId: number): Promise<InvoiceDto[]>;

    findAllUninvoicedLines(
        startDate: EccoDate,
        endDate: EccoDate,
        buildingId: number | null
    ): Promise<InvoiceLineDto[]>;

    findUninvoicedByServiceRecipientIdAndEndDate(
        serviceRecipientId: number,
        endDate: EccoDate
    ): Promise<InvoiceLineDto[]>;
}
