import {IndividualRepository} from "./IndividualRepository";
import {PersonUserSummary} from "../contact-dto";
import {ApiClient} from "../web-api";

export class IndividualAjaxRepository implements IndividualRepository {
    constructor(private apiClient: ApiClient) {
    }

    findIndividualFromUsername(username: string): Promise<PersonUserSummary> {
        return this.apiClient.get<PersonUserSummary>(`individuals/filter/username/${username}/`);
    }

    findIndividualsWithAuthority(authority: string): Promise<PersonUserSummary[]> {
        return this.apiClient.get<PersonUserSummary[]>(`individuals/filter/authority/${authority}/`);
    }

}
