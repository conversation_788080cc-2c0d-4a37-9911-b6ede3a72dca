import {SparseArray} from "@eccosolutions/ecco-common";
import {Agency, Company} from "../contact-dto";
import {ApiClient} from "../web-api";

export class CompanyAjaxRepository {
    private companyCache: SparseArray<Promise<Company>> = {};
    private agencyCache: SparseArray<Promise<Agency>> = {};

    constructor(private apiClient: ApiClient) {}

    findOneCompany(companyId: number): Promise<Company> {
        var company = this.companyCache[companyId];
        if (!company) {
            company = this.apiClient.get<Company>(`company/${companyId}/`);
            this.companyCache[companyId] = company;
        }
        return company;
    }

    findOneAgency(agencyId: number): Promise<Agency> {
        var agency = this.agencyCache[agencyId];
        if (!agency) {
            agency = this.apiClient.get<Agency>(`agencies/${agencyId}/`);
            this.agencyCache[agencyId] = agency;
        }
        return agency;
    }
}
