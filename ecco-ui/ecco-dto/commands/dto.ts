import {CipherTextDto} from "@eccosolutions/ecco-crypto";

/** Data-transfer object representing a User. */
export interface UserEncryptionDto {
    /** A string that uniquely identifies the user. */
    username: string;

    /** A GUID that uniquely identifies the User-Device. */
    userDeviceId: string;

    /** The salt used in conunction with the user's password to derive the
     * user's Credentials Key. */
    credentialsKeySalt: string;

    /** The encrypted User-Device key */
    userDeviceKeyCipherMessage: CipherTextDto;
}

/** Data-transfer object representing a User-Device. Contains the
 * User-Device Key. */
export interface UserDeviceDto {
    /** A GUID that uniquely identifies the User-Device. */
    guid: string;

    /** The User-Device Key, Base64 encoded. */
    base64Key: string;

    /** A string identifying the cipher and cipher mode used to encrypt
     * information on this User-Device. */
    cipher: string;

    valid: boolean;
}



export class SecurePayloadDto {
    /**
     * @param guid User-Device uid
     * @param base64Payload encrypted JSON representation of payload
     */
    constructor(public guid: string, public base64Payload: string) {
    }
}

export class CommandRequestDto {
    constructor(
        public method: string,
        public url: string,
        public body: string,
        public contentType?: string | undefined,
        public acceptType?: string | undefined
    ) {
        if (!contentType) {
            this.contentType = "application/json";
        }
        if (!acceptType) {
            this.acceptType = "application/json";
        }
    }
}
