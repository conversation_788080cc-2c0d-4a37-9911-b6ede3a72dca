const username = process.env["REACT_APP_USERNAME"];
const password = process.env["REACT_APP_PASSWORD"];

if (typeof username === "string" && username !== "" && typeof password === "string") {
    window.global_credentials = {
        username,
        password
    };
}

const publicUrl = document.querySelector("link[rel=x-ecco-public-url]")?.getAttribute("href");

if (publicUrl == null) {
    throw new Error("Missing public URL");
}

// Default remote root is public URL with the tail removed from e.g. http://localhost:8888/ecco-war/r/app/rota?blah
const remoteRoot = process.env["REACT_APP_REMOTE_ROOT"] ?? publicUrl.split("/r/app")[0] ?? "";

console.assert(!remoteRoot.endsWith("/"));

const appPath = new URL(publicUrl.replace(/\/?$/, "/"), window.location.href).pathname;
const applicationRootPath = remoteRoot + "/";

window.applicationProperties = {
    applicationRootPath,
    remoteHost: undefined,
    resourceRootPath: "/ui/",
    isLoginProvidersOnly: false
};

import("./app").then(app => app.start({appPath, remoteRoot}));

export {};
