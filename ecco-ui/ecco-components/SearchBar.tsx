import {Container, InputAdornment, TextField} from "@eccosolutions/ecco-mui";
import {default as SearchIcon} from "@material-ui/icons/Search";
import * as React from "react";
import {useEffect, useState} from "react";
import {useDebounce} from "ecco-components-core";

export const SearchBar = (props: {
    searchText: string | null;
    onChange: (value: string | null) => void;
}) => {
    const [searchText, setSearchText] = useState<string | null>(props.searchText);

    // debounce for live updates
    let debouncedValue = useDebounce(searchText, 1000);
    // if debounced value changes, then save it
    useEffect(() => {
        props.onChange(debouncedValue);
    }, [debouncedValue]);

    return (
        <Container maxWidth="md" /*sx={{ mt: 20 }}*/>
            <TextField
                autoComplete={"google-broke-it"}
                id="audit-search"
                type="text"
                label=""
                value={searchText}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const val = event.target.value == "" ? null : event.target.value;
                    setSearchText(val);
                }}
                InputProps={{
                    endAdornment: (
                        <InputAdornment position="end">
                            <SearchIcon />
                        </InputAdornment>
                    )
                }}
            />
        </Container>
    );
};
