import * as React from "react";
import {useRef, useState, useEffect} from "react";
import {default as QrScanner} from "qr-scanner";

//import QrScanner from "qr-scanner";
//QrScanner.WORKER_PATH = "../qr-scanner-worker.min.js";

interface ScannerProps {
    triggerID?: string | undefined;
}

// see https://stackoverflow.com/a/74691867
export const QrCodeScan: React.FC<ScannerProps> = () => {
    // triggerID
    const video = useRef<HTMLVideoElement>(null);
    const [qrScanner, setQrScanner] = useState<QrScanner>();

    function handleScan(result: QrScanner.ScanResult) {
        alert(result);
    }

    useEffect(
        () => () => {
            qrScanner?.stop();
            qrScanner?.destroy();
            setQrScanner(undefined);
        },
        []
    );

    useEffect(() => {
        if (!qrScanner && video.current) {
            const qrScanner = new QrScanner(video.current, result => handleScan(result), {
                highlightScanRegion: true
            });
            qrScanner.start();
            setQrScanner(qrScanner);
        }
        // Dependency array missing handleScan, since it should not set Scanner on handleScan change
        // eslint-disable-next-line
    }, [qrScanner]);

    return <video style={{width: "100%", height: "auto"}} ref={video}></video>;
};
