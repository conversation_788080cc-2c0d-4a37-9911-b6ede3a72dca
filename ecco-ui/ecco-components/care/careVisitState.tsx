import {create<PERSON>ontext, Dispatch, useContext} from "react";
import {EccoDateTime, HateoasResource} from "@eccosolutions/ecco-common";
import {CommandQueue, CommandRepository, EvidenceCommand, MergeableCommand} from "ecco-commands";
import {CommentFormFields, SupportWork} from "ecco-dto";
import {AdditionalStaffData} from "./AdditionalStaff";
import {BaseServiceRecipientCommandDto} from "ecco-dto";

/*
 * For Adam's notes, see ../README.md for thoughts on state, hooks, defaults and testing using typescript
 */

// STATE
// =====
/**
 * CareVisit is designed to show what to do on a visit - a collection of items.
 *
 * RESIDENTIAL (appointment based): On the current rota, the appointment cards show the overarching reason (eg one-to-one) a time and location.
 * The card shows with a 'visit' which takes them to the support plan - but should take them here.
 *
 * BUILDING (shift based): CareScheduleSource generates a set of target dates from live clients which are the tasks to do.
 * When a CareSchedule is selected, we should come here.
 */

/**
 * See careSingleVisitDataLoader#compute for how these are determined,
 * and transformToCareVisit.
 */
export interface VisitProps {
    visitDateTime: string | undefined;
    plannedDateTime?: EccoDateTime | undefined;
    plannedMins?: number | undefined;
    displayName: string | undefined;
    address: string | undefined;
    // transformToCareVisit assigns this from schedule.title
    visitType: string | undefined;
    adHoc: boolean | undefined;
    aptTypeId: number | undefined;
    commentForm: Partial<CommentFormFields>;
    locationId: number | null;
    tasks: CareTask[] | null;
    serviceRecipientId: number;
    resources: HateoasResource | undefined;
}

export interface StopOptionData {
    stopReasonId: number | null
}

export interface CareVisitProps extends VisitProps {
    stopOptionsOn: boolean;
    stopOptions: Partial<StopOptionData>;
    eventStatusId: number | undefined;
    supportWorker: string | null | undefined;
    scheduleInfo: string | undefined;
    taskSummary: string | null;
    work: SupportWork | null;
    additionalStaff: AdditionalStaffData | undefined;
    tasks: CareTask[] | null;
    workLoad: () => Promise<SupportWork | null>;
    tasksLoad: () => Promise<CareTask[]>;
    previousVisitHistoryLoad: (page: number) => Promise<BaseServiceRecipientCommandDto[]>;
    commentUpdateCmd: (
        state: CareVisitState & {timerStartedAt: EccoDateTime; timerStoppedAt: EccoDateTime}
    ) => EvidenceCommand;
    // see careSingleVisitDataLoader which uses 'task' to get the taskInstanceId, actionDefId and listDefId
    taskUpdateCmd: (
        task: CareTask,
        outcomeId: number | null,
        plannedDateTime: EccoDateTime | null
    ) => MergeableCommand;
    loneWorkerUpdate: (state: CareVisitState) => Promise<void>;
}

export interface CareVisitState extends CareVisitProps {
    cmdRepo: CommandRepository;
    cmdQueue: CommandQueue;
    showClientDetails?: boolean | undefined;
    showClientFile?: boolean | undefined;
    showStopOptions?: boolean | undefined;
    showVisit?: boolean | undefined;
    showCommentForm?: boolean | undefined;
    loneWorkerUpdateFailed: boolean;
    timerStartedAt: EccoDateTime | undefined;
    timerStoppedAt: EccoDateTime | undefined;
}

export interface Task {
    taskInstanceId: string,
    taskText: string,
    taskDescription: string | null;
    taskTime: EccoDateTime | null
}

/**
 * CareTask are properties relating to the type
 */
export interface CareTask extends Task {
    listDefId: number | null;
    actionDefId: number;
}


// CONTEXT
// =======

/** Reducer actions */
export type CareVisitActionType =
    | {type: "hideVisit"}
    | {type: "commandFailed"; reason: any}
    | {type: "start"; dispatch: Dispatch<CareVisitActionType>}
    | {type: "stop"; dispatch: Dispatch<CareVisitActionType>}
    | {type: "stopWithOptions"; stopReasonId: number; dispatch: Dispatch<CareVisitActionType>}
    | {type: "showStopOptions"; show: boolean}
    | {type: "loneWorkerUpdateFailed"} // probably redundant now we've got a snackbar message
    | {type: "showVisit"; dispatch: Dispatch<CareVisitActionType>} // dispatch so we can allow the reducer to dispatch an event after async action
    | {type: "setState"; setter: (state: CareVisitState) => CareVisitState}
    | {type: "showVisitComment"; show: boolean}
    | {type: "changeVisitComment"; commentForm: Partial<CommentFormFields>}
    | {type: "changeVisitLocation"; locationId: number | null}
    | {type: "showTask"; taskInstanceId: string}
    | {
          type: "changeTaskOutcome";
          task: CareTask;
          value: number | null;
          dispatch: Dispatch<CareVisitActionType>;
      } // dispatch so we can allow the reducer to dispatch an event after async action
    | {type: "showClientDetails"}
    | {type: "hideClientDetails"}
    | {type: "showClientFile"}
    | {type: "hideClientFile"}
    | {type: "loadPreviousVisitHistoryPage"; dispatch: Dispatch<CareVisitActionType>}; // dispatch so we can allow the reducer to dispatch an event after async action

/** context for the useReducer to pass through */
export interface CareVisitContextProps {
    state: CareVisitState;
    dispatch: Dispatch<CareVisitActionType>;
}

// "The defaultValue argument is only used when a component does not have a matching Provider above it in the tree. This can be helpful for testing components in isolation without wrapping them. Note: passing undefined as a Provider value does not cause consuming components to use defaultValue."
export const CareVisitContext = createContext<CareVisitContextProps>({} as CareVisitContextProps);
//  using a fn is a 'little trick' to provide smaller consuming code https://medium.com/simply/state-management-with-react-hooks-and-context-api-at-10-lines-of-code-baf6be8302c
//  and allows a bit of error checking
//  consume by: const [{ state }, dispatch] = useCareVisitContext();
export const useCareVisitContext = () => {
    const ctx = useContext(CareVisitContext);
    if (!ctx) {
        throw new Error('You probably forgot to put CareVisitContext.Provider somewhere');
    }
    return ctx;
};