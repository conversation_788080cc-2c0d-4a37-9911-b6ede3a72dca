import * as React from "react";
import {FC, useEffect, useState} from "react";
import {<PERSON>, <PERSON><PERSON>, Card, CardActions, CardContent, Typography} from "@eccosolutions/ecco-mui";
import {EccoV3Modal} from "ecco-components-core";

function sendSkipWaiting() {
    console.debug("Attempting to skip waiting")
    navigator.serviceWorker.getRegistration().then(registration => {
        if (registration && registration.waiting) {
            registration.waiting.postMessage('SKIP_WAITING');
        }
    })
}

export const AppUpdateInstructions: FC<{onClose: () => void}> = ({onClose}) =>
    <EccoV3Modal
        title="update available"
        maxWidth="sm" action="none" onCancel={onClose}
        show={true}
    >
        <Typography gutterBottom={true}>The latest version of ECCO is downloaded and ready to use.</Typography>
        <Typography gutterBottom={true}>To start using the update, close your browser and re-open it.</Typography>
        <Typography>On a mobile device close the application and re-open it.</Typography>
    </EccoV3Modal>

/** Hook that returns true when it is known that an update is ready.
 * TODO: This can also be extended to pick up the installed event after the component is mounted.
 */
export function useHasServiceWorkerUpdate() {
    const [hasUpdate, setHasUpdate] = useState(false);

    useEffect(() => {
            navigator.serviceWorker.ready.then(registration => {
                if (registration.waiting) {
                    setHasUpdate(true);
                }
            })
        },
        [])
    return hasUpdate;
}

export function ServiceWorkerCheck() {
    const hasUpdate = useHasServiceWorkerUpdate();

    return hasUpdate ? <Box m={2}>
            <Card>
                <CardContent>
                    An application update is available. You can update immediately.
                </CardContent>
                <CardActions>
                    <Button
                        variant="contained" color="primary" size="small"
                        onClick={sendSkipWaiting}
                    >update now</Button>
                </CardActions>
            </Card>
        </Box>
        : null;
}

