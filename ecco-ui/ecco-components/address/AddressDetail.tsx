import * as React from "react"
import {FC, useRef, useState} from "react"
import {
    Address,
    AddressedLocationAjaxRepository,
    AddressedLocationRepository,
    getGlobalApiClient
} from "ecco-dto";
import {formatPostcode, isValidPostcode} from "./validation";
import {EccoV3Modal, createTextInput} from "ecco-components-core";

import {Grid} from "@eccosolutions/ecco-mui";

interface ModalProps {
    address?: Address | undefined;
    onSave: (addressLocationId: number) => void;
    onCancel: () => void;
}

interface Props {
    address?: Address | undefined;
    onValid?: ((isValid: boolean) => void) | undefined;
}

interface State {
    address: Address;
    errors: any;
}

export class AddressValidation {
    public static getNewAddress(): Address {
        const adr: Partial<Address> = {
            address: []
        };
        return adr as Address;
    }

    public static validate(address: Address | null | undefined) {
        const errors: {[P in keyof Address]?: string} = {};
        if (!address || !address.hasOwnProperty("address")) {
            errors.address = "required";
        } else {
            if (!address.address[0]) errors.address = "required";
            const errorsPostCode = AddressValidation.validatePostCode(address.postcode);
            if (errorsPostCode.postcode) {
                errors.postcode = errorsPostCode.postcode;
            }
        }
        return errors;
    }

    public static validatePostCode(postCode: string) {
        const errors: {[P in keyof Address]?: string} = {};
        if (!postCode) {
            errors.postcode = "required";
        } else if (!isValidPostcode(postCode)) {
            errors.postcode = "should be a valid postcode";
        }
        return errors;
    }

    public static isValid(errors: Object): boolean {
        return Object.keys(errors).length == 0;
    }
}

export const AddressRenderer: FC<{
    address: Address | undefined;
    errors: any;
    stateSetter: (address: Address) => void;
}> = ({address, errors, stateSetter}) => {
    const size = "small"; // may want to expose as prop
    if (!address) {
        return null;
    }
    return (
        <Grid container direction="row" justify="flex-start" alignItems="flex-start">
            <Grid item xs={12}>
                {createTextInput(
                    "line1",
                    "address",
                    address.address[0],
                    val =>
                        stateSetter({
                            ...address,
                            address: [val || "", address.address[1], address.address[2]]
                        }),
                    "text",
                    {
                        placeholder: "n.b. include street name here",
                        maxLength: 31,
                        size,
                        required: true
                    },
                    () => errors.address && "error"
                )}
            </Grid>
            <Grid item xs={12}>
                {createTextInput(
                    "line2",
                    "line2",
                    address.address[1],
                    val =>
                        stateSetter({
                            ...address,
                            address: [address.address[0], val || "", address.address[2]]
                        }),
                    "text",
                    {placeholder: "", maxLength: 31, size}
                )}
            </Grid>
            <Grid item xs={12}>
                {createTextInput(
                    "line3",
                    "line3",
                    address.address[2],
                    val =>
                        stateSetter({
                            ...address,
                            address: [address.address[0], address.address[1], val || ""]
                        }),
                    "text",
                    {placeholder: "", maxLength: 31, size}
                )}
            </Grid>
            <Grid item xs={12}>
                {createTextInput(
                    "town",
                    "town",
                    address.town,
                    val => stateSetter({...address, town: val}),
                    "text",
                    {placeholder: "", maxLength: 63, size},
                    () => errors.town && "error"
                )}
            </Grid>
            <Grid item xs={12}>
                {createTextInput(
                    "postcode",
                    "post code",
                    address.postcode,
                    val => stateSetter({...address, postcode: formatPostcode(val)}),
                    "text",
                    {
                        placeholder: "enter a full UK postcode",
                        maxLength: 10,
                        size,
                        required: true
                    },
                    () => errors.postcode && "error"
                )}
            </Grid>
            <Grid item xs={12}>
                {createTextInput(
                    "county",
                    "county",
                    address.county,
                    val => stateSetter({...address, county: val}),
                    "text",
                    {placeholder: "", maxLength: 63, size},
                    () => errors.county && "error"
                )}
            </Grid>
        </Grid>
    );
};

export const AddressDetailModal: FC<ModalProps> = ({address, onSave, onCancel}) => {
    const ref = useRef<AddressDetail>(null);
    const [saveEnabled, setSaveEnabled] = useState(false);

    return <EccoV3Modal
        title="new address"
        action="save"
        show={true}
        onCancel={onCancel}
        onSave={() => {
            setSaveEnabled(false)
            return ref.current!.save().then(id => id && onSave(id))
        }}
        saveEnabled={saveEnabled}
    >
        <AddressDetail address={address} ref={ref} onValid={setSaveEnabled}/>
    </EccoV3Modal>;
}

export class AddressDetail extends React.Component<Props, State> {
    private repository: AddressedLocationRepository;

    constructor(props: Props) {
        super(props);

        const apiClient = getGlobalApiClient(); // should be set in ServicesContextProvider as that provides the apiClient that we load the parent resources from
        this.repository = new AddressedLocationAjaxRepository(apiClient);

        const address = this.props.address || AddressValidation.getNewAddress();
        const errors = AddressValidation.validate(address);
        const valid = AddressValidation.isValid(errors);

        this.props.onValid && this.props.onValid(valid);

        this.state = {
            address: address,
            errors: errors
        };
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        if (this.props.address != nextProps.address) {
            const address = nextProps.address || AddressValidation.getNewAddress();
            const errors = AddressValidation.validate(address);

            this.setState({
                address: address,
                errors: errors
            });
        }
    }

    // private handleAddressChange(updater: (event: any) => UpdateSpec<Address>, val: string | null) {
    //     const addressQuery: UpdateSpec<Address> = updater(val);
    //     const address: Address = update(this.state.address, addressQuery);

    private handleAddressChange(address: Address, prevErrors: any) {
        const errors = AddressValidation.validate(address);
        const valid = AddressValidation.isValid(errors);

        if (valid != AddressValidation.isValid(prevErrors)) {
            this.props.onValid && this.props.onValid(valid);
        }

        this.setState({
            address: address,
            errors: errors
        });
    }

    /*private handleAddress0Change: (val: string | null) => void = (val: string | null) =>
        this.handleAddressChange(v => ({address: {0: {$set: v}}}), val);
    private handleAddress1Change: (val: string | null) => void = (val: string | null) =>
        this.handleAddressChange(v => ({address: {1: {$set: v}}}), val);
    private handleAddress2Change: (val: string | null) => void = (val: string | null) =>
        this.handleAddressChange(v => ({address: {2: {$set: v}}}), val);
    private handleTownChange: (val: string | null) => void = (val: string | null) =>
        this.handleAddressChange(v => ({town: {$set: v}}), val);
    private handleCountyChange: (val: string | null) => void = (val: string | null) =>
        this.handleAddressChange(v => ({county: {$set: v}}), val);
    private handlePostCodeChange: (val: string | null) => void = (val: string | null) =>
        this.handleAddressChange(v => ({postcode: {$set: formatPostcode(v)}}), val);*/

    public save(): Promise<number | null> {
        // TODO: Replace with CommandSource.emitChangesTo as done with smart steps etc
        return AddressValidation.isValid(this.state.errors)
            ? this.repository
                  .saveAddress(this.state.address)
                  .then((response: {id: number}) => response.id)
            : Promise.resolve(null); // return null addressId if no address to save
    }

    override render() {
        const errorsRender: any = this.state.errors;
        const stateRender: Address = this.state.address;
        return (
            <AddressRenderer
                address={stateRender}
                errors={errorsRender}
                stateSetter={a => this.handleAddressChange(a, errorsRender)}
            />
        );
    }
}
