import {sequentialMapAll} from "ecco-commands";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {Moment} from "moment";
import {isOffline} from "ecco-dto";

// possibly more useful in 'mapUtils' file

export class Link {
    constructor(public text: string, public href: string) {}
}

export function fromIso8601OrMoment(time: Moment | string) {
    return typeof time == "string"
        ? EccoDateTime.parseIso8601(time as string)
        : EccoDateTime.fromMoment(time as Moment);
}

// also see ClientReferralsPopup#openReferral
export function adjustHrefIfOffline(href: string) {
    if (isOffline()) {
        href = href.replace("/nav/", "/offline/");
        // blah/nav/secure/referralFlow.html?referralId=3 -> blah/offline/referrals/3/
        href = href.replace(
            /nav\/secure\/referralFlow\.html\?referralId=(\d+)/,
            "offline/referrals/$1/"
        );
    }
    return href;
}


//const unique = (value, index, array) => array.indexOf(value) === index;

// from https://stackoverflow.com/questions/7273668/how-to-split-a-long-array-into-smaller-arrays-with-javascript
export function chunkArray<T>(arr: T[], size: number): T[][] {
    return arr.length > size
            ? [arr.slice(0, size), ...chunkArray(arr.slice(size), size)]
            : [arr];
}


// *******
// UTILS

// from https://stackoverflow.com/a/67607340
/*
export const groupBy = <T, K extends keyof T>(value: T[], key: K) =>
        value.reduce((acc, curr) => {
            if (acc.get(curr[key])) return acc;
            acc.set(curr[key], value.filter(elem => elem[key] === curr[key]));
            return acc;
        }, new Map<T[K], T[]>());
*/

// also see collections/MapReduce
export function groupBy<K, V>(array: V[], grouper: (item: V) => K) {
    return array.reduce((store, item) => {
        var key = grouper(item)
        if (!store.has(key)) {
            store.set(key, [item])
        } else {
            store.get(key)!.push(item)
        }
        return store
    }, new Map<K, V[]>())
}
export function transformMap<K, V, R>(
        source: Map<K, V>,
        transformer: (value: V, key: K) => R) {
    return new Map(
            Array.from(source, v => [v[0], transformer(v[1], v[0])])
    )
}
export function groupByAndMap<T, K, R>(
        array: T[],
        grouper: (x: T) => K,
        mapper: (x: T[]) => R) {
    let groups = groupBy(array, grouper)
    return transformMap(groups, values => mapper(values))
}
// *******

export function processLinear<T, R>(keys: T[], singlePromise: (id: T) => Promise<R[]>) {
    return sequentialMapAll(keys, singlePromise).then(data => {
        return data && data.reduce((r, x) => r.concat(x), []); // flatMap
    });
}
/**
 * Break up loading a large number of ids into smaller ones
 */
export function loadChunks<T, R>(keys: T[], singlePromise: (ids: T[]) => Promise<R[]>, chunkSize = 10) {
    const chunkedIds = chunkArray(keys, chunkSize);
    return sequentialMapAll(chunkedIds, singlePromise).then(data => {
        return data.reduce((r, x) => r.concat(x), []); // flatMap
    });
}

/**
 * For an entity, load a specific property and, usually, assign back to the object
 */
export function loadChunksAndAssign<T>(
        data: T[],
        dataKey: keyof T,
        singlePromise: (ids: any[]) => Promise<void>
) {
    const dataGrouped = groupBy(data, item => item[dataKey]);
    const chunkedIds = chunkArray(Array.from(dataGrouped.keys()), 10);
    return sequentialMapAll(chunkedIds, singlePromise).then(() => data);
}
