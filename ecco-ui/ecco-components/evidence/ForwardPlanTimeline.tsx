import * as React from "react";
import {FC, useMemo} from "react";
import {
    <PERSON>,
    CardHeader,
    Container,
    Timeline,
    TimelineConnector,
    TimelineContent,
    TimelineDot,
    TimelineItem,
    TimelineOppositeContent,
    TimelineSeparator,
    Typography
} from "@eccosolutions/ecco-mui";
import {getSupportSmartStepsSnapshotRepository} from "ecco-offline-data";
import {expandByScheduleTime} from "ecco-rota";
import {usePromises} from "../data/entityLoadHooks";
import {LoadingOrError} from "../Loading";
import {useParams} from "react-router";
import {SupportAction} from "ecco-dto";
import {taskDueClass, taskDueStatus} from "../tasks/TaskUtils";
import {EccoDateTime} from "@eccosolutions/ecco-common";

const describeDueTime = (action: SupportAction) => {
    const dueStatus = taskDueStatus(action.targetDateTime);
    const dueClass = taskDueClass(dueStatus);
    //console.log(`due date: ${task.dueDate} with status ${dueStatus} and class ${dueClass}`)
    const dte = action.targetDateTime
        ? EccoDateTime.parseIso8601(action.targetDateTime).toEccoDate()
        : null;
    return <span className={dueClass || ""}>{dte?.formatPretty()}</span>;
};

/**
 * Forward plan as of now (date and time)
 */
export const ForwardPlanTimeline: FC<{srId?: number | undefined}> = props => {
    let params = useParams<{srId: string}>();
    const srId = props.srId ?? parseInt(params.srId);

    const getSnapshotPromise = () =>
        getSupportSmartStepsSnapshotRepository().findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
            srId,
            "needs"
        );

    const {resolved, error} = usePromises([getSnapshotPromise], [srId]);

    // double up on items where there is a schedule
    const data = useMemo<SupportAction[]>(() => {
        if (resolved) {
            const [snapshot] = resolved;
            return expandByScheduleTime(snapshot.latestActions);
        } else {
            return [];
        }
    }, [srId, resolved]);

    if (!resolved) return <LoadingOrError error={error} />;

    return (
        <Container maxWidth="sm">
            {data.length == 0 ? (
                <Card>
                    <CardHeader
                        style={{textAlign: "center"}}
                        title="no target dates to show on the forward plan"
                    />
                </Card>
            ) : (
                <Timeline align="alternate">
                    {data.map(action => (
                        <TimelineItem>
                            <TimelineOppositeContent></TimelineOppositeContent>
                            <TimelineSeparator>
                                <TimelineConnector />
                                <TimelineDot>{/*<FastfoodIcon />*/}</TimelineDot>
                                <TimelineConnector />
                            </TimelineSeparator>
                            <TimelineContent>
                                <Typography variant={"body2"}>{describeDueTime(action)}</Typography>
                                <Typography variant="h6" component="span">
                                    {action.name || action.actionId}
                                </Typography>
                                <Typography>{action.goalName}</Typography>
                            </TimelineContent>
                        </TimelineItem>
                    ))}
                </Timeline>
            )}
        </Container>
    );
};
