import * as React from "react";
import {mount} from "enzyme";
import {ReactNode} from "react";
import {LoadingOrError} from "../Loading";
import {usePromise} from "../data/entityLoadHooks";
import {ReferralSummaryDto} from "ecco-dto";

export const useReferral = (srId: number) => {
    const {resolved, loading, error, reload} = usePromise(
        () => Promise.resolve({referralCode: "bob"} as ReferralSummaryDto),
        [srId]
    );
    return {referral: resolved, loading, error, reload};
};

function TestReferral(props: {srId?: number | undefined; children: ReactNode}) {
    const {referral, error} = useReferral(props.srId!);
    if (!referral) return <LoadingOrError error={error} />;
    return <div className="resolved">{referral?.referralCode}</div>;
}

describe("useReferral", () => {
    it("should be defined", () => {
        expect(TestReferral).toBeDefined();
    });
    it("should render Loading", () => {
        const callback = jest.fn();

        const tree = mount(<TestReferral srId={11}>{callback}</TestReferral>);
        // console.log(tree.text());

        // const loading = tree.children(LoadingSpinner);
        // console.log(loading);
        expect(tree.find(".resolved").exists()).toBe(false);
        expect(tree.find(".fa-spinner").exists()).toBe(true);
        expect(callback).toBeCalledTimes(0); // Zero because we're loading
    });
    it.skip("this should test resolved value - we've removed initial value", () => {
        const tree = mount(
            <TestReferral>
                <div className="content">Stuff that's visible when we've loaded</div>
            </TestReferral>
        );
        // console.log(tree.text());

        expect(tree.find(".fa-spinner").exists()).toBe(false);
        expect(tree.find(".resolved").exists()).toBe(true);
        // TODO: Investigate - we may still ahve a bug: expect(tree.find(".content").exists()).toBe(true);

        // expect(tree).toMatchSnapshot(); // Can't do as snapshot contains timestamps
    });
});
