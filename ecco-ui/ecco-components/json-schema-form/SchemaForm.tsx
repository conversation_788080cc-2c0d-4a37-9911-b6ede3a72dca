import {FormDefinition} from "ecco-dto/form-definition-dto";
import {
    AjvError,
    FieldProps,
    IChangeEvent,
    Markdown,
    SchemaForm as JsonSchemaForm
} from "ecco-forms";
import update from "immutability-helper";
import * as React from "react";
import "./SchemaForm.css";
import {JSONSchema7} from "json-schema";
import {Grid} from "@eccosolutions/ecco-mui";
import {FormEvidence} from "ecco-dto";
import {LoadingSpinner} from "../Loading";

export const SimpleSchemaFormRenderer = (
    props: SchemaFormRendererProps<FormEvidence<CustomFormFields>>
) => {
    if (!props.formDefinition) return <LoadingSpinner />;
    return (
        <>
            {props.formDefinition.definition.schema.schema.properties &&
                Object.entries(props.formDefinition.definition.schema.schema.properties).map(
                    ([key, value]) => (
                        <Grid container spacing={1} key={key}>
                            <Grid item md={3} sm={4} xs={5}>
                                <strong>{(value as JSONSchema7).title}</strong>
                            </Grid>
                            <Grid item md sm xs>
                                {props.formData && props.formData.form[key]}
                            </Grid>
                        </Grid>
                    )
                )}
        </>
    );
};

/* EXAMPLE form definition entry
update cfg_form_definitions set body = "
{
  "meta": {
    "label": "json-schema-form-layout",
    "version": "1"
  },
  "schema": {
    "schema": {
      "definitions": {
        "medication": {
          "type": "object",
          "title": "",
          "properties": {
            "name": {
              "type": "string"
            },
            "startDate": {
              "type": "string",
              "title": "start"
            },
            "endDate": {
              "type": "string",
              "title": "end"
            },
            "takeFor": {
              "type": "string",
              "title": "take this for"
            },
            "takeWhen": {
              "type": "string",
              "title": "when I take"
            },
            "takeHow": {
              "type": "string",
              "title": "how I take"
            }
          }
        }
      },
      "title": "",
      "type": "object",
      "properties": {
        "medication": {
          "type": "array",
          "title": "",
          "items": {
            "$ref": "#/definitions/medication"
          }
        },
        "mytext": {
          "type": "object",
          "properties": {}
        }
      }
    },
    "uiSchema": {
      "ui:rootFieldId": "medicationForm",
      "medication": {
        "items": {
          "name": {},
          "startDate": {
            "ui:widget": "date"
          },
          "endDate": {
            "ui:widget": "date"
          },
          "takeFor": {
            "ui:widget": "textarea",
            "ui:options": {
              "rows": "2"
            }
          },
          "takeWhen": {
            "ui:widget": "textarea",
            "ui:options": {
              "rows": "2"
            }
          },
          "takeHow": {
            "ui:widget": "textarea",
            "ui:options": {
              "rows": "2"
            }
          },
          "ui:field": "layout",
          "ui:layout": [
            {
              "name": {
                "md": "12"
              }
            },
            {
              "startDate": {
                "md": "4"
              },
              "endDate": {
                "md": "4"
              }
            },
            {
              "takeFor": {
                "md": "4"
              },
              "takeWhen": {
                "md": "4"
              },
              "takeHow": {
                "md": "4"
              }
            }
          ]
        }
      },
      "mytext": {
        "ui:field": "markdownBlock",
        "md": "this is my html - from the database"
      }
    }
  }
}
";
*/

export interface SchemaFormRendererProps<FORM_DATA> {
    formData: FORM_DATA | null;
    /** Should show loading spinner until formDefinition is populated  */
    formDefinition: FormDefinition | null;
}

interface SchemaFormProps<FORM_DATA> extends SchemaFormRendererProps<FORM_DATA> {
    readOnly: boolean;
    onChange: (formData: FORM_DATA, hasErrors: boolean) => void;
    resetData: boolean;
}

interface State<FORM_DATA> {
    formData: FORM_DATA;
}

/*interface ArrayElement {
    index: number
    children: any
    hasMoveDown: boolean
    hasMoveUp: boolean
    onReorderClick: (i: number, j: number) => void
}

interface ArrayFieldProps {
    className: string
    items: ArrayElement[]
    disabled: boolean
    readOnly: boolean
}*/

// NB we've experimented with objectFieldTemplate also in the past - see MyHealthProfile log, around DEV-293
// from https://github.com/mozilla-services/react-jsonschema-form/blob/master/playground/samples/customObject.js
// see https://github.com/mozilla-services/react-jsonschema-form Object Field Template
// demo'd at https://mozilla-services.github.io/react-jsonschema-form/ 'Custom Object'
/*const objectFieldTemplate = ({ TitleField, properties, title, description }) => {
    return (
        <div>
            <TitleField title={title} />
            <div className="row">
                {properties.map(prop => (
                    <div
                        className="col-lg-2 col-md-4 col-sm-6 col-xs-12"
                        key={prop.content.key}>
                        {prop.content}
                    </div>
                ))}
            </div>
            {description}
        </div>
    );
};*/

interface MarkdownState {
    md: string | null;
}
interface MarkdownProps {
    md: string | null;
}

function renderErrorMessage() {
    // Below was $("div.panel-danger").find("h3.panel-title").text("please complete required questions");
    // Tested by modifying https://jsfiddle.net/sanjayarakeri/3fofjLat/ to ensure this works
    const nodelist = document.querySelectorAll("div.panel-danger h3.panel-title");
    nodelist.forEach((node: Element) => {
        node.textContent = "please complete required questions";
    });
}

/**
 * Render some text, taken from the idea in https://github.com/mozilla-services/react-jsonschema-form/issues/647
 * Note that react-jsonschema-form-extensions has a key feature of 'add non-form elements in between' but the schema
 * definition includes code - and we are not going to 'exec' something from the database even if we could.
 */
class MarkdownBlock extends React.Component<FieldProps, MarkdownState> {
    constructor(props: FieldProps) {
        super(props);
        const mdProps = this.props.uiSchema as MarkdownProps;
        this.state = {
            md: mdProps.md || null
        };
    }

    override render() {
        return (
            <div>
                <Markdown>{this.state.md || ""}</Markdown>
            </div>
        );
    }
}

// A widget can override the default representation of the 'select' etc
// A widget represents a HTML tag for the user to enter data, eg. input, select, etc.
// So we DON'T want to use a widget, since a pic is non-input stuff... like markdown
/*export const PictureWidget: Widget = props => {

    return <>
        <div>hi: something here</div>
    </>
}*/

/** A faked Record<string, object> that pretends to have _isCustomForm: true present, so that we can detect if we've
 * assigned some other value to what is effectively 'object' */
export type CustomFormFields = {_isCustomForm: true} & Record<string, object | string | number | boolean> // So we can pretend this is the type for sanity's sake

export class SchemaForm<FORM_DATA extends CustomFormFields = CustomFormFields> extends React.Component<
    SchemaFormProps<FORM_DATA>,
    State<FORM_DATA>
> {
    constructor(props: SchemaFormProps<FORM_DATA>) {
        super(props);

        if (this.props.formDefinition && this.props.readOnly) {
            this.props.formDefinition.definition.schema.uiSchema["ui:disabled"] = true;
        }
        this.state = {
            formData: this.props.formData ? {...this.props.formData} : ({} as FORM_DATA)
        };
    }

    // capture initial render
    override componentDidMount() {
        renderErrorMessage();
    }
    // capture later renders
    override componentDidUpdate(prevProps: SchemaFormProps<FORM_DATA>) {
        renderErrorMessage();
        if (this.props.resetData && this.props.resetData !== prevProps.resetData) {
            this.setState({formData: {} as FORM_DATA});
        }
        if (this.props.formData !== prevProps.formData) {
            this.setState({
                formData: this.props.formData ? {...this.props.formData} : ({} as FORM_DATA)
            });
        }
    }

    override render() {
        const schemaFields = {
            markdownBlock: MarkdownBlock
        };

        // To change the date widget (eg d/m/y), we could use native, with some other options:
        //      https://github.com/rjsf-team/react-jsonschema-form/blob/561dff16f0033f3c8fa996b87cab8bd3f7dc53d6/packages/core/docs/form-customization.md
        // but otherwise we need to override the widget with a different 'dateElementProps':
        //      https://github.com/rjsf-team/react-jsonschema-form/blob/ef8b7fce19c5271f75a849fa951ad62595314b00/packages/core/src/components/widgets/AltDateWidget.js
        const schemaWidgets = {
            //DateWidget: DateLocaleWidget
            //PictureWidget: PictureWidget
        };

        const CustomForm = this.props.formDefinition ? (
            <JsonSchemaForm
                schema={this.props.formDefinition.definition.schema.schema}
                uiSchema={this.props.formDefinition.definition.schema.uiSchema}
                formData={this.state.formData}
                liveValidate={true}
                fields={schemaFields}
                widgets={schemaWidgets}
                // onSubmit not triggered since we hide the button
                // "WARNING: If you have situations where your parent component can re-render, make sure you listen to the onChange event and update the data you pass to the formData attribute."
                onChange={(data: IChangeEvent<FORM_DATA>) => {
                    //console.log("changed %o", data);
                    this.handleChangeForm(data.formData, data.errors);
                }}
                // onError not called currently
            >
                <button
                    type="submit"
                    style={{display: "none"}}
                    className="btn btn-primary pull-right"
                >
                    save
                </button>
            </JsonSchemaForm>
        ) : null;

        return (
            <div
                // adjust style because -25 margin on BS inline inputs in JsonSchemaForm crops things
                style={{paddingLeft: 5}}
            >
                {CustomForm}
            </div>
        );

        /* NOTE: the order of this in the JSX is important.
         * See https://facebook.github.io/react/docs/transferring-props.html */
    }

    /**
     * A more concise approach to the below - this has no typing because we don't need any here.
     */
    private handleChangeForm(data: FORM_DATA, errors: AjvError[]) {
        this.setState(prevState => update(prevState, {formData: {$set: data}}));
        this.bubbleUp(data, errors);
    }

    private bubbleUp(data: FORM_DATA, errors: AjvError[]) {
        // strip out root properties that are 'undefined'
        // as they don't play well with the client side json 'diff'
        const cleanedData = {} as FORM_DATA;
        for (let key in data) {
            if (data.hasOwnProperty(key)) {
                if (data[key] !== undefined) {
                    cleanedData[key] = data[key];
                }
            }
        }

        this.props.onChange(cleanedData, errors.length > 0);
    }

    /**
     * See ReferralWizard.handleReferralEntityChange and its setReferralAgencyUpdater
     * for a more type safe way of updating the state. Here, however, we get a valid
     * schema from the form and we don't need to know the types.
     */
    // private handleChangeForm(data: any) {
    //     //const cloned = _.clone(data.formData);
    //     let updater: (data: any) => React.UpdateSpec<State>
    //         = (data: any) => ({formData: {$set: data}});
    //     this.changeState(updater, data);
    // }
    // private changeState<T>(updater: (data: T) => React.UpdateSpec<State>, data: T) {
    //     const updatedState = update(this.state, updater(data));
    //     this.setState((prevState, props) => updatedState );
    // }
}
