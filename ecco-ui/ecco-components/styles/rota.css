
.rota-now-line {
    position: absolute;
    height: 100%;
    width: 0;
    border: 1px solid red;
    z-index: auto
}

/* Ensure our spans break between lines */
.rota-week td > span.label {
    float: left;
    white-space: normal;
    margin-top: 1px;
}

.rota {
    position: relative;
    font-size: 14px;
    user-select: none;
}

.rota-day table {
    border-collapse: separate;
    border-spacing: 0px 2px;
    table-layout: fixed;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px; /* as per rota week 'table-bordered' */
    /*line-height: initial;*/
}

.rota-week table {
    table-layout: fixed;
}

.rota .nowrap {
    overflow: hidden; /* undo to show all text */
    text-overflow: ellipsis;
    white-space: nowrap;
}

.rota .rota-worker-name, .rota .rota-row-label {
    border: 0;
    border-top: 0 white solid;
    border-bottom: 0 white solid;
}

.rota td,
.rota th.rota-worker-name,
.rota th.rota-row-label {
    border: 1px solid #00000010; /* generic border for time-slots - all around td, the white every hour is below */
    font-weight: normal;
    text-align: center;
    z-index: 1;
}

/* this doesn't work with dropping targets colspan */
/*.rota td:nth-child(4n+1) {
    border-right: 1px solid #eee; * more solid line every hour *
}*/

.rota-worker-row {
    height: 46px; /* Should match .rota-worker-name below for when we horiz scroll */
}
.rota tbody td, .rota th.rota-worker-name {
    height: 46px; /* need fixed height for when we fix first column on scroll right */
}
/* make appointments higher also */
.rota-day .rota-draggable-activity, .rota-day .rota-appointment-colour {
    height: 44px;
}

.rota-appointment.rota-clash div {
    opacity: 0.4;
    background-color: red !important;
}

.rota td.rota-worker-name-head {
    border: none;
}

.rota th.rota-time {
    position: relative;
    height: 20px;
    vertical-align: top;
    overflow: visible;
    font-size: 12px;
    font-weight: normal;
}

.rota th.rota-time p {
    position: relative;
    left: -20px;
    margin: 0;
    border: 0;
    padding: 0;
    text-align: center;
    overflow: hidden;
}

.rota th.rota-time-minor p {
    width: 0;
}

.rota .rota-time-tick {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 1.1px;
    background: #ddd;
}

.rota th.rota-time-major .rota-time-tick {
    height: 6px;
}

.rota th.rota-time-minor .rota-time-tick {
    height: 0;
}

.rota th.rota-worker-name, .rota .rota-row-label {
    background: white;
    height: 100%;
    vertical-align: top;
}

/* copied from housestyle with th for td to take priority over the local rule above */
.rota-day th.clickable {
    color: #3c83ca;
}
.rota-day th.clickable:hover, .rota-day tr th.mimic-hover {
    color: #F7F6F6;
    background: #3c83ca;
    cursor:pointer;
}

/* This now floats from the table cell */
.rota .rota-appointment {
    position: relative; /* for now, as we can rely on layout with a cell */
    /*margin-top: 1px;*/
    padding: 0;
    /*background: #5bc0de;*/
}

.rota .rota-appointment div {
    border-radius: 5px;
    border: white solid 1px;
    padding: 0;
    text-align: center;
    font-weight: 700;
    font-size: 10.5px;
    color: white;
}

.rota .rota-appointment span {
    padding: 0;
    /* make span fill whole cell */
    display: inline-block;
    height: 44px;
    width: 100%;
}

.rota td.rota-worker-available {
    background: #5bc0de; /* as per week view (using alpha 5bc0de80 means white text on light background) */
}

.rota td.rota-worker-unavailable {
}

.rota .rota-draggable-activity {
    margin-top: 1px;
    cursor: pointer;
    border-radius: 5px;
    /*border: 1px solid grey;*/
    text-align: center;
}

.rota td.rota-appointment-droppable {
    background: lightgreen;
}

.rota td.rota-appointment-droppable.rota-appointment-non-match {
    background: #da6666;
}

.rota td.rota-appointment-droppable-hover {
    background: rgb(92, 184, 92); /* #5cb85c */
}

.rota td.rota-appointment-droppable-hover.rota-appointment-non-match {
    background: rgb(92, 184, 92);
}


.rota .rota-cancelled-activities-group div.rota-draggable-activity {
    padding: 0;
    background: #777;
    border-radius: 5px;
    font-weight: 700;
    font-size: 10.5px;
    color: white;
    white-space: normal;
}
.rota .rota-unassigned-activities-group div.rota-draggable-activity {
    padding: 0;
    background: #f0ad4e;
    border-radius: 5px;
    font-weight: 700;
    font-size: 10.5px;
    color: white;
    white-space: normal;
}

/*.rota .rota-unassigned-activities-group tr:first-child {
    border-top: 1px solid #bbb;
}*/

.rota tr:nth-child(even) {
    background-color: #0000000b;
}

.rota .rota-unassigned-activities-group tr {
    background: #00000020;  /* (using f0ad4e80 means white text on light background) */
}


.rota .rota-unassigned-activities-group td {
    margin: 0;
    padding: 0;
}

.rota .rota-cancelled-activities-group td {
}

.rota .rota-unassigned-activities-group td span,
.rota .rota-cancelled-activities-group td span {
    /* make span fill whole cell */
    display: inline-block;
    height: 44px;
    width: 100%;
}

th.time-headings span {
    position: relative;
    left: -15px;
    padding: 2px;
}

th.time-night-to-day {
    background: #fefcea; /* Old browsers */
    background: linear-gradient(135deg, #e2e2e2 0%, #b5b5b5 40%, #fefcea 60%, #f1da36 100%);
}

th.time-day-to-night {
    background: #e2e2e2;
    background: linear-gradient(45deg, #f1da36 0%, #fefcea 40%, #b5b5b5 60%, #e2e2e2 100%);
}

th.time-day {
    background: #fefcea;
    background: linear-gradient(to bottom,  #fefcea 0%,#f1da36 100%); /* W3C */
}

th.time-night {
    background: #e2e2e2; /* Old browsers */
    background: linear-gradient(to bottom,  #e2e2e2 0%,#b5b5b5 100%);
}

tr.weekend {
    color: #666;
    opacity: 0.8;
}

tr.weekend td.unavailable {
    background-color: #bbb !important;
}

tr.beforeToday {
    text-decoration: line-through;
}

table#worker-availability {
    border-spacing: 0;
}

#worker-availability th {
    white-space: nowrap;
    font-size: 70%;
    line-height: 2em;
}

#worker-availability td {
    border-left: 1px solid white;
    border-top: 1px solid white;
}

#worker-availability tbody td:nth-child(even) { /* Darker lines on the hour */
    border-left: solid 1px #ccc;
}

#worker-availability tbody td:nth-child(6n + 2) { /* Even darker lines on every 3rd hour */
    border-left: solid 1px #999;
}

#worker-availability td.available {
    background-color: lightgreen; /* we use lightgoldenrodyellow on allocation UI */
}

#worker-availability td.to-available {
    background-color: green !important;
}

#worker-availability td.unavailable {
    background-color: #eee;
}

#worker-availability td.to-unavailable {
    background-color: gray !important;
}

#worker-availability td.selected {
    background-color: yellow !important;
}
