import * as React from "react";
import {FC, useMemo} from "react";
import {keyBy} from "@softwareventures/array"
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {usePrintIcon} from "../layout/printing";
import {useNearbyAppointments, usePromise} from "../data/entityLoadHooks";
import {
    ActivityItem,
    AdditionalStaffData,
    AdditionalStaffRenderer,
    AdditionalStaffText,
    attendeeNotCurrentUser,
    getShiftGuidanceElement,
    LoadingSpinner,
    SimpleSchemaFormRenderer,
    useAdditionalStaff,
    useServicesContext
} from "../index"; // pulling in index brings in style sheets (a bit of a hack)
import {DemandScheduleDto, EventResourceDto, getRelation} from "ecco-dto";
import {REL_RUN_BREAKDOWN} from "ecco-rota";
import {flagsAsDefinitionListEntry, RiskFlags} from "../flags/flags";

// NB care-run breakdown comes from /events - a different API to the rota
const AppointmentActivity: FC<{event: EventResourceDto}> = ({event}) => {
    const {apiClient, sessionData} = useServicesContext();

    const {resolved: schedule} = usePromise(() => apiClient.fetchRelation<DemandScheduleDto>(event, 'demand-schedule'));
    const {additionalStaff} = useAdditionalStaff(event, event.uid);

    return (
        <li>
            {schedule && (
                <ActivityItem
                    event={event}
                    additionalStaff={additionalStaff}
                    title={`${schedule.serviceRecipientName} (${event.title})`}
                    flags={<RiskFlags resource={event} renderer={flagsAsDefinitionListEntry} />}
                    location={event.location}
                    sessionData={sessionData}
                    schedule={schedule}
                />
            )}
        </li>
    );
}

const CareRunBreakdown: FC<{event: EventResourceDto}> = ({event} ) => {
    const now = useMemo(() => EccoDateTime.nowLocalTime().formatIso8601(), []);
    const {apiClient, sessionData} = useServicesContext();
    const {resolved} = usePromise(() =>
        apiClient.fetchRelation<EventResourceDto[]>(event, REL_RUN_BREAKDOWN)
    );
    const {formDefinitionUuid, guidanceElement} = getShiftGuidanceElement(
        sessionData,
        SimpleSchemaFormRenderer,
        event.serviceRecipientId!
    );

    return resolved && (!formDefinitionUuid || guidanceElement) ? (
        <>
            {guidanceElement}
            <ol style={{border: "1px solid grey", padding: 8, margin: 8}}>
                {resolved
                    .filter(e => e.uid != event.uid) // Don't want the shift again
                    .sort((a, b) => a.start.localeCompare(b.start))
                    .map(event => (
                        <div
                            className={`rotaEvent ${
                                event.end && event.end.localeCompare(now) < 0 ? "past" : ""
                            }`}
                        >
                            <AppointmentActivity event={event} />
                        </div>
                    ))}
            </ol>
        </>
    ) : (
        <LoadingSpinner />
    );
};

function careRunBreakdown(event: EventResourceDto) {
    const relation = getRelation(event, REL_RUN_BREAKDOWN);
    return relation ? <CareRunBreakdown event={event}/> : null;
}

const AdditionalStaffDT = (data: AdditionalStaffData) => {
    return (
        <>
            <dt>and</dt>
            <dd>
                <AdditionalStaffText additionalStaff={data} />
            </dd>
        </>
    );
};

export const PrintableAppointments: FC<{calendarId: string, subjectDisplayName: string}> = ({calendarId, subjectDisplayName}) => {
    const now = useMemo(() => EccoDateTime.nowLocalTime().formatIso8601(), []);

    const {printableContentRef, PrintIcon} = usePrintIcon<HTMLDivElement>();
    const {appointments} = useNearbyAppointments(calendarId);
    const {sessionData} = useServicesContext();

        if (!appointments) return <LoadingSpinner/>

        // See head,content/reportAppointments.jsp for previouw
        const apptsByDay = keyBy(appointments, appt => appt.start.substr(0,10));
        return (
            <>
                <div className="clickable" style={{float: "right", opacity: 0.7}}>
                    <PrintIcon />
                </div>
                <div className="rotaReport" ref={printableContentRef}>
                    <h2>Appointments for {subjectDisplayName}</h2>
                    {Object.keys(apptsByDay)
                        .sort()
                        .map(date => (
                            <div
                                className={`rotaDay ${
                                    date.localeCompare(now.substr(0, 10)) < 0 ? "past" : ""
                                }`}
                            >
                                <h3>{EccoDate.parseIso8601(date).formatPretty()}</h3>
                                <ol>
                                    {apptsByDay
                                        .get(date)
                                        ?.sort((a, b) => a.start.localeCompare(b.start))
                                        .map(event => {
                                            const start = EccoDateTime.parseIso8601(event.start);
                                            const end = EccoDateTime.parseIso8601(event.end);
                                            return (
                                                <>
                                                    <li
                                                        className={`rotaEvent ${
                                                            event.end &&
                                                            event.end.localeCompare(now) < 0
                                                                ? "past"
                                                                : ""
                                                        }`}
                                                    >
                                                        <header>
                                                            {event.allDay ? (
                                                                <span className="rotaAllDay">
                                                                    {start.formatDateShort()}
                                                                </span>
                                                            ) : (
                                                                <span className="rotaPeriod">
                                                                    <span className="rotaTime">
                                                                        {start
                                                                            .toEccoTime()
                                                                            .formatHoursMinutes()}
                                                                    </span>{" "}
                                                                    &#8211;{" "}
                                                                    <span className="rotaTime">
                                                                        {/* may not be a rota event - might not have end */}
                                                                        {end
                                                                            ?.toEccoTime()
                                                                            .formatHoursMinutes()}
                                                                    </span>
                                                                </span>
                                                            )}
                                                            <span className="rotaTitle">
                                                                {" "}
                                                                {event.title}
                                                            </span>
                                                        </header>
                                                        <dl>
                                                            {event.attendees &&
                                                                event.attendees.length > 1 && (
                                                                    <>
                                                                        <dt>with</dt>
                                                                        <dd>
                                                                            <div>
                                                                                {/* TODO 'not me' may not be accurate (eg on the rota) */}
                                                                                {
                                                                                    attendeeNotCurrentUser(
                                                                                        sessionData,
                                                                                        event
                                                                                    )?.name
                                                                                }
                                                                            </div>
                                                                        </dd>
                                                                    </>
                                                                )}
                                                            <AdditionalStaffRenderer
                                                                resource={event}
                                                                activityRef={event.uid}
                                                                renderer={AdditionalStaffDT}
                                                            />
                                                        </dl>
                                                        <div className="clearfix" />
                                                        {careRunBreakdown(event)}
                                                    </li>
                                                </>
                                            );
                                        })}
                                </ol>
                            </div>
                        ))}
                </div>
            </>
        );
}
