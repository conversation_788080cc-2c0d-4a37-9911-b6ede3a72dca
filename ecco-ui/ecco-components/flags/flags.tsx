import {
    FlagEvidenceDto,
    getRelation,
    ListDefinitionEntry,
    RiskFlagsSnapshotDto,
    SessionData
} from "ecco-dto";
import {HateoasResource} from "@eccosolutions/ecco-common";
import * as React from "react";
import {ReactElement} from "react";
import {useServicesContext} from "../ServicesContext";
import {usePromise} from "../data/entityLoadHooks";
import {Typography} from "@eccosolutions/ecco-mui";

/**
 * @Exemplar Shows good example of rendering a Hateoas relation
 *
 * Loads and renders "risk flags" relation of the suppied Hateoas resource
 * @param resource - Hateoas resource which may have "risk flags" relation
 * @param renderer - function to turn ListDefintionEntry[] into the ReactElement to render (e.g. could include flag icon)
 * @constructor
 */
export function RiskFlags({
    resource,
    renderer
}: {
    resource: HateoasResource;
    renderer: (sessionData: SessionData, flags: FlagEvidenceDto[]) => ReactElement;
}) {
    const {apiClient, sessionData} = useServicesContext();

    const link = getRelation(resource, "risk flags");
    if (!link) return null;

    const {resolved} = usePromise(() => apiClient.getCached<RiskFlagsSnapshotDto>(link.href));
    const flags = resolved && resolved.latestFlags.filter(f => f.value); // filter for true flags
    return flags ? renderer(sessionData, flags) : null;
}

/**
 * Render graphically, as per RiskStatusAreaControl render.
 * For rendering as a stream of text, see RiskFlagsAsTextList below.
 * For rendering as a list of text, see RiskFlagsAsTextList below.
 */
export function flagsAsGraphical(
    sessionData: SessionData,
    flags: FlagEvidenceDto[],
    bold = true,
    prefix = false
) {
    return (
        <>
            <dt>&nbsp;</dt>
            <dd style={bold ? {fontWeight: "bold"} : {}}>
                {flags.map(it => flagAsGraphical(sessionData, it, prefix))}
            </dd>
        </>
    );
}

/**
 * @param prefix Where the flag may appear with 'added' or 'removed' (ie the history)
 */
function flagAsGraphical(sessionData: SessionData, flag: FlagEvidenceDto, prefix: boolean) {
    const flagStyled = flagStyledAsGraphical(sessionData, flag);
    const Icon = flagStyled || <span role={"img"} className={"std-flag-image flag_red24"}></span>;
    const flagDef = sessionData.getListDefinitionEntryById(flag.flagId);
    return (
        <>
            {Icon} {/* TODO if removed flag, then should be grey/disabled? */}
            {prefix && (flag.value ? " added flag:" : " removed flag:")}
            <span>&nbsp;{flagDef.getDisplayName()}</span>
        </>
    );
}
export function flagStyledAsGraphical(
    sessionData: SessionData,
    flag: {flagId: number},
    span = true
) {
    const flagDef = sessionData.getListDefinitionEntryById(flag.flagId);
    if (!flagDef.getIconClasses() && !flagDef.getColour()) {
        return null;
    }
    const defaultStyle = {padding: "5px"};
    const flagStyle = flagDef.getColour()
        ? {defaultStyle, color: flagDef.getColour()}
        : defaultStyle;
    return span ? (
        <span className={flagDef.getIconClasses()} style={flagStyle} />
    ) : (
        <i className={flagDef.getIconClasses()} style={flagStyle} />
    );
}

export function flagsAsDefinitionListEntry(sessionData: SessionData, flags: FlagEvidenceDto[]) {
    return (
        <>
            <dt>&nbsp;</dt>
            <dd style={{fontWeight: "bold"}}>
                {flags
                    .map(it => sessionData.getListDefinitionEntryById(it.flagId).getDisplayName())
                    .join(", ")}
            </dd>
        </>
    );
}

export function RiskFlagsAsTextList(props: { serviceRecipientId: number }) {
    return (
        <RiskFlags
            resource={{
                links: [
                    {
                        rel: "risk flags",
                        href: `service-recipients/${props.serviceRecipientId}/evidence/threat/snapshots/latest/`
                    }
                ]
            }}
            renderer={(sessionData, flags) => (
                <>
                    <Typography
                        // className={classes.title}
                        color="textSecondary"
                    >
                        flags:
                    </Typography>
                    <Typography
                        // className={classes.pos}
                        color="textPrimary"
                    >
                        {flags
                            .map(it =>
                                sessionData.getListDefinitionEntryById(it.flagId).getDisplayName()
                            )
                            .join(", ")}
                    </Typography>
                </>
            )}
        />
    );
}