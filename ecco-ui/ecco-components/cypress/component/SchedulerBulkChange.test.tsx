import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {carer1, carer2, sessionData, testStaff} from "../../__tests__/testUtils";
import {WorkersAjaxRepository} from "ecco-dto";
import {Button, Grid} from "@eccosolutions/ecco-mui";
import {useState} from "react";
import {SchedulerBulkChangeCarer, SchedulerBulkDrop} from "../../rota/SchedulerBulkChange";
import {WorkerJobSelector} from "../../rota/WorkerJobSelector";
import {EccoDate} from "@eccosolutions/ecco-common";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";


const workersRepository = getFailAllMethodsMock(WorkersAjaxRepository);
workersRepository.findWorkersWithAccessTo = (
    serviceId: number,
    projectId?: number | undefined,
    role = "ROLE_CARER"
) => {
    return Promise.resolve([carer1, carer2]);
};

workersRepository.findWorkersEmployedAtByIndividualIds = (
    individualIds: number[],
    employedAt: EccoDate
) => {
    return Promise.resolve(testStaff);
};

const overrides = {
    sessionData: sessionData,
    workersRepository: workersRepository
} as any as EccoAPI;

function CarerJobSelectorTest() {
    const [carerJobSrId, setCarerJobSrId] = useState<number | null>(null);

    return (
        <>
            <WorkerJobSelector
                labelName={"pick"}
                role={"ROLE_CARER"}
                serviceId={999}
                onChange={setCarerJobSrId}
                selectedSrId={carerJobSrId}
            />
            <hr style={{marginTop: "250px"}} />
            <pre>{`workerJobSrId: ${JSON.stringify(carerJobSrId)}`}</pre>
        </>
    );
}

function SchedulerBulkChangeCarerTest() {
    const [open, setOpen] = useState<boolean>(false);
    const [carerJobId, setCarerJobId] = useState<number | null>(null);

    const processCarerChange = (carerJobId: number) => {
        setCarerJobId(carerJobId);
        setOpen(false);
    };

    return (
        <>
            <Button onClick={() => setOpen(true)}>carer</Button>
            <SchedulerBulkChangeCarer
                serviceId={null}
                open={open}
                onClose={() => setOpen(false)}
                onSave={processCarerChange}
            />
            <pre>{`carerJobId: ${JSON.stringify(carerJobId)}`}</pre>
        </>
    );
}

function SchedulerBulkDropTest() {
    const [open, setOpen] = useState<boolean>(false);
    const [dropReasonId, setDropReasonId] = useState<number | null>(null);

    const processDrop = (dropReasonId: number) => {
        setDropReasonId(dropReasonId);
        setOpen(false);
    };

    return (
        <>
            <Button onClick={() => setOpen(true)}>drop</Button>
            <SchedulerBulkDrop open={open} onClose={() => setOpen(false)} onSave={processDrop} />
            <pre>{`dropReasonId: ${JSON.stringify(dropReasonId)}`}</pre>
        </>
    );
}

describe("SchedulerBulk tests", () => {
    it("job select", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <Grid container alignItems="baseline">
                    <Grid item xs={12}>
                        <CarerJobSelectorTest />
                    </Grid>
                </Grid>
            </TestServicesContextProvider>
        );
    });

    it("change carer", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <Grid container alignItems="baseline">
                    <Grid item xs={12}>
                        <SchedulerBulkChangeCarerTest />
                    </Grid>
                </Grid>
            </TestServicesContextProvider>
        );
        // cy.findByText("open").should("exist").click();
        // cy.findByText("bulk change").should("exist");
        // cy.findByText("update").should("exist");
        // cy.findByRole("button", {name: "update"}).should("be.disabled");
    });

    it("drop", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <Grid container alignItems="baseline">
                    <Grid item xs={12}>
                        <SchedulerBulkDropTest />
                    </Grid>
                </Grid>
            </TestServicesContextProvider>
        );
    });
});
