import {
    BooleanChange,
    CommandDto,
    NumberChangeOptional,
    StringChangeOptional,
    Mergeable,
    EventAttendee,
    onlyInLeft
} from "ecco-dto";
import {EccoDate} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {assertNotNull, BaseUpdateCommandTransitioning} from "../cmd-queue/commands";
import {BaseCommandOperations} from "../commands";

// Matches ContactCalendarEntryCommandViewModel.java */
export interface ContactCalendarEntryDto extends CommandDto {
    contactId: number;
    calendarEntryViewModel: CalendarEntryDto;
}
// Matches ServiceRecipientCalendarEntryCommandViewModel.java */
export interface ServiceRecipientCalendarEntryDto extends CommandDto {
    serviceRecipientId: number;
    calendarEntryViewModel: CalendarEntryDto;
}
export interface CalendarEntryDto {
    operation: string;
    eventUuid: string | null;
    title: StringChangeOptional;
    startDate: StringChangeOptional;
    allDay: BooleanChange;
    startTime: StringChangeOptional;
    endDate: StringChangeOptional;
    endTime: StringChangeOptional;
    eventCategoryId: NumberChangeOptional;
    repeatEveryDays: NumberChangeOptional;
    repeatEveryWeeks: NumberChangeOptional;
    repeatEndDate: StringChangeOptional;
    attendees: CalendarAttendeeDto[] | undefined;
}
// Matches CalendarAttendeeCommandViewModel.java */
export interface CalendarAttendeeDto {
    operation: string;
    name: string;
    calendarIdUserReferenceUri: string; // cosmo representation - see CosmoHelper.syncAttendeesWithCalendars
    calendarId?: string; // integration-representation (pointing to the user's collection)
    requiredChange: BooleanChange | null;
    statusChange: NumberChangeOptional | null;
}

/**
 * Base Command to add, remove or update a calendar entry
 */
export class ContactCalendarEntryCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "calendarEntry";

    /** operation should be "add", "update" or "delete" */
    constructor(
        uuid: Uuid,
        private contactId: number,
        private calendarEntry: CalendarEntryCommand
    ) {
        super(
            `contact/${contactId}/calendar-entry/command/`,
            uuid,
            ContactCalendarEntryCommand.discriminator
        );
        assertNotNull(uuid, "ContactCalendarEntryCommand.uuid");
        assertNotNull(contactId, "ContactCalendarEntryCommand.contactId");
    }

    public getCommandDto(): ContactCalendarEntryDto {
        return {
            commandName: ContactCalendarEntryCommand.discriminator, // must match ORM discriminator at server end
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            contactId: this.contactId,
            calendarEntryViewModel: this.calendarEntry.getCommandDto()
        };
    }
}

/**
 * Base Command to add, remove or update a calendar entry
 */
export class ServiceRecipientCalendarEntryCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "calendarEntry";

    /** operation should be "add", "update" or "delete" */
    constructor(
        uuid: Uuid,
        private serviceRecipientId: number,
        private calendarEntry: CalendarEntryCommand
    ) {
        super(
            `service-recipient/${serviceRecipientId}/calendar-entry/command/`,
            uuid,
            ServiceRecipientCalendarEntryCommand.discriminator
        );
        assertNotNull(uuid, "ServiceRecipientCalendarEntryCommand.uuid");
        assertNotNull(
            serviceRecipientId,
            "ServiceRecipientCalendarEntryCommand.serviceRecipientId"
        );
    }

    public getCommandDto(): ServiceRecipientCalendarEntryDto {
        return {
            commandName: ServiceRecipientCalendarEntryCommand.discriminator, // must match ORM discriminator at server end
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            calendarEntryViewModel: this.calendarEntry.getCommandDto()
        };
    }
}

export class CalendarEntryCommand extends BaseCommandOperations {
    private title: StringChangeOptional;
    private startDate: StringChangeOptional;
    private allDay: BooleanChange;
    private startTime: StringChangeOptional;
    private endDate: StringChangeOptional;
    private endTime: StringChangeOptional;
    private eventCategoryId: NumberChangeOptional;
    private eventStatusId: NumberChangeOptional;
    private repeatEveryDays: NumberChangeOptional;
    private repeatEveryWeeks: NumberChangeOptional;
    private repeatEndDate: StringChangeOptional;
    private attendees: CalendarAttendeeDto[] | undefined;

    /** operation should be "add", "update" or "delete" */
    constructor(
        private operation: string,
        protected eventUuid: string | null
    ) {
        super();
        assertNotNull(operation, "CalendarEntryCommand.operation");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    public changeTitle(from: string | null, to: string | null) {
        this.title = this.asStringChange(from, to);
        return this;
    }
    public changeStartDate(from: EccoDate | null, to: EccoDate | null) {
        this.startDate = this.asDateChange(from, to);
        return this;
    }
    public changeAllDay(from: boolean | null, to: boolean | null) {
        this.allDay = this.asBooleanChange(from, to);
        return this;
    }
    public changeStartTime(from: string | null, to: string | null) {
        this.startTime = this.asStringChange(from, to);
        return this;
    }
    public changeEndDate(from: EccoDate | null, to: EccoDate | null) {
        this.endDate = this.asDateChange(from, to);
        return this;
    }
    public changeEndTime(from: string | null, to: string | null) {
        this.endTime = this.asStringChange(from, to);
        return this;
    }
    public changeEventCategoryId(from: number | null, to: number | null) {
        this.eventCategoryId = this.asNumberChange(from, to);
        return this;
    }
    public changeEventStatusId(from: number | null, to: number | null) {
        this.eventStatusId = this.asNumberChange(from, to);
        return this;
    }
    // as per DaysOfWeek.java (SUN = 1)
    public changeRepeatEveryDays(from: number | null, to: number | null) {
        this.repeatEveryDays = this.asNumberChange(from, to);
        return this;
    }
    public changeRepeatEveryWeeks(from: number | null, to: number | null) {
        this.repeatEveryWeeks = this.asNumberChange(from, to);
        return this;
    }
    public changeRepeatEndDate(from: EccoDate | null, to: EccoDate | null) {
        this.repeatEndDate = this.asDateChange(from, to);
        return this;
    }
    public changeAttendees(
        fromIn: EventAttendee[] | undefined | null,
        toIn: EventAttendee[] | undefined | null
    ) {
        const from = fromIn || [];
        const to = toIn || [];
        const removedAttendees = onlyInLeft(
            from,
            to,
            (a, b) => a.calendarIdUserReferenceUri == b.calendarIdUserReferenceUri
        );
        const newAttendees: EventAttendee[] = onlyInLeft(
            to,
            from,
            (a, b) => a.calendarIdUserReferenceUri == b.calendarIdUserReferenceUri
        );
        this.attendees = [];

        if (newAttendees.length > 0) {
            newAttendees.map(a => {
                const vm: CalendarAttendeeDto = {
                    operation: "add",
                    name: a.name, // to help with displaying audits
                    calendarIdUserReferenceUri: a.calendarIdUserReferenceUri,
                    // NB calendarId can be referenced on 'new' since its come from PersonUserSummary
                    calendarId: a.calendarId,
                    requiredChange: null,
                    statusChange: null
                };
                this.attendees!.push(vm);
            });
        }

        if (removedAttendees.length > 0) {
            throw new Error(
                "cannot remove an attendee since can't reference them from calendarIdUserReferenceUri, and don't have calendarId"
            );
            // removedAttendees.map(a => {
            //     const vm: CalendarAttendeeDto = {
            //         operation: "remove",
            //         name: a.name,
            //         calendarIdUserReferenceUri: a.calendarIdUserReferenceUri,
            //         requiredChange: null,
            //         statusChange: null
            //     };
            //     this.attendees.push(vm);
            // })
        }

        return this;
    }
    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return (
            this.title != null ||
            this.startDate != null ||
            this.startTime != null ||
            this.allDay != null ||
            this.endDate != null ||
            this.endTime != null ||
            this.eventCategoryId != null ||
            this.eventStatusId != null ||
            this.repeatEveryDays != null ||
            this.repeatEveryWeeks != null ||
            this.repeatEndDate != null ||
            (this.attendees != undefined && this.attendees.length > 0)
        );
    }

    public getCommandDto(): CalendarEntryDto {
        return {
            operation: this.operation,
            eventUuid: this.eventUuid,
            title: this.title,
            startDate: this.startDate,
            allDay: this.allDay,
            startTime: this.startTime,
            endDate: this.endDate,
            endTime: this.endTime,
            eventCategoryId: this.eventCategoryId,
            repeatEveryDays: this.repeatEveryDays,
            repeatEveryWeeks: this.repeatEveryWeeks,
            repeatEndDate: this.repeatEndDate,
            attendees: this.attendees
        };
    }
}
