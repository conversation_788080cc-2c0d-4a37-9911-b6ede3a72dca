import {
    CommonOptionOverrides,
    CommonOptions,
    resolveCommonOptionOverrides,
    resolveCommonOptions,
    ResolvedCommonOptions
} from "./common";
import {WebpackEnv} from "../webpack/env";
import {WebpackArguments} from "../webpack/arguments";
import {objectFromEntries} from "@softwareventures/object";
import {isArray} from "@softwareventures/array";

export interface ScriptOptions extends CommonOptions {
    readonly target: "script";
    readonly entry?:
        | {
              readonly [name: string]: string;
          }
        | readonly string[]
        | undefined;
}

export interface ResolvedScriptOptions<TMode extends string = "development" | "production">
    extends ResolvedCommonOptions<TMode> {
    readonly target: "script";
    readonly entry: {
        readonly [name: string]: string;
    };
}

export async function resolveScriptBuildOptions(
    options: ScriptOptions,
    webpackEnv: WebpackEnv,
    args: WebpackArguments
): Promise<ResolvedScriptOptions> {
    return resolveScriptOptions(options, resolveScriptOptionOverrides(webpackEnv, args));
}

export async function resolveScriptTestOptions(
    options: ScriptOptions
): Promise<ResolvedScriptOptions<"test">> {
    return resolveScriptOptions(options, {mode: "test"});
}

function resolveScriptOptionOverrides(
    webpackEnv: WebpackEnv,
    args: WebpackArguments
): CommonOptionOverrides<"production" | "development"> {
    const commonOptionOverrides = resolveCommonOptionOverrides(webpackEnv, args);

    return {
        ...commonOptionOverrides,
        mode: commonOptionOverrides.mode ?? "production"
    };
}

async function resolveScriptOptions<TMode extends "production" | "development" | "test">(
    options: ScriptOptions,
    overrides: CommonOptionOverrides<TMode>
): Promise<ResolvedScriptOptions<TMode>> {
    const commonOptions = await resolveCommonOptions(options, overrides);

    return {
        ...commonOptions,
        target: "script",
        entry: isArray(options.entry)
            ? objectFromEntries(options.entry.map(entry => [entry, entry] as const))
            : (options.entry ?? {
                  [commonOptions.packageName ?? "script"]: "index"
              })
    };
}
