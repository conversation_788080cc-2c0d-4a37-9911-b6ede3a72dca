import {
    CommonOptionOverrides,
    CommonOptions,
    CommonPathOverrides,
    CommonPaths,
    resolveCommonOptionOverrides,
    resolveCommonOptions,
    ResolvedCommonOptions,
    ResolvedCommonPaths
} from "./common";
import {WebpackEnv} from "../webpack/env";
import {WebpackArguments} from "../webpack/arguments";
import {hasProperty} from "unknown";
import {posix as pathPosix, resolve as resolvePath} from "node:path";

export interface SinglePageAppPaths extends CommonPaths {
    readonly public?: string | undefined;
    readonly html?: string | undefined;
}

export interface ResolvedSinglePageAppPaths extends ResolvedCommonPaths {
    readonly public: string;
    readonly html: string;
}

export interface SinglePageAppOptions extends CommonOptions {
    readonly target: "single-page-app";
    readonly paths?: SinglePageAppPaths | undefined;
    readonly publicUrl: string;
    readonly imageInlineSizeLimit?: number | undefined;
    readonly serviceWorker?:
        | {
              readonly implementation?:
                  | {
                        readonly src: string;
                    }
                  | "auto"
                  | undefined;
              readonly maximumFileSizeToCacheInBytes?: number | null | undefined;
          }
        | "auto"
        | "none"
        | undefined;
    readonly devServer?:
        | {
              readonly port?: number | undefined;
          }
        | undefined;
}

export interface ResolvedSinglePageAppOptions<TMode extends string = "development" | "production">
    extends ResolvedCommonOptions<TMode> {
    readonly target: "single-page-app";
    readonly paths: ResolvedSinglePageAppPaths;
    readonly publicUrl: string;
    readonly imageInlineSizeLimit: number;
    readonly serviceWorker:
        | {
              readonly implementation:
                  | {
                        readonly src: string;
                    }
                  | "auto";
              readonly maximumFileSizeToCacheInBytes: number;
          }
        | "none";
    readonly devServer: {
        readonly port: number;
    };
}

export function resolveSinglePageAppBuildOptions(
    options: SinglePageAppOptions,
    webpackEnv: WebpackEnv,
    args: WebpackArguments
) {
    return resolveSinglePageAppOptions(
        options,
        resolveSinglePageAppOptionOverrides(webpackEnv, args)
    );
}

export function resolveSinglePageAppTestOptions(options: SinglePageAppOptions) {
    return resolveSinglePageAppOptions(options, {mode: "test"});
}

interface SinglePageAppOptionOverrides<TMode extends string> extends CommonOptionOverrides<TMode> {
    readonly paths?: SinglePageAppPathOverrides | undefined;
    readonly publicUrl?: string | undefined;
    readonly imageInlineSizeLimit?: number | undefined;
    readonly devServer?:
        | undefined
        | {
              readonly port?: number | undefined;
          };
}

interface SinglePageAppPathOverrides extends CommonPathOverrides {
    readonly public?: string | undefined;
    readonly html?: string | undefined;
}

function resolveSinglePageAppOptionOverrides(
    webpackEnv: WebpackEnv,
    args: WebpackArguments
): SinglePageAppOptionOverrides<"production" | "development"> {
    const commonOptionOverrides = resolveCommonOptionOverrides(webpackEnv, args);

    const devServer =
        Boolean(process.env["WEBPACK_SERVE"]) || Boolean(process.env["WEBPACK_DEV_SERVER"]);

    return {
        ...commonOptionOverrides,
        mode: commonOptionOverrides.mode ?? (devServer ? "development" : "production"),
        paths: {
            ...commonOptionOverrides.paths,
            public:
                typeof webpackEnv["publicPath"] === "string" ? webpackEnv["publicPath"] : undefined,
            html: typeof webpackEnv["htmlPath"] === "string" ? webpackEnv["htmlPath"] : undefined
        },
        publicUrl:
            typeof webpackEnv["publicUrl"] === "string" ? webpackEnv["publicUrl"] : undefined,
        imageInlineSizeLimit:
            typeof webpackEnv["imageInlineSizeLimit"] === "string"
                ? parseInt(webpackEnv["imageInlineSizeLimit"], 10)
                : undefined,
        devServer: {
            port: webpackEnv["port"] != null ? parseInt(String(webpackEnv["port"]), 10) : undefined
        }
    };
}

async function resolveSinglePageAppOptions<TMode extends "production" | "development" | "test">(
    options: SinglePageAppOptions,
    overrides: SinglePageAppOptionOverrides<TMode>
): Promise<ResolvedSinglePageAppOptions<TMode>> {
    const commonOptions = await resolveCommonOptions(options, overrides);
    const publicPath = resolvePath(
        commonOptions.paths.root,
        overrides.paths?.public ?? options.paths?.public ?? "public"
    );
    const htmlPath = resolvePath(
        commonOptions.paths.root,
        overrides.paths?.html ?? options.paths?.html ?? `${publicPath}/index.html`
    );
    const publicUrl = `${overrides.publicUrl ?? options.publicUrl}/`.replace(/\/\/$/, "/");
    const imageInlineSizeLimit =
        overrides.imageInlineSizeLimit ?? options.imageInlineSizeLimit ?? 10000;
    const serviceWorker =
        options.serviceWorker === "none" || options.serviceWorker == null
            ? "none"
            : ({
                  implementation:
                      options.serviceWorker === "auto" ||
                      options.serviceWorker.implementation == null ||
                      options.serviceWorker.implementation === "auto"
                          ? "auto"
                          : {
                                src: resolvePath(
                                    commonOptions.paths.root,
                                    options.serviceWorker.implementation.src
                                )
                            },
                  maximumFileSizeToCacheInBytes:
                      options.serviceWorker === "auto" ||
                      options.serviceWorker.maximumFileSizeToCacheInBytes == null ||
                      !isFinite(options.serviceWorker.maximumFileSizeToCacheInBytes)
                          ? 50 * 1024 * 1024
                          : options.serviceWorker.maximumFileSizeToCacheInBytes < 0
                            ? 0
                            : options.serviceWorker.maximumFileSizeToCacheInBytes
              } as const);
    const devServer = {
        port: overrides.devServer?.port ?? options.devServer?.port ?? 3000
    };

    if (!urlIsAbsolute(publicUrl) && !pathPosix.isAbsolute(publicUrl)) {
        if (publicUrl.startsWith("_CONTEXT_PATH_")) {
            // As a temporary exception, publicUrl is allowed to be relative
            // provided it starts with _CONTEXT_PATH_.

            // Remove this exception and throw an error instead when we start
            // serving Single Page Apps via a separate web server instead of
            // via Java.

            console.warn(
                "WARNING: publicUrl is allowed to be relative because it starts with _CONTEXT_PATH_"
            );
            console.warn(`WARNING: publicUrl = ${JSON.stringify(publicUrl)}`);
            console.warn("WARNING: This project must be served from Java by ecco-offline");
            console.warn("WARNING: This project cannot be served by a standard web server");
            console.warn(
                "WARNING: When _CONTEXT_PATH appears as a literal string in index.html, " +
                    "it will be replaced by the actual absolute context path when Java serves the corresponding file"
            );
            console.warn("WARNING: See ResourcesWebMvcConfig.java");
            console.warn(
                "WARNING: The variable process.env.PUBLIC_URL will not be valid in JavaScript code"
            );
        } else {
            throw new Error("publicUrl must be an absolute URL or an absolute path");
        }
    }

    return {
        target: "single-page-app",
        ...commonOptions,
        paths: {
            ...commonOptions.paths,
            public: publicPath,
            html: htmlPath
        },
        publicUrl,
        imageInlineSizeLimit,
        clientEnv: {
            PUBLIC_URL: publicUrl,
            ...commonOptions.clientEnv
        },
        serviceWorker,
        devServer
    };
}

function urlIsAbsolute(url: string): boolean {
    try {
        new URL(url);
    } catch (e) {
        if (hasProperty(e, "code") && e.code === "ERR_INVALID_URL") {
            return false;
        } else {
            throw e;
        }
    }
    return true;
}
