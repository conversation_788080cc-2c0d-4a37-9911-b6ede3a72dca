import {promises} from "node:fs";
import {parse} from "dotenv";
import {hasProperty} from "unknown";

export interface ClientEnvOptions {
    mode: "production" | "development" | "test";
    paths: {
        dotEnv: string;
    };
}

export async function readDotEnv(options: ClientEnvOptions): Promise<Record<string, string>> {
    const paths = [
        options.paths.dotEnv,

        // Don't include `.env.local` for `test` mode since normally you expect
        // tests to produce the same results for everyone.
        ...(options.mode === "test" ? [] : [`${options.paths.dotEnv}.local`]),

        `${options.paths.dotEnv}.${options.mode}`,

        // If the user really does want local settings for tests, they can
        // still put them in `.env.test.local`.
        `${options.paths.dotEnv}.${options.mode}.local`
    ];

    return paths.reduce(
        async (env: Promise<Record<string, string>>, path) =>
            env.then(env =>
                promises.readFile(path, "utf-8").then(
                    text => ({...env, ...parse(text)}),
                    (reason: unknown) => {
                        if (hasProperty(reason, "code") && reason.code === "ENOENT") {
                            return env;
                        } else {
                            throw reason;
                        }
                    }
                )
            ),
        Promise.resolve({})
    );
}
