import {RuleSetRule} from "webpack";
import {ResolvedOptions} from "../../options/options";

export function fileLoader(options: ResolvedOptions): RuleSetRule {
    return {
        exclude: [/\.[cm]?[tj]sx?$/, /\.html$/, /\.json$/],
        type: "asset",
        parser: {
            dataUrlCondition: {
                maxSize:
                    options.target === "single-page-app" ? options.imageInlineSizeLimit : Infinity
            }
        }
    };
}
