import {GenerateSW, InjectManifest} from "workbox-webpack-plugin";
import {ResolvedSinglePageAppOptions} from "../../options/single-page-app";

export function serviceWorkerPlugins(options: ResolvedSinglePageAppOptions) {
    if (options.serviceWorker === "none") {
        return [];
    } else {
        const pluginOptions = {
            maximumFileSizeToCacheInBytes: options.serviceWorker.maximumFileSizeToCacheInBytes,
            swDest: "service-worker.js"
        };

        return options.serviceWorker.implementation === "auto"
            ? [new GenerateSW(pluginOptions)]
            : [
                  new InjectManifest({
                      ...pluginOptions,
                      swSrc: `${options.serviceWorker.implementation.src}/index`
                  })
              ];
    }
}
