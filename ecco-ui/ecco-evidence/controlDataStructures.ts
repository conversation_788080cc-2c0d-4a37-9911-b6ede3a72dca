import {Uuid} from "@eccosolutions/ecco-crypto";
import {Change, SmartStepStatus, ActivityType, SupportAction, GoalUpdateCommandDto, TaskSettingName} from "ecco-dto";
import {EvidenceContext} from "./EvidenceContext";
import {bus, EccoDateTime} from "@eccosolutions/ecco-common";
import {GoalUpdateCommand} from "ecco-commands";


function to<T>(change: Change<T> | undefined): T | null {
    return change ? change.to : null;
}

export interface ActionInstanceControlData {
    asCreatingFromEmpty: boolean | null;

    activityState: number[];

    status: SmartStepStatus | null;
    expiryDate: string | null;
    targetDate: string | null; // stick with targetDate (not time) for now
    targetSchedule: string | null;
    goalName: string | null;
    goalPlan: string | null;
    score: number | null;
    statusChangeReasonId: number | null;
    likelihood: number | null;
    severity: number | null;
    trigger: string | null;
    control: string | null;
    actionInstanceUuid: Uuid | null;
    parentActionInstanceUuid: Uuid | null;
    hierarchy: number | null;
    position: string | null;
    lastUpdate?: EccoDateTime | null;
    statusChangeComment?: string | null;
}

/**
 * Store the initial data for the ActionDef control (snapshot based)
 * that we want to star
 */
export class ActionInstanceControlDataSetup {
    protected constructor() {}

    /**
     * Determines if a support action is empty.
     */
    public static isEmpty(data: SupportAction, userDataOnly = true) {
        // see https://stackoverflow.com/a/27709663
        // also 'Object.keys(obj).length'
        /*export function objectHasData(obj: SupportAction) {
            for (let key in obj) {
                // @ts-ignore
                if (obj[key] !== null && obj[key] != "") return false;
            }
            return true;
        }*/

        if (!data) {
            return true;
        }
        const notNullUndefined = (v: any) => v !== null && v !== undefined;

        let empty = true;

        if (data.id && !userDataOnly) {
            empty = false;
        }
        if (data.workId && !userDataOnly) {
            empty = false;
        }
        if (data.actionInstanceUuid && !userDataOnly) {
            empty = false;
        }
        if (data.parentActionInstanceUuid && !userDataOnly) {
            empty = false;
        }
        if (data.actionId && !userDataOnly) {
            empty = false;
        }
        if (data.actionGroupId && !userDataOnly) {
            empty = false;
        }
        if (data.outcomeId && !userDataOnly) {
            empty = false;
        }

        if (notNullUndefined(data.hierarchy) && !userDataOnly) {
            empty = false;
        }
        if (notNullUndefined(data.position && !userDataOnly)) {
            empty = false;
        }
        if (notNullUndefined(data.lastUpdate) && !userDataOnly) {
            empty = false;
        }
        if (notNullUndefined(data.status) && !userDataOnly) {
            empty = false;
        }
        if (notNullUndefined(data.statusChange) && !userDataOnly) {
            empty = false;
        }

        if (data.workDate) {
            empty = false;
        }
        if (data.expiryDate) {
            empty = false;
        }
        if (data.targetDateTime) {
            empty = false;
        }
        if (data.targetSchedule) {
            empty = false;
        }
        if (data.name) {
            empty = false;
        }
        if (data.goalName) {
            empty = false;
        }
        if (data.goalPlan) {
            empty = false;
        }
        if (notNullUndefined(data.score)) {
            empty = false;
        }
        if (notNullUndefined(data.statusChangeReasonId)) {
            empty = false;
        }
        if (notNullUndefined(data.likelihood)) {
            empty = false;
        }
        if (notNullUndefined(data.severity)) {
            empty = false;
        }
        if (data.intervention) {
            empty = false;
        }
        if (data.statusChangeComment) {
            empty = false;
        }
        return empty;
    }

    /**
     * @param asCreatingFromEmpty if we are in the processing of creating a new control without any actionDefId etc - see GoalsControl
     * @param activityInterest
     */
    public static fromEmpty(
        asCreatingFromEmpty: boolean,
        activityInterest: ActivityType[] | null
    ): ActionInstanceControlData {
        const data: ActionInstanceControlData = {} as ActionInstanceControlData;
        data.asCreatingFromEmpty = asCreatingFromEmpty;
        if (activityInterest) {
            data.activityState = activityInterest.map(actInt => actInt.id);
        }
        return data;
    }

    public static fromSnapshot(
        supportAction: SupportAction,
        asCreatingFromEmpty: boolean,
        //public currentStatusChangeReasonId: number,
        activityInterest: ActivityType[]
    ) {
        const data = this.fromEmpty(asCreatingFromEmpty, activityInterest);
        if (supportAction) {
            data.status = supportAction.status;
            data.expiryDate = supportAction.expiryDate;
            data.targetDate =
                EccoDateTime.parseIso8601Utc(supportAction.targetDateTime)
                    ?.toEccoDate()
                    .formatIso8601() || null;
            data.targetSchedule =
                supportAction.targetSchedule == undefined ? null : supportAction.targetSchedule;
            data.goalName = supportAction.goalName;
            data.goalPlan = supportAction.goalPlan;
            data.score = supportAction.score;
            data.statusChangeReasonId = supportAction.statusChangeReasonId;
            data.actionInstanceUuid = supportAction.actionInstanceUuid
                ? Uuid.parse(supportAction.actionInstanceUuid)
                : null;
            data.parentActionInstanceUuid = supportAction.parentActionInstanceUuid
                ? Uuid.parse(supportAction.parentActionInstanceUuid)
                : null;
            data.hierarchy = supportAction.hierarchy;
            data.position = supportAction.position;
            data.likelihood =
                supportAction.likelihood == undefined ? null : supportAction.likelihood;
            data.severity = supportAction.severity == undefined ? null : supportAction.severity;
            data.trigger = supportAction.hazard ? supportAction.hazard : null;
            data.control = supportAction.intervention ? supportAction.intervention : null;
            data.lastUpdate = supportAction.lastUpdate;
            data.statusChangeComment = supportAction.statusChangeComment;
        }
        return data;
    }

    public static fromCommand(
        dto: GoalUpdateCommandDto,
        asCreatingFromEmpty: boolean,
        activityInterest: ActivityType[]
    ) {
        const data = this.fromEmpty(asCreatingFromEmpty, activityInterest);
        if (dto) {
            data.status = to(dto.statusChange);
            data.expiryDate = to(dto.expiryDateChange);
            data.targetDate = to(dto.targetDateChange);
            data.targetSchedule = to(dto.targetScheduleChange);
            data.goalName = to(dto.goalNameChange);
            data.goalPlan = to(dto.goalPlanChange);
            data.score = to(dto.scoreChange);
            data.statusChangeReasonId = to(dto.statusChangeReason);
            data.actionInstanceUuid = Uuid.parse(dto.actionInstanceUuid);
            data.parentActionInstanceUuid = Uuid.parse(dto.parentActionInstanceUuid);
            data.hierarchy = to(dto.hierarchyChange);
            data.position = to(dto.positionChange);
            data.likelihood = to(dto.likelihoodChange);
            data.severity = to(dto.severityChange);
            data.trigger = to(dto.triggerChange);
            data.control = to(dto.controlChange);
            // as per OutcomesControl.mergeSupportSnapshotAndCommand
            // data.lastUpdate = EccoDateTime.parseIso8601Utc(dto.timestamp);
            // let statusChangeAnnotation = dto.annotationChange && dto.annotationChange["statusChangeComment"];
            // if (dto.statusChangeReason || statusChangeAnnotation) {
            //     data.statusChangeComment = statusChangeAnnotation && statusChangeAnnotation.to || "(no comment)";
            // }
        }
        return data;
    }
}


export class ActionInstanceFeatures {
    private allowFromBlankUi = false;
    private title = true;
    private settingName: TaskSettingName;
    private validateSettingName: TaskSettingName;
    private forceSettingName: TaskSettingName;
    private clearSettingName: TaskSettingName;

    constructor(private context: EvidenceContext, private hierarchy: number) {
        this.settingName = this.getSettingNameForHierarchy("showActionComponents", hierarchy);
        this.validateSettingName = this.getSettingNameForHierarchy(
            "validateActionComponents",
            hierarchy
        );
        this.forceSettingName = this.getSettingNameForHierarchy("forceActionComponents", hierarchy);
        this.clearSettingName = this.getSettingNameForHierarchy("clearActionComponents", hierarchy);
    }

    private getSettingNameForHierarchy(settingName: string, hierarchy: number) {
        if (hierarchy > 0) {
            this.title = false;
            return (settingName + "SubActions" + hierarchy) as TaskSettingName;
        } else {
            return settingName as TaskSettingName;
        }
    }

    public setAllowFromBlankUi(allowFromBlankUi: boolean) {
        this.allowFromBlankUi = allowFromBlankUi;
        return this;
    }

    /** Mainly around GoalsControl and having actions created in situ or not */
    public isAllowFromBlankUi() {
        return this.allowFromBlankUi;
    }

    public showTitle() {
        return this.title;
    }

    public showStatus(outcomeId: number) {
        return (
            this.context.features.isEnabled("support.showActionComponents.status") ||
            this.context.configResolver
                .getServiceType()
                .taskDefinitionSettingHasFlag(
                    this.context.evidenceDef.getTaskName(),
                    this.settingName,
                    "status",
                    outcomeId
                )
        );
    }

    public showLink(outcomeId: number) {
        return (
            this.context.features.isEnabled("support.showActionComponents.link") ||
            this.context.configResolver
                .getServiceType()
                .taskDefinitionSettingHasFlag(
                    this.context.evidenceDef.getTaskName(),
                    this.settingName,
                    "link",
                    outcomeId
                )
        );
    }

    public showExpiry(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "expiry",
                outcomeId
            );
    }

    public showTarget(outcomeId: number) {
        return (
            this.context.features.isEnabled("support.showActionComponents.target") ||
            this.context.configResolver
                .getServiceType()
                .taskDefinitionSettingHasFlag(
                    this.context.evidenceDef.getTaskName(),
                    this.settingName,
                    "target",
                    outcomeId
                )
        );
    }

    public showTargetSchedule(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "targetSchedule",
                outcomeId
            );
    }

    public showGoalName(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "name",
                outcomeId
            );
    }

    public showGoalPlan(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "comment",
                outcomeId
            );
    }

    public showScore(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "score",
                outcomeId
            );
    }

    public showActivityInterest(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "activityInterest",
                outcomeId
            );
    }

    public showStatusChangeReason(outcomeId: number) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.settingName,
                "statusChangeReason",
                outcomeId
            );
    }

    /**
     * See if any validation is required.
     * Only required fields are supported in this single list config style (ie not numbers > 10, for instance).
     * @param outcomeId
     * @param property Only 'score' is supported currently
     */
    public validateActionComponents(outcomeId: number, property: string) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.validateSettingName,
                property,
                outcomeId
            );
    }

    /**
     * Force action components to show
     */
    public forceActionComponents(outcomeId: number, property: string) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.forceSettingName,
                property,
                outcomeId
            );
    }

    public clearActionComponents(outcomeId: number, property: string) {
        return this.context.configResolver
            .getServiceType()
            .taskDefinitionSettingHasFlag(
                this.context.evidenceDef.getTaskName(),
                this.clearSettingName,
                property,
                outcomeId
            );
    }
}


export class ActionInstanceParentFeatures {
    private needsBased = false;
    private showUnachievedOnly = false;
    private showAsPlaceholder = false;
    private showSubsteps = false;
    private showNewActions = false;
    private showTitle = false;

    constructor() {}

    public setNeedsBased(needsBased: boolean) {
        this.needsBased = needsBased;
        return this;
    }
    public setShowUnachievedOnly(showUnachievedOnly: boolean) {
        this.showUnachievedOnly = showUnachievedOnly;
        return this;
    }
    public setShowSubsteps(showSubsteps: boolean) {
        this.showSubsteps = showSubsteps;
        return this;
    }
    public setShowNewActions(showNewActions: boolean) {
        this.showNewActions = showNewActions;
        // by default, if multiple-smart-steps then we hide achieved
        //this.actionDefFeatures.setNewActionsHideAchieved(showNewActions);
        return this;
    }


    /**
     * When no data is presented, see if we should show a control
     * as a placeholder for the user to click and activate
     */
    public setShowAsPlaceholder(showAsPlaceholder: boolean) {
        this.showAsPlaceholder = showAsPlaceholder;
        return this;
    }
    public setShowTitle(showTitle: boolean) {
        this.showTitle = showTitle;
        return this;
    }
    public getActionDefFeatures(context: EvidenceContext, hierarchy: number) {
        return new ActionInstanceFeatures(context, hierarchy);
    }
    public isNeedsBased() {
        return this.needsBased;
    }
    public isShowUnachievedOnly() {
        return this.showUnachievedOnly;
    }
    public isShowAsPlaceholder() {
        return this.showAsPlaceholder;
    }
    public isShowSubsteps() {
        return this.showSubsteps;
    }
    public isShowNewActions() {
        return this.showNewActions;
    }
    public isShowTitle() {
        return this.showTitle;
    }
}


/**
 * A framework agnostic store.  Whenever we update the store, we fire an event on the bus.
 *
 * For React, a root component should do instanceState.bus.addHandler(() => this.forceUpdate()) and from
 * there the render diffing algorithm should be able to cope so long as we've updated what has changed using an
 * immutable pattern (i.e. if a prop is a composite, the entire composite should be replaced using immutable)
 */
class ScheduleInstanceState {
    /** Bus to notify that this object has changed */
    private readonly _bus = bus<void>();
    private _targetDate: string | null;
    private _targetSchedule: string | null;

    constructor(protected readonly origTargetDate: string | null = null, protected origTargetSchedule: string | null = null) {
        this._targetDate = origTargetDate;
        this._targetSchedule = origTargetSchedule;
    }

    get bus() { return this._bus};
    get targetDate(): string | null {return this._targetDate;}
    get targetSchedule(): string | null {return this._targetSchedule;}

    set targetDate(value: string | null) {
        this._targetDate = value;
        this._bus.fire();
    }

    set targetSchedule(value: string | null) {
        this._targetSchedule = value;
        this._bus.fire();
    }

}

export class ActionInstanceState extends ScheduleInstanceState {
    populateGoalUpdateCommand(cmd: GoalUpdateCommand) {
        cmd.changeTargetDate(this.origTargetDate, this.targetDate);
        cmd.changeTargetSchedule(this.origTargetSchedule, this.targetSchedule);
    }
}
