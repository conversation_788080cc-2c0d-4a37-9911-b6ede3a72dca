import {
    Checkbox,
    FormControlLabel,
    FormGroup,
    Grid,
    Radio,
    RadioGroup,
    Typography
} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {textInput, textArea, SelectList} from "ecco-components-core";
import {FC} from "react";
import {QuestionAnswerProps} from "./QuestionAnswerRoot";
import {Markdown} from "ecco-forms";
import {EccoDate} from "@eccosolutions/ecco-common";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {ListDefinitionEntry, QuestionAnswerFreeType, SessionData} from "ecco-dto";


const QuestionDefName = (props: QuestionAnswerProps) => (
    <Typography>{props.init.questionDef.name}</Typography>
);

const QuestionAnswerCheckbox = (props: QuestionAnswerProps) => {
    const {state, stateSetter} = props;

    const value = state.answer ? state.answer.toLowerCase() == "true" : false;
    return (
        <FormGroup>
            <FormControlLabel
                control={<Checkbox />}
                label={props.init.questionDef.name}
                checked={value}
                onChange={(_, checked) => stateSetter({answer: checked.toString()})}
            />
        </FormGroup>
    );
};

const QuestionAnswerRadios = (props: QuestionAnswerProps) => {
    const {state, stateSetter} = props;

    return (
        <RadioGroup
            name={`${props.init.questionDef.id}-radiogroup`}
            value={state.answer?.toString() || null} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
            onChange={(_, value) => stateSetter({answer: value})}
            style={{paddingLeft: "60px", paddingTop: "20px"}}
        >
            {props.init.questionDef.choices
                .filter(c => !c.disabled || c.value == state.answer)
                .map(c => (
                    <FormControlLabel
                        key={c.id}
                        value={c.value.toString()}
                        control={<Radio />}
                        label={c.displayValue}
                    />
                ))}
        </RadioGroup>
    );
};

const QuestionAnswerList = (props: QuestionAnswerProps) => {
    const listName = props.init.questionDef.parameters.listName;
    const initialAnswerId =
        props.init.initState.answer != null ? parseInt(props.init.initState.answer) : null;
    const options = listName
        ? props.init.initData.sessionData.getListDefinitionEntriesByListName(
              listName,
              undefined,
              initialAnswerId || true
          )
        : [];
    return (
        <SelectList
            key={`${props.init.questionDef.id}-select`}
            placeholder={"-"}
            createNew={false}
            getOptionLabel={l => l.getDisplayName()}
            getOptionValue={l => l.getId().toString()}
            value={options.filter(ld => ld.getId().toString() == props.state.answer)}
            options={options}
            onChange={value =>
                props.stateSetter({answer: (value as ListDefinitionEntry).getId().toString()})
            }
        />
    );
};

const QuestionAnswerDate = (props: QuestionAnswerProps) => {
    const {state, stateSetter} = props;

    const value = state.answer ? EccoDate.parseIso8601(state.answer) : null;
    return (
        <DatePickerEccoDate
            name={`${props.init.questionDef.id}-date`}
            label="date"
            onChange={date => stateSetter({answer: date?.formatIso8601()})}
            value={value}
            required={props.init.questionDef.answerRequired}
        />
    );
};

const QuestionAnswerText = (props: QuestionAnswerProps) => {
    const {state, stateSetter} = props;
    // ?? limit 384 chars
    return (
        <>
            {textInput(
                "answer",
                undefined,
                stateSetter,
                state,
                undefined,
                undefined,
                props.init.questionDef.answerRequired
            )}
        </>
    );
};

const QuestionAnswerTextArea = (props: QuestionAnswerProps) => {
    const {state, stateSetter} = props;
    // ?? limit 384 chars
    return (
        <>
            {textArea(
                "answer",
                undefined,
                stateSetter,
                state,
                undefined,
                false,
                props.init.questionDef.answerRequired
            )}
        </>
    );
};

const QuestionAnswerField = (props: QuestionAnswerProps) => {
    const qn = props.init.initData.sessionData.getQuestionById(props.state.questionDefId);
    const type = SessionData.questionType(qn, false);

    switch (type) {
        case "markdown":
            return <Markdown>{qn.name}</Markdown>;
        case "choices":
            return <QuestionAnswerRadios {...props} />;
        case "integer":
        case "money":
        case "number":
        case "text":
            return <QuestionAnswerText {...props} />;
        case "textarea":
            return <QuestionAnswerTextArea {...props} />;
        case "date":
            return <QuestionAnswerDate {...props} />;
        case "checkbox":
            return <QuestionAnswerCheckbox {...props} />;
        case "list":
            return <QuestionAnswerList {...props} />;
        default:
            return null;
    }
};


const QuestionAnswerLayout = (props: QuestionAnswerProps) => {
    // see QuestionControl.ts createInputControl
    const qn = props.init.questionDef;
    let type: QuestionAnswerFreeType | "choices" = "text";
    if (qn.choices && qn.choices.length > 0) {
        type = "choices";
    }
    if (qn.freeTypes && qn.freeTypes.length > 0) {
        // get the freetype - which can only be one entry!
        type = qn.freeTypes[0].valueType;
    }

    const LayoutFactory = (type: QuestionAnswerFreeType | "choices") => {
        switch (type) {
            case "markdown":
                return <Markdown>{props.init.questionDef.name}</Markdown>;
            case "checkbox":
                return (
                    <QuestionAnswerField
                        init={props.init}
                        state={props.state}
                        stateSetter={props.stateSetter}
                    />
                );
            default:
                return (
                    <>
                        <QuestionDefName
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                        <QuestionAnswerField
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                    </>
                );
        }
    };
    const Layout = LayoutFactory(type);

    return (
        <Grid container direction="row" justify="flex-start" alignItems="flex-start">
            <Grid item sm={2}>
                &nbsp;
            </Grid>
            <Grid item sm={8} xs={12}>
                {Layout}
            </Grid>
        </Grid>
    );
};

/**
 * External entry point to the layout
 */
export const QuestionAnswer: FC<QuestionAnswerProps> = props => {
    return (
        <QuestionAnswerLayout
            init={props.init}
            state={props.state}
            stateSetter={props.stateSetter}
        />
    );
};
QuestionAnswer.whyDidYouRender = true;