import {mount} from "cypress/react";
import * as React from "react";
import {sessionData, testBaseData, testSmartStepInit} from "../../test-support/mockEvidence";
import {EccoAPI} from "ecco-components/EccoAPI";
import {FC, useState} from "react";
import {Paper} from "@eccosolutions/ecco-mui";
import {SmartStepInit, SmartStepState} from "../../smartsteps/SmartStepRoot";
import {SmartStep} from "../../smartsteps/SmartStep";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {CommandForm} from "ecco-components";
import {Command} from "ecco-commands";
import {EvidencePageSetupForCommandForm} from "../../EvidencePage";
import {CommandFormTest} from "ecco-components/cmd-queue/testUtils";
import {EvidencePageData} from "../../EvidencePageRoot";
import {SupportAction} from "ecco-dto";

const SmartStepTestLayout: FC<{init: SmartStepInit}> = (props: {init: SmartStepInit}) => {
    // create/hold the props
    const init = props.init;
    const [state, setState] = useState<SmartStepState>(init.initState);
    const stateSetter = (update: Partial<SmartStepState>) => {
        setState({...state, ...update});
    };

    return (
        <>
            <SmartStep init={init} state={state} stateSetter={stateSetter} remove={() => {}} />
            <Paper elevation={2}>
                <p>state: {JSON.stringify(state)}</p>
            </Paper>
        </>
    );
};

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("SmartStep tests", () => {
    it("smartstep", () => {
        // ideally this test data should be refactored into the 'parent' pageData
        // because although we want to test a SmartStep independently (EvidencePageSmartStep tests in context)
        // a SmartStep now has a SubStep that uses the context, so the test data needs to be in the context
        const init: SmartStepInit = testSmartStepInit();

        // PAGE DATA - because our SubSteps now get the evidence directly from the context we
        // include the smart step data in the page context because this is expected in SubSteps -
        // we do it now to fix 'parent' in SubStepWrapper work as it requires the control to exist
        // we convert our direct testing into persisted support actions in the context
        const {actionInstanceUuid, parentActionInstanceUuid, ...rest} = init.initState;
        const supportAction: SupportAction = {
            actionId: init.initState.actionDefId,
            id: 0,
            name: "",
            statusChange: false,
            targetDateTime: "",
            workDate: "",
            workId: "",
            actionInstanceUuid: init.initState.controlUuid.toString(),
            parentActionInstanceUuid: init.initState.parentActionInstanceUuid?.toString(),
            ...rest
        };
        const pageData: EvidencePageData = {
            ...testBaseData(
                init.initData.evidenceDef.getTaskName(),
                init.initData.evidenceDef.getEvidencePageType()
            ),
            supportActions: [supportAction]
        };

        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/*draft save DEV-2650*/}
                            {/* EvidencePageRoot - directly, when no commandForm required */}
                            {/* EvidencePageLoaderForCommandForm - when loading data */}
                            <EvidencePageSetupForCommandForm initData={pageData}>
                                <SmartStepTestLayout init={init} />
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        //cy.findByLabelText("target").type("16/05/2022");
        //cy.contains('"targetDate":"2022-05-16"');
    });
});
