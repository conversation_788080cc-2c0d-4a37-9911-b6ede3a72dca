{
  "compilerOptions": {
    "allowSyntheticDefaultImports": false,
    "composite": true,
    "declarationMap": true, // so we can link through to .ts instead of the .d.ts
    "target": "ES6",
    "module": "commonjs",
    "lib": ["ES2017", "DOM", "DOM.Iterable"],
    "skipDefaultLibCheck": true,
    "skipLibCheck": true,
    "noEmit": false,
    "strict": true,
    "downlevelIteration": true,
    "forceConsistentCasingInFileNames": true,
    "jsx": "react",
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "types": [],
    "paths": {
      // relative to baseUrl for actual modules
      "ecco-admin": ["../ecco-admin/index.ts"],
      "ecco-calendar": ["../ecco-calendar/index.ts"],
      "ecco-commands": ["../ecco-commands/index.ts"],
      "ecco-components-core": ["../ecco-components-core/index.ts"],
      "ecco-components": ["../ecco-components/index.ts"],
      "ecco-dto": ["../ecco-dto/index.ts"],
      "ecco-evidence": ["../ecco-evidence/index.ts"],
      "ecco-finance": ["../ecco-finance/index.ts"],
      "ecco-forms": ["../ecco-forms/index.ts"],
      "ecco-incidents": ["../ecco-incidents/index.ts"],
      "ecco-managedvoids": ["../ecco-managedvoids/index.ts"],
      "ecco-math": ["../ecco-math/index.ts"],
      "ecco-mui": ["../ecco-mui/index.ts"],
      "ecco-offline-data": ["../ecco-ui/ecco-offline-data/index.ts"],
      "ecco-repairs": ["../ecco-repairs/index.ts"],
      "ecco-rota": ["../ecco-ui/ecco-rota/index.ts"],
      "ecco-reports": ["../ecco-reports/index.ts"]
    },
    "removeComments": true,
    "sourceMap": true,
    "stripInternal": true
  }
}
