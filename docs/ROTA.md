
ROTA - implementation
---------------------
- see ECCO-SNIPPETS for ROTA - config
- see JIRAs and google docs also
- eg https://docs.google.com/document/d/1W8bZnoLoLcl6MDyYbxN4e_jQ0OOQ46GABm1Ba9UdnNs/edit

- sr/client

      AgreementOfAppointments - relates to srId, overall charge, has AppointmentSchedules
        AppointmentSchedule - DaysOfWeek & frequency, app time, AppointmentType, DemandParameters (skills), charge, FinanceBand (list)
            - also has entryHandleAsString (column calendar_entry_handle) which is an IcalUid which all its events live off
                which is created from the recurring entry against the client's calendar
          - can't be edited once created as calendar would need updating
        AppointmentType simple list associated with a Service (eg 'one-to-one'), with suggestedDuration

- rota scheduler

		(see clarifying commit 206de9a1)
		RotaController /rota/demandResource/view/ - for showing the rota
			RotaServiceImpl.fetchRota

				BuildingWorkerRotaHandler.populateRota
					resource - find all workers in the building
					addWorkerEntries - as DefaultRotaHandler
					addDemandWithStatuses - as DefaultRotaHandler
						srIds include clients.residence.eq(buildingId), buildingServiceRecipientId
						** therefore this includes referrals residing in the building (despite the name BuildingWorkerRotaHandler)

				DefaultRotaHandler.populateRota
					findAllWorkersWithUserWhoAreEmployedAt
					addWorkerEntries - add lines for each worker (allocated=confirmed recurrences of type 'demand schedule'; and VAVAILABLE appts; could also do all day events - eg hol/sickness)
						addConfirmedRecurrences - loads CONFIRMED entries, then gets data if entity instanceof DemandSchedule
							CalendarService.findRecurrences
								** finds recurring entries only (eg no review dates), returns expanded entries only - setExpandRecurringEvents(true)
								** any single events (NoteOccurence) which are not modifications are converted using its recurring entry
							filters to only allocated appointments which are CONFIRMED occurrences (for both attendees worker & client)
							for each, add the DemandSchedule (got from the recurringEntryHandle) with the allcoated appt
						addAvailability
					addDemandWithStatuses - find unfilfilled demand to display on the bottom rows of the rota
						loads DemandSchedules in range, and gets its handle (icalUid) events
							?? why do this - and not just from the calendar?
						filters TENTATIVE
							ad-hoc creates tentative, but often allocated instantly to a worker, and so confirmed
							demand schedule creates tentative
						filters DROPPED - cancelled, because they were tentative at some point
							just a translation term to mean exceptions to the recurrence - not official term

		RotaController /rota/demandResource/serviceRecipients/ - for reporting on 'workers:all' BUT ends with UnsupportedOperationException
			(reporting is now done from evidence working back to the event)
			RotaServiceImpl.fetchServiceRecipients
				DefaultRotaHandler.findAllAgreementsByDate

		RotaController /agreements/ - for showing the agreeements in an ad-hoc form

		RotaController /allocate /deallocate /split /drop /reinstate/ - for doing stuff

		ad-hoc - creates a new schedule with a one-time recurring entry (and therefore new icalUuid handle) for ad-hoc appt in the AgreementOfAppointments

        RotaControl.ts
            gets the Rota (domain)
            which converts the workers [RotaResourceDto from RotaResourceViewModel through populateWorkerEntry]
            unallocated are !dropped and no worker assigned
            cancalled are dropped
