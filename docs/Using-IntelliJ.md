Install, then 'Customize Technologies'.

Add a launcher
--------------
- See https://gist.github.com/rob-murray/6828864

Useful info
-----------
- At the first instance, click the maven tab, then the execute maven goal 
  button, then run the command 'mvn test-compile'. This will compile 
  sources, tests and will ensure any generated code is generated as well.

- For reloading configurations you can right click on a module (or the aggregator) and select
    Maven -> Reimport

- When liquibase Q files don't update, liquibase uses a separate plug-in for APT, so need to right click project and Maven -> Generate Sources and Update Folders

- Check default Font size

- Themes (file -> preferences -> plugins -> browse 'material theme' -> install)

- HTML entities: Convert to html entities with Cmd+Shift+A and type "Encode" then select the action "Encode XML/HTML Special Characters" (although the plugin 'String Manipulation' is good)


Tomcat
--------------
- If you want to use an existing tcServer installation because you also have STS installed, then you can install
  the tcServer plugin and configure it with:

  -Denv=dev -Ddb.schema=ecco -Ddb.extraContexts=central-proc-servicetype -Dliquibase=CREATE -Duser.timezone=UTC 
  -Xmx568m -Xss256k -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true

HOWEVER.. I couldn't get it to work.

For Tomcat, I found downloading Tomcat 7 tar.gz, unzipping it and then pointing IntelliJ's Tomcat configuration at
it worked fine. Edit the default, then create instances. Settings:
		'update class and resource on frame deactivation' and 'update action'
		'retain sessions between restarts'

Typescript compiler needs some config
--------
- File -> Settings -> Languages & Frameworks -> Typescript
- Set Enable compiler; Use tsconfig.json and set Scope = VCS -> Default

Quirks I've not resolved but can work around
--------
- IntelliJ creates a */bin/ dirs which seem to be a staging ground for .class and other files before they
  are written to */target/..
  These are getting included on the source path.  To fix, right click, "Open module settings (F12)", select each module
  then the bin folder, in turn, and click "Excluded" (it'll turn from orange to red).

Running JUnit tests with our defaults:
--------
- At http://stackoverflow.com/questions/32484449/how-to-define-default-jvm-arguments-in-idea, it points out that
  there are no default JRE options across the board, and you have to set them per configuration type.
  So, go to Run -> Edit Configurations.. and expand the "Defaults" node at the bottom of the categorised list,
  and you can then enter your VM arguments.
  THEN... delete existing JUnit run configurations so the new ones pick up this config.

Using Path Variables in tests
-----
If you want to easily switch many configurations between say CHROME and FIREFOX for the browser
then you can set up path variables in Settings -> Path Variables, and then use e.g. $BROWSER$ in the run 
configuration so that this gets picked up for all existing configurations.

iBus problem on Ubuntu 14.04
-------
The following needs to be added to either idea.sh or ~/.profile

    # see https://youtrack.jetbrains.com/issue/IDEA-78860
    export IBUS_ENABLE_SYNC_MODE=1

Key mappings
    Settings -> Key map

- Might want to remove Alt-R as this gets in the way of getting at the Refactor menu
- Might want to re-assign Ctrl-W to what is currently Ctrl-F4 and remove the old Ctrl-W mapping
