name: vulnerability-check
on:
#  push:
  schedule:
    - cron: "00 20 * * THU"
  workflow_dispatch:
      inputs:
          MAVEN_ARGS:
              description: 'Maven args'
              required: false
              default: '-Dtest=x'
env:
  NIST_NVD_API_KEY: ${secrets.NIST_NVD_API_KEY}
jobs:
  check:
    name: ${{needs.parameters.outputs.build-and-test-name}}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 50
      - name: Set up OpenJDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 17
          cache: 'gradle'
      - name: Cache dependencyCheck data
        uses: actions/cache@v4
        with:
            path: ~/.gradle/caches
            key: ${{ runner.os }}-dependencyCheck-${{ hashFiles('**/*.gradle') }}
            restore-keys: ${{ runner.os }}-dependencyCheck-
      - name: Validate Grad<PERSON>
        uses: gradle/wrapper-validation-action@v3
      - name: Configure access token for GitHub Packages
        run: echo "//npm.pkg.github.com/:_authToken=${{secrets.PERSONAL_ACCESS_READ}}" > ~/.npmrc
      - name: vulnerability check
        timeout-minutes: 20
        run: |
            ./gradlew dependencyCheckAnalyze -Dorg.gradle.parallel=false
      - name: Upload ecco-webapi-boot Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
            name: ecco-int-oh-dependency-check-report.html
            path: ./ecco-int-oh/build/reports/dependency-check-report.html
            if-no-files-found: error
            retention-days: 31
      - name: Upload ecco-webapi-boot Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
            name: ecco-webapi-boot-dependency-check-report.html
            path: ./ecco-webapi-boot/build/reports/dependency-check-report.html
            if-no-files-found: error
            retention-days: 31
      - name: Report build failure on Slack
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          author_name: ${{needs.parameters.outputs.build-and-test-name}}
          job_name: ${{needs.parameters.outputs.build-and-test-name}}
          status: ${{job.status}}
          fields: repo,job,ref,commit,message,author,took
        env:
          SLACK_WEBHOOK_URL: ${{secrets.SLACK_WEBHOOK_URL}}
          MATRIX_CONTEXT: ${{toJson(matrix)}}
